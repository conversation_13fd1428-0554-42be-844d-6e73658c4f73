/**
 * Quick Fill Button Manager
 *
 * A reusable component for creating and managing quick fill buttons
 * across different systems in the Force Guides Build Planner.
 *
 * This component handles:
 * - Creating quick fill buttons with consistent styling
 * - Supporting both single buttons and button groups (quick fill + reset)
 * - Positioning buttons consistently across systems
 * - Event handling for button clicks
 * - Confirmation dialogs for destructive actions
 */

class QuickFillButtonManager {
    /**
     * Create a new QuickFillButtonManager
     * @param {Object} options - Configuration options
     * @param {string|HTMLElement} options.container - Container element or selector
     * @param {Array} options.buttons - Array of button configurations
     * @param {string} options.position - Position type: 'top', 'header', 'custom'
     * @param {string} options.alignment - Button alignment: 'left', 'center', 'right'
     * @param {string} options.className - Additional CSS class for the container
     */
    constructor(options = {}) {
        this.options = Object.assign({
            container: null,
            buttons: [],
            position: 'top',
            alignment: 'left',
            className: ''
        }, options);

        this.isCreated = false;
        this.elements = {};
        this.buttonElements = [];

        // Validate required options
        if (!this.options.container) {
            throw new Error('QuickFillButtonManager: container option is required');
        }

        // Get container element
        this.containerElement = typeof this.options.container === 'string'
            ? document.querySelector(this.options.container)
            : this.options.container;

        if (!this.containerElement) {
            throw new Error('QuickFillButtonManager: container element not found');
        }

        // Create the button container
        this._createButtonContainer();
    }

    /**
     * Create the button container DOM element
     * @private
     */
    _createButtonContainer() {
        if (this.isCreated) return;

        // Create container element
        const containerEl = document.createElement('div');
        containerEl.className = `fg-quick-fill-container fg-quick-fill-${this.options.position} fg-quick-fill-align-${this.options.alignment}`;

        if (this.options.className) {
            containerEl.classList.add(this.options.className);
        }

        // Cache the container
        this.elements.container = containerEl;

        // Add buttons if provided
        if (this.options.buttons && this.options.buttons.length > 0) {
            this._createButtons(this.options.buttons);
        }

        // Insert into the target container based on position
        this._insertContainer();

        this.isCreated = true;
    }

    /**
     * Insert the button container into the target container
     * @private
     */
    _insertContainer() {
    switch (this.options.position) {
        case 'top':
            // Insert after header and tabs, but before main content
            const header = this.containerElement.querySelector('h2');
            const tabs = this.containerElement.querySelector('.fg-overlord-mastery-tabs, .fg-honor-rank-content, .fg-pet-tier-content');
            
            if (tabs) {
                // Insert after tabs
                tabs.insertAdjacentElement('afterend', this.elements.container);
            } else if (header) {
                // Insert after header if no tabs
                header.insertAdjacentElement('afterend', this.elements.container);
            } else {
                // Fallback to beginning
                this.containerElement.insertBefore(this.elements.container, this.containerElement.firstChild);
            }
            break;
        case 'header':
            const headerEl = this.containerElement.querySelector('.fg-system-header, .essence-runes-header, h2');
            if (headerEl) {
                headerEl.appendChild(this.elements.container);
            } else {
                this.containerElement.insertBefore(this.elements.container, this.containerElement.firstChild);
            }
            break;
        case 'custom':
        default:
            this.containerElement.appendChild(this.elements.container);
            break;
    }
}

    /**
     * Create buttons from configuration array
     * @private
     * @param {Array} buttonConfigs - Array of button configuration objects
     */
    _createButtons(buttonConfigs) {
        buttonConfigs.forEach((config, index) => {
            const button = this._createButton(config, index);
            this.elements.container.appendChild(button);
            this.buttonElements.push(button);
        });
    }

    /**
     * Create a single button element
     * @private
     * @param {Object} config - Button configuration
     * @param {number} index - Button index
     * @returns {HTMLElement} - Button element
     */
    _createButton(config, index) {
        // Default button configuration
        const buttonConfig = Object.assign({
            text: 'Quick Fill',
            type: 'primary',
            onClick: () => {},
            tooltip: '',
            confirmMessage: null,
            disabled: false
        }, config);

        // Create button element
        const button = document.createElement('button');
        button.className = `fg-quick-fill-button fg-quick-fill-button-${buttonConfig.type}`;
        button.textContent = buttonConfig.text;
        button.disabled = buttonConfig.disabled;

        // Add tooltip if provided
        if (buttonConfig.tooltip) {
            button.title = buttonConfig.tooltip;
        }

        // Set up click handler
        button.addEventListener('click', () => {
            this._handleButtonClick(buttonConfig, button, index);
        });

        return button;
    }

    /**
     * Handle button click with optional confirmation
     * @private
     * @param {Object} config - Button configuration
     * @param {HTMLElement} button - Button element
     * @param {number} index - Button index
     */
    _handleButtonClick(config, button, index) {
        // Check if confirmation is needed
        if (config.confirmMessage) {
            if (!confirm(config.confirmMessage)) {
                return;
            }
        }

        // Call the onClick handler
        if (typeof config.onClick === 'function') {
            config.onClick(button, index);
        }
    }

    /**
     * Add a new button to the container
     * @param {Object} config - Button configuration
     * @returns {HTMLElement} - Created button element
     */
    addButton(config) {
        if (!this.isCreated) {
            this._createButtonContainer();
        }

        const button = this._createButton(config, this.buttonElements.length);
        this.elements.container.appendChild(button);
        this.buttonElements.push(button);

        return button;
    }

    /**
     * Remove a button by index
     * @param {number} index - Button index to remove
     */
    removeButton(index) {
        if (index >= 0 && index < this.buttonElements.length) {
            const button = this.buttonElements[index];
            button.remove();
            this.buttonElements.splice(index, 1);
        }
    }

    /**
     * Update button configuration
     * @param {number} index - Button index
     * @param {Object} config - New configuration
     */
    updateButton(index, config) {
        if (index >= 0 && index < this.buttonElements.length) {
            const button = this.buttonElements[index];

            if (config.text !== undefined) {
                button.textContent = config.text;
            }

            if (config.disabled !== undefined) {
                button.disabled = config.disabled;
            }

            if (config.tooltip !== undefined) {
                button.title = config.tooltip;
            }

            if (config.type !== undefined) {
                // Remove old type class and add new one
                button.className = button.className.replace(/fg-quick-fill-button-\w+/, `fg-quick-fill-button-${config.type}`);
            }
        }
    }

    /**
     * Enable or disable all buttons
     * @param {boolean} enabled - Whether buttons should be enabled
     */
    setEnabled(enabled) {
        this.buttonElements.forEach(button => {
            button.disabled = !enabled;
        });
    }

    /**
     * Show or hide the button container
     * @param {boolean} visible - Whether container should be visible
     */
    setVisible(visible) {
        if (this.elements.container) {
            this.elements.container.style.display = visible ? '' : 'none';
        }
    }

    /**
     * Destroy the button container and remove it from the DOM
     */
    destroy() {
        if (this.isCreated && this.elements.container) {
            this.elements.container.remove();
            this.isCreated = false;
            this.buttonElements = [];
        }
    }

    /**
     * Get button element by index
     * @param {number} index - Button index
     * @returns {HTMLElement|null} - Button element or null if not found
     */
    getButton(index) {
        return this.buttonElements[index] || null;
    }

    /**
     * Get all button elements
     * @returns {Array<HTMLElement>} - Array of button elements
     */
    getAllButtons() {
        return [...this.buttonElements];
    }
}

// Export the class
window.QuickFillButtonManager = QuickFillButtonManager;

/**
 * Quick Fill Button System Configurations
 * Centralized configuration for all systems
 */
window.QuickFillButtonConfigs = {
    /**
     * Pet System Configuration
     */
    pet: {
        position: 'top',
        alignment: 'left',
        buttons: [
            {
                text: 'Quick Fill',
                tooltip: 'Fill with endgame pet build (Normal: Max Crit Rate, Covenant/Trust: Penetration)',
                onClick: function() {
                    if (window.PetSystem && typeof window.PetSystem.quickFillEndgameSetup === 'function') {
                        window.PetSystem.quickFillEndgameSetup();
                    }
                }
            },
            {
                text: 'Reset All',
                type: 'danger',
                tooltip: 'Remove all equipped pet stats',
                confirmMessage: 'Are you sure you want to remove all pet stats? This cannot be undone.',
                onClick: function() {
                    if (window.PetSystem && typeof window.PetSystem.resetAllStats === 'function') {
                        window.PetSystem.resetAllStats();
                    }
                }
            }
        ]
    },

    /**
     * Stellar System Configuration
     */
    stellar: {
        position: 'top',
        alignment: 'left',
        buttons: [
            {
                text: 'Quick Fill',
                tooltip: 'Fill all nodes with popular PvE build (Penetration & Crit Damage focus)',
                onClick: function() {
                    if (window.StellarSystem && typeof window.StellarSystem.quickFillStellarSystem === 'function') {
                        window.StellarSystem.quickFillStellarSystem();
                    }
                }
            },
            {
                text: 'Reset All',
                type: 'danger',
                tooltip: 'Remove all stellar node stats',
                confirmMessage: 'Are you sure you want to remove all stellar node stats? This cannot be undone.',
                onClick: function() {
                    if (window.StellarSystem && typeof window.StellarSystem.resetAllNodes === 'function') {
                        window.StellarSystem.resetAllNodes();
                    }
                }
            }
        ]
    },

    /**
     * Honor Medal System Configuration
     */
    honor: {
        position: 'top',
        alignment: 'left',
        buttons: [
            {
                text: 'Quick Fill',
                tooltip: 'Fill all ranks with recommended stats (Dex, Crit Damage, All Attack Up, Penetration)',
                onClick: function() {
                    if (window.HonorMedalSystem && typeof window.HonorMedalSystem.quickFill === 'function') {
                        window.HonorMedalSystem.quickFill();
                    }
                }
            },
            {
                text: 'Reset All',
                type: 'danger',
                tooltip: 'Remove all equipped honor medal stats',
                confirmMessage: 'Are you sure you want to remove all honor medal stats? This cannot be undone.',
                onClick: function() {
                    if (window.HonorMedalSystem && typeof window.HonorMedalSystem.resetAllStats === 'function') {
                        window.HonorMedalSystem.resetAllStats();
                    }
                }
            }
        ]
    },

    /**
     * Essence Rune System Configuration
     */
    'essence-runes': {
        position: 'top',
        alignment: 'left',
        buttons: [
            {
                text: 'Quick Fill',
                tooltip: 'Add slots and equip/max all available essence runes',
                onClick: function() {
                    if (window.EssenceRunesSystem && typeof window.EssenceRunesSystem.maxAllRunes === 'function') {
                        window.EssenceRunesSystem.maxAllRunes();
                    }
                }
            },
            {
                text: 'Reset All',
                type: 'danger',
                tooltip: 'Remove all equipped essence runes',
                confirmMessage: 'Are you sure you want to remove all essence runes? This cannot be undone.',
                onClick: function() {
                    if (window.EssenceRunesSystem && typeof window.EssenceRunesSystem.resetAllRunes === 'function') {
                        window.EssenceRunesSystem.resetAllRunes();
                    }
                }
            }
        ]
    },

    /**
     * Karma Rune System Configuration
     */
    'karma-runes': {
        position: 'top',
        alignment: 'left',
        buttons: [
            {
                text: 'Quick Fill',
                tooltip: 'Add slots and equip/max all available karma runes',
                onClick: function() {
                    if (window.KarmaRunesSystem && typeof window.KarmaRunesSystem.maxAllRunes === 'function') {
                        window.KarmaRunesSystem.maxAllRunes();
                    }
                }
            },
            {
                text: 'Reset All',
                type: 'danger',
                tooltip: 'Remove all equipped karma runes',
                confirmMessage: 'Are you sure you want to remove all karma runes? This cannot be undone.',
                onClick: function() {
                    if (window.KarmaRunesSystem && typeof window.KarmaRunesSystem.resetAllRunes === 'function') {
                        window.KarmaRunesSystem.resetAllRunes();
                    }
                }
            }
        ]
    },

    /**
     * Class System Configuration
     */
    'class': {
        position: 'top',
        alignment: 'left',
        buttons: [
            {
                text: 'Reset All',
                type: 'danger',
                tooltip: 'Reset attributes to base values',
                confirmMessage: 'Are you sure you want to reset all attributes to base values? This cannot be undone.',
                onClick: function() {
                    if (window.ClassSystem && typeof window.ClassSystem.resetAttributes === 'function') {
                        window.ClassSystem.resetAttributes();
                    }
                }
            }
        ]
    },

    /**
     * Overlord Mastery System Configuration
     */
    'overlord-mastery': {
        position: 'top',
        alignment: 'left',
        buttons: [
            {
                text: 'Quick Fill',
                tooltip: 'Max all skills with balanced attack and defense build',
                onClick: function() {
                    if (window.OverlordMasterySystem && typeof window.OverlordMasterySystem.quickFillAllSkills === 'function') {
                        window.OverlordMasterySystem.quickFillAllSkills();
                    }
                }
            },
            {
                text: 'Reset All',
                type: 'danger',
                tooltip: 'Reset all overlord mastery skills to level 0',
                confirmMessage: 'Are you sure you want to reset all overlord mastery skills? This cannot be undone.',
                onClick: function() {
                    if (window.OverlordMasterySystem && typeof window.OverlordMasterySystem.resetAllSkills === 'function') {
                        window.OverlordMasterySystem.resetAllSkills();
                    }
                }
            }
        ]
    },

    /**
     * System-specific container selectors
     */
    containerSelectors: {
        'pet': '.fg-pet-system-container',
        'stellar': '.fg-stellar-system-container',
        'honor': '.fg-honor-system-container',
        'essence-runes': '.essence-runes-container',
        'karma-runes': '.essence-runes-container',
        'class': '.fg-class-system-container',
        'overlord-mastery': '.fg-overlord-mastery-container'
    },

    /**
     * Get configuration for a system
     */
    getConfig: function(systemId) {
        return this[systemId] || null;
    },

    /**
     * Get container selector for a system
     */
    getContainerSelector: function(systemId) {
        return this.containerSelectors[systemId] || null;
    },

    /**
     * Create a Quick Fill Button Manager for a system
     */
    createManager: function(systemId, container) {
        const config = this.getConfig(systemId);
        if (!config) {
            console.warn(`QuickFillButtonConfigs: No configuration found for system '${systemId}'`);
            return null;
        }

        try {
            return new QuickFillButtonManager({
                container: container,
                position: config.position,
                alignment: config.alignment,
                buttons: config.buttons,
                className: `fg-quick-fill-${systemId}`
            });
        } catch (error) {
            console.error(`QuickFillButtonConfigs: Failed to create manager for system '${systemId}':`, error);
            return null;
        }
    },

    /**
     * Initialize quick fill buttons for a system
     * This is the main function systems should call
     */
    initializeSystem: function(systemId, container) {
        const targetContainer = container || this.getContainerSelector(systemId);

        if (!targetContainer) {
            console.warn(`QuickFillButtonConfigs: No container specified and no default found for system '${systemId}'`);
            return null;
        }

        const manager = this.createManager(systemId, targetContainer);

        return manager;
    }
};
