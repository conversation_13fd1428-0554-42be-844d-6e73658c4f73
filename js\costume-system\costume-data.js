/**
 * Costume Data
 * Contains data for the costume system including available stats for each costume type
 * and epic craft options.
 *
 * Structure Overview:
 * - typeStats: Base stats available for each costume type
 * - metaOptions: Consolidated view of epic craft options across all grades
 * - maxSlots: Maximum number of slots per costume type
 * - minSlotsForEpic: Minimum slots required to use epic options
 */

window.CostumeData = {
    // Stats available for each costume type
    typeStats: {
        // Generic costume stats - These are the basic stats available for standard costumes
        // Values represent the stat bonus provided by each option
        generic: [
            { id: 'hp', value: 100 },
            { id: 'attack', value: 20 },
            { id: 'magicAttack', value: 20 },
            { id: 'defense', value: 15 },
            { id: 'evasion', value: 100 },
            { id: 'critDamage', value: 4 },
            { id: 'critRate', value: 1 },
            { id: 'maxCritRate', value: 1 },
            { id: 'swordSkillAmp', value: 2 },
            { id: 'magicSkillAmp', value: 2 },
            { id: 'resistCritRate', value: 1 },
            { id: 'accuracy', value: 50 },
            { id: 'penetration', value: 10 }
        ],

        // Force Wing costume stats - Specialized for offensive capabilities
        forceWing: [
            { id: 'hp', value: 200 },
            { id: 'attack', value: 30 },
            { id: "damageReduce", value: 10 },
            { id: "critDamage", value: 3 },
            { id: "swordSkillAmp", value: 2 },
            { id: "magicSkillAmp", value: 2 },
            { id: "accuracy", value: 100 },
            { id: "ignoreAccuracy", value: 20 },
            { id: "penetration", value: 15 },
            { id: "ignoreDamageReduce", value: 20 }
        ],

        // Vehicle costume stats - Focused on defensive and counter capabilities
        vehicle: [
            { id: 'hp', value: 200 },
            { id: 'defense', value: 20 },
            { id: "evasion", value: 50 },
            { id: "ignoreEvasion", value: 100 },
            { id: "critDamage", value: 3 },
            { id: "accuracy", value: 70 },
            { id: "ignoreAccuracy", value: 20 },
            { id: "penetration", value: 15 },
            { id: "ignoreDamageReduce", value: 20 },
            { id: "ignoreResistCritRate", value: 1 },
            { id: "ignoreResistCritDmg", value: 4 }
        ]
    },

    // Epic craft options data - Consolidated view of all options across all grades
    // This provides a summary of all stats and their values for each grade
    metaOptions: {
        generic: {
            // Stats grouped by type instead of by grade
            penetration: {
                name: 'Penetration',
                statId: 'penetration',
                values: [10, 15, 20, 30],  // Values for grades 1-4
                grades: [1, 2, 3, 4],
                chances: [25, 50, 15, 10]  // % chance of obtaining each grade
            },
            critDamage: {
                name: 'Crit. DMG',
                statId: 'critDamage',
                values: [3, 5, 7, 10],
                grades: [1, 2, 3, 4],
                chances: [25, 50, 15, 10]
            },
            allSkillAmp: {
                name: 'All Skill Amp.',
                statId: 'allSkillAmp',
                values: [1, 2, 3, 5],
                grades: [1, 2, 3, 4],
                chances: [25, 50, 15, 10]
            },
            resistCritRate: {
                name: 'Resist Crit. Rate',
                statId: 'resistCritRate',
                values: [1, 2, 3, 4],
                grades: [1, 2, 3, 4],
                chances: [25, 50, 15, 10]
            },
            allAttackUp: {
                name: 'All Attack Up',
                statId: 'allAttackUp',
                values: [20, 30, 40, 50],
                grades: [1, 2, 3, 4],
                chances: [25, 50, 15, 10]
            },
            defense: {
                name: 'Defense',
                statId: 'defense',
                values: [15, 20, 30, 40],
                grades: [1, 2, 3, 4],
                chances: [25, 50, 15, 10]
            },
            evasion: {
                name: 'Evasion',
                statId: 'evasion',
                values: [50, 70, 100, 150],
                grades: [1, 2, 3, 4],
                chances: [25, 50, 15, 10]
            },
            hp: {
                name: 'HP',
                statId: 'hp',
                values: [50, 70, 100, 150],
                grades: [1, 2, 3, 4],
                chances: [25, 50, 15, 10]
            }
        },

        forceWing: {
            hp: {
                name: 'HP',
                statId: 'hp',
                values: [100, 150, 200, 300],
                grades: [1, 2, 3, 4],
                chances: [25, 50, 15, 10]
            },
            defense: {
                name: 'Defense',
                statId: 'defense',
                values: [30, 40, 50, 60],
                grades: [1, 2, 3, 4],
                chances: [25, 50, 15, 10]
            },
            allAttackUp: {
                name: 'All Attack Up',
                statId: 'allAttackUp',
                values: [30, 40, 50, 60],
                grades: [1, 2, 3, 4],
                chances: [25, 50, 15, 10]
            },
            allSkillAmp: {
                name: 'All Skill Amp.',
                statId: 'allSkillAmp',
                values: [2, 3, 4, 5],
                grades: [1, 2, 3, 4],
                chances: [25, 50, 15, 10]
            },
            critDamage: {
                name: 'Crit. DMG',
                statId: 'critDamage',
                values: [4, 7, 9, 11],
                grades: [1, 2, 3, 4],
                chances: [25, 50, 15, 10]
            },
            normalDmgUp: {
                name: 'Normal DMG Up',
                statId: 'normalDmgUp',
                values: [5, 8, 11, 14],
                grades: [1, 2, 3, 4],
                chances: [25, 50, 15, 10]
            },
            addDmg: {
                name: 'Add DMG',
                statId: 'addDmg',
                values: [15, 30, 45, 60],
                grades: [1, 2, 3, 4],
                chances: [25, 50, 15, 10]
            },
            ignoreDmgReduce: {
                name: 'Ignore DMG Reduce',
                statId: 'ignoreDmgReduce',
                values: [20, 40, 60, 80],
                grades: [1, 2, 3, 4],
                chances: [25, 50, 15, 10]
            }
        },

        vehicle: {
            hp: {
                name: 'HP',
                statId: 'hp',
                values: [100, 150, 200, 300],
                grades: [1, 2, 3, 4],
                chances: [25, 50, 15, 10]
            },
            defense: {
                name: 'Defense',
                statId: 'defense',
                values: [20, 30, 40, 60],
                grades: [1, 2, 3, 4],
                chances: [25, 50, 15, 10]
            },
            ignorePenetration: {
                name: 'Ignore Penetration',
                statId: 'ignorePenetration',
                values: [20, 25, 35, 45],
                grades: [1, 2, 3, 4],
                chances: [25, 50, 15, 10]
            },
            allAttackUp: {
                name: 'All Attack Up',
                statId: 'allAttackUp',
                values: [30, 40, 50, 70],
                grades: [1, 2, 3, 4],
                chances: [25, 50, 15, 10]
            },
            allSkillAmp: {
                name: 'All Skill Amp.',
                statId: 'allSkillAmp',
                values: [3, 5, 7, 9],
                grades: [1, 2, 3, 4],
                chances: [25, 50, 15, 10]
            },
            ignoreResistSkillAmp: {
                name: 'Ignore Resist Skill Amp.',
                statId: 'ignoreResistSkillAmp',
                values: [3, 5, 7, 9],
                grades: [1, 2, 3, 4],
                chances: [25, 50, 15, 10]
            },
            critDamage: {
                name: 'Crit. DMG',
                statId: 'critDamage',
                values: [3, 5, 7, 9],
                grades: [1, 2, 3, 4],
                chances: [25, 50, 15, 10]
            },
            resistCritDmg: {
                name: 'Resist Crit. DMG',
                statId: 'resistCritDmg',
                values: [8, 12, 16, 20],
                grades: [1, 2, 3, 4],
                chances: [25, 50, 15, 10]
            }
        }
    },

    // Maximum number of slots for each costume type
    // Each costume type can have up to 3 stat slots
    maxSlots: {
        generic: 3,
        forceWing: 3,
        vehicle: 3
    },

    // Minimum slots required for epic options
    // Epic options can only be used when costume has at least 2 slots filled
    minSlotsForEpic: 2
};