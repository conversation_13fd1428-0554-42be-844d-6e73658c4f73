/**
 * Equipment Detail View
 * Handles the detail panel for equipment items, including all upgrade options
 * and configuration UI for weapons and other equipment
 */

window.EquipmentDetailView = {
    // Reference to the EquipmentSystem
    system: null,

    // Reference to DOM elements
    elements: {},

    /**
     * Initialize the detail view handler
     * @param {Object} system - Reference to the parent EquipmentSystem
     */
    init: function(system) {
        this.system = system;

        // Cache DOM elements
        this.elements = {
            detailsPanel: system.elements.detailsPanel,
            detailsTitle: system.elements.detailsTitle,
            detailsContent: system.elements.detailsContent
        };

        return this;
    },

    /**
     * Update the details panel with slot information
     * @param {Object} slot - The equipment slot to display
     */
    updateDetailsPanel: function(slot) {
        // Update title
        this.elements.detailsTitle.textContent = `${slot.name} Details`;

        // Get equipped item (if any) from the parent system
        const equippedItem = this.system.data.equippedItems[slot.id];

        // Reset upgrade settings for the current item type
        if (equippedItem) {
            // Store current item type for proper upgrade settings management
            const itemType = equippedItem.type || slot.type;

            // Initialize item-specific upgrade settings if needed
            if (this.system.data.weaponUpgrades && !this.system.data.weaponUpgrades.items) {
                this.system.data.weaponUpgrades.items = {};
            }

            // Get stored settings for this specific item or initialize new ones
            if (!this.system.data.weaponUpgrades.items[slot.id]) {
                this.system.data.weaponUpgrades.items[slot.id] = {
                    weaponId: equippedItem.id,
                    grade: 0,
                    epicOption: {
                        id: null,
                        level: 0
                    },
                    activeSlots: 1,
                    slotOptions: new Array(3).fill(null),
                    extremeLevel: 0,
                    divineLevel: 0,
                    chaosLevel: 0
                };
            }

            // Use the item-specific settings for the current selection
            this.system.data.weaponUpgrades.settings = this.system.data.weaponUpgrades.items[slot.id];
            this.system.data.weaponUpgrades.activeWeapon = equippedItem.id;
        }

        // Generate content based on the slot and equipped item
        let contentHTML = '';

        if (equippedItem) {
            // Generate UI dynamically based on item type
            contentHTML = this.renderItemDetailsUI(slot, equippedItem);
        } else {
            // Show empty slot
            contentHTML = `
                <div class="fg-empty-slot">
                    <p>No ${slot.name} equipped</p>
                    <p class="fg-slot-type">${this.formatSlotType(slot.type)}</p>
                    <button class="fg-button fg-equip-item-btn" data-slot-id="${slot.id}">Select ${slot.name}</button>
                </div>
            `;
        }

        // Update details content
        this.elements.detailsContent.innerHTML = contentHTML;

        // Setup event listeners for the UI based on item type
        this.setupDetailUIEventListeners(slot, equippedItem);
    },

    /**
     * Render appropriate UI for an item based on its type
     * @param {Object} slot - The equipment slot
     * @param {Object} item - The equipped item
     * @returns {string} HTML content for the details panel
     */
    renderItemDetailsUI: function(slot, item) {
        // Determine the item type and applicable upgrade paths
        const itemType = item.type || slot.type;

        // If we're using the new EquipmentData structure
        if (window.EquipmentData && EquipmentData.itemTypes && EquipmentData.itemTypes[itemType]) {
            const typeMetadata = EquipmentData.itemTypes[itemType];
            const upgradePaths = typeMetadata.upgradePaths || [];

            // If no upgrade paths, just show basic item details
            if (upgradePaths.length === 0) {
                return this.renderBasicItemDetails(item);
            }

            // Start building the UI with item header
            let html = `
                <div class="fg-item-full-details">
                    <div class="fg-item-header">
                        <img src="${item.imagePath}" alt="${item.name}" class="fg-item-thumbnail">
                        <div class="fg-item-title">
                            <h4>${item.name}</h4>
                            <div class="fg-item-type">${this.formatItemType(itemType)}</div>
                        </div>
                    </div>
                    <div class="fg-item-description">${item.description || ''}</div>
            `;

            // Add appropriate upgrade sections based on upgrade paths
            if (upgradePaths.includes('base')) {
                html += this.renderBaseUpgradeSection(item);
            }

            if (upgradePaths.includes('epic') && typeMetadata.canHaveEpicOption) {
                html += this.renderEpicOptionSection(item);
            }

            if (upgradePaths.includes('slots') && typeMetadata.hasSlots) {
                html += this.renderSlotsSection(item);
            }

            if (upgradePaths.includes('extreme')) {
                html += this.renderExtremeUpgradeSection(item);
            }

            if (upgradePaths.includes('divine')) {
                html += this.renderDivineUpgradeSection(item);
            }

            if (upgradePaths.includes('chaos')) {
                html += this.renderChaosUpgradeSection(item);
            }

            // Add controls section
            html += `
                <div class="fg-item-controls">
                    <button class="fg-button fg-remove-item-btn" data-slot-id="${slot.id}">Remove ${this.formatItemType(itemType)}</button>
                </div>
            </div>`;

            return html;

        } else {
            // Removed fallback logic - Always use EquipmentData
            // If EquipmentData or item type is missing, render basic details
            return this.renderBasicItemDetails(item);
        }
    },

    /**
     * Setup event listeners for the details UI based on item type
     * @param {Object} slot - The equipment slot
     * @param {Object} item - The equipped item (can be null for empty slots)
     */
    setupDetailUIEventListeners: function(slot, item) {
        // Set up event listeners for the detail panel buttons
        const equipBtn = this.elements.detailsContent.querySelector('.fg-equip-item-btn');
        if (equipBtn) {
            equipBtn.addEventListener('click', () => {
                this.system.openEquipmentModal(slot.id, slot.type);
            });
        }

        const removeBtn = this.elements.detailsContent.querySelector('.fg-remove-item-btn');
        if (removeBtn) {
            removeBtn.addEventListener('click', () => {
                this.system.removeEquippedItem(slot.id);
            });
        }

        // If no item, no further setup needed
        if (!item) return;

        // Determine item type and setup appropriate listeners
        const itemType = item.type || slot.type;

        // If using the new equipment data structure
        if (window.EquipmentData && EquipmentData.itemTypes && EquipmentData.itemTypes[itemType]) {
            const typeMetadata = EquipmentData.itemTypes[itemType];
            const upgradePaths = typeMetadata.upgradePaths || [];

            // Setup listeners for each upgrade path
            if (upgradePaths.includes('base')) {
                this.setupGradeUpgradeEvents(item);
            }

            if (upgradePaths.includes('epic') && typeMetadata.canHaveEpicOption) {
                this.setupEpicOptionEvents(item);
            }

            if (upgradePaths.includes('slots') && typeMetadata.hasSlots) {
                this.setupSlotOptionEvents(item);
            }

            if (upgradePaths.includes('extreme')) {
                this.setupExtremeUpgradeEvents(item);
            }

            if (upgradePaths.includes('divine')) {
                this.setupDivineUpgradeEvents(item);
            }

            if (upgradePaths.includes('chaos')) {
                this.setupChaosUpgradeEvents(item);
            }
        } else {
            // Removed fallback logic - No event listeners needed if data is missing
        }
    },

    /**
     * Render basic item details for items with no upgrades
     * @param {Object} item - The equipment item
     * @returns {string} HTML content
     */
    renderBasicItemDetails: function(item) {
        let statsHTML = '';
        const stats = item.baseStats || item.stats || {};

        for (const statName in stats) {
            const statValue = stats[statName];
            statsHTML += `<div class="fg-item-stat"><span class="fg-stat-name">${this.formatStatName(statName)}</span>: <span class="fg-stat-value">${statValue}</span></div>`;
        }

        return `
            <div class="fg-equipped-item">
                <div class="fg-item-header">
                    <img src="${item.imagePath}" alt="${item.name}" class="fg-item-thumbnail">
                    <h4>${item.name}</h4>
                </div>
                <div class="fg-item-description">${item.description || ''}</div>
                <div class="fg-item-stats">
                    ${statsHTML}
                </div>
                <button class="fg-button fg-remove-item-btn" data-slot-id="${item.slotId}">Remove Item</button>
            </div>
        `;
    },

    /**
     * Render item base stats section
     * @param {Object} item - The equipment item
     * @returns {string} HTML content for stats
     */
    renderItemBaseStats: function(item) {
        const stats = item.baseStats || item.stats || {};
        let html = '';

        for (const statName in stats) {
            const statValue = stats[statName];
            html += `
                <div class="fg-stat-item">
                    <span class="fg-stat-name">${this.formatStatName(statName)}:</span>
                    <span class="fg-stat-value">${statValue}${this.isPercentStat(statName) ? '%' : ''}</span>
                </div>
            `;
        }

        return html || '<div class="fg-no-stats">No base stats available</div>';
    },

    /**
     * Format item type for display
     * @param {string} itemType - The type of item
     * @returns {string} Formatted item type
     */
    formatItemType: function(itemType) {
        if (!itemType) return '';
        return itemType.charAt(0).toUpperCase() + itemType.slice(1);
    },

    /**
     * Format a list of stat bonuses for display
     */
    formatBonusesList: function(bonuses) {
        if (!bonuses || bonuses.length === 0) {
            return '<div class="fg-no-bonuses">No bonuses at this level</div>';
        }

        let html = '<div class="fg-bonuses-list">';
        bonuses.forEach(bonus => {
            // Use StatsConfig for consistent stat formatting
            const statName = typeof StatsConfig !== 'undefined' ? StatsConfig.getStatInfo(bonus.stat).name : this.formatStatName(bonus.stat);
            const formattedValue = typeof StatsConfig !== 'undefined' ? StatsConfig.formatStatValue(bonus.stat, bonus.value) : (this.isPercentStat(bonus.stat) ? bonus.value + '%' : bonus.value);

            // Use stat icon if available through StatsConfig
            let iconHtml = '';
            if (typeof StatsConfig !== 'undefined' && StatsConfig.getStatIconUrl) {
                const iconUrl = StatsConfig.getStatIconUrl(bonus.stat);
                iconHtml = `<img src="${iconUrl}" alt="${statName}" class="fg-stat-icon">`;
            }

            html += `
                <div class="fg-bonus-item">
                    ${iconHtml}
                    <span class="fg-stat-name">${statName}:</span>
                    <span class="fg-stat-value">+${formattedValue}</span>
                </div>
            `;
        });
        html += '</div>';

        return html;
    },

    /**
     * Calculate total stat value (base + upgrade bonus)
     * @param {number} baseValue - The base stat value
     * @param {number} upgradeValue - The upgrade bonus value
     * @returns {number} Total stat value
     */
    calculateTotalStatValue: function(baseValue, upgradeValue) {
        return (baseValue || 0) + (upgradeValue || 0);
    },

    /**
     * Format weapon type for display
     */
    formatWeaponType: function(weaponType) {
        return weaponType.charAt(0).toUpperCase() + weaponType.slice(1);
    },

    /**
     * Check if a stat should be displayed as a percentage
     */
    isPercentStat: function(statName) {
        // Use StatsConfig to determine if stat is percentage-based, with no fallback
        if (typeof StatsConfig !== 'undefined') {
            return StatsConfig.getStatInfo(statName).isPercentage || false;
        }

        // Throw error if StatsConfig not available
        console.error('StatsConfig is required but not available');
        return false;
    },

    /**
     * Format stat name for display
     */
    formatStatName: function(statName) {
        // Use StatsConfig for stat name with no fallback
        if (typeof StatsConfig !== 'undefined') {
            return StatsConfig.getStatInfo(statName).name || statName;
        }

        // Throw error if StatsConfig not available
        console.error('StatsConfig is required but not available');
        return statName;
    },

    /**
     * Format slot type for display
     */
    formatSlotType: function(slotType) {
        return slotType.charAt(0).toUpperCase() + slotType.slice(1);
    },

    /**
     * Render base upgrade section (grade +0 to +20)
     * @param {Object} item - The equipment item
     * @returns {string} HTML content
     */
    renderBaseUpgradeSection: function(item) {
        // Get current upgrade level from system data
        const settings = this.system.data.weaponUpgrades.settings || {};
        const level = settings.grade || 0;

        // If this is a belt, use the belt-specific renderer
        if (item.type === 'belt') {
            return this.renderBeltUpgradeSection(item, level);
        }

        // For belts, just show base stats (belts don't have upgrade bonuses like weapons)
        let statsHTML = '';
        const baseStats = item.baseStats || {};

        if (baseStats.attack) {
            statsHTML += `
                <div class="fg-stat-item">
                    <span class="fg-stat-name">Attack:</span>
                    <span class="fg-stat-value">${baseStats.attack}</span>
                </div>`;
        }

        if (baseStats.magicAttack) {
            statsHTML += `
                <div class="fg-stat-item">
                    <span class="fg-stat-name">Magic Attack:</span>
                    <span class="fg-stat-value">${baseStats.magicAttack}</span>
                </div>`;
        }

        if (baseStats.attackRate) {
            statsHTML += `
                <div class="fg-stat-item">
                    <span class="fg-stat-name">Attack Rate:</span>
                    <span class="fg-stat-value">${baseStats.attackRate}</span>
                </div>`;
        }

        // Add other base stats that don't change with grade
        for (const statName in baseStats) {
            if (!['attack', 'magicAttack', 'attackRate'].includes(statName)) {
                statsHTML += `
                    <div class="fg-stat-item">
                        <span class="fg-stat-name">${this.formatStatName(statName)}:</span>
                        <span class="fg-stat-value">${baseStats[statName]}${this.isPercentStat(statName) ? '%' : ''}</span>
                    </div>`;
            }
        }

        return `
            <div class="fg-upgrade-section fg-grade-upgrade">
                <h5>${item.name} +${level}</h5>
                <div class="fg-slider-container">
                    <input type="range" min="0" max="20" value="${level}" class="fg-grade-slider" id="fg-grade-slider">
                    <div class="fg-grade-value">${level}</div>
                </div>
                <div class="fg-combined-stats">
                    ${statsHTML}
                </div>
            </div>
        `;
    },

    /**
     * Render belt upgrade section (grade +0 to +20)
     * @param {Object} belt - The belt item
     * @param {number} level - The current upgrade level
     * @returns {string} HTML content
     */
    renderBeltUpgradeSection: function(belt, level) {
        // Get belt data from BeltsData based on upgradeData reference
        let upgradeStats = {};

        if (window.BeltsData && belt.upgradeData) {
            const beltData = window.BeltsData[belt.upgradeData];

            if (beltData && Array.isArray(beltData.upgrades) &&
                level >= 0 && level < beltData.upgrades.length) {
                // Get the stats for this upgrade level
                upgradeStats = beltData.upgrades[level];
            }
        }

        // Format base stats with upgrades
        let statsHTML = '';
        const baseStats = belt.baseStats || {};

        // Add base stats first (only if they have a value)
        for (const statName in baseStats) {
            const baseValue = baseStats[statName];
            const upgradeValue = upgradeStats[statName] || 0;
            const totalValue = this.calculateTotalStatValue(baseValue, upgradeValue);

            if (totalValue === 0) continue; // Skip stats with zero total value

            statsHTML += `
                <div class="fg-stat-item">
                    <span class="fg-stat-name">${this.formatStatName(statName)}:</span>
                    <span class="fg-stat-value">${totalValue}${this.isPercentStat(statName) ? '%' : ''}</span>
                </div>`;
        }

        // Add upgrade stats that don't exist in base stats (only if they have a value)
        for (const statName in upgradeStats) {
            if (!baseStats[statName] && upgradeStats[statName] > 0) {
                statsHTML += `
                    <div class="fg-stat-item">
                        <span class="fg-stat-name">${this.formatStatName(statName)}:</span>
                        <span class="fg-stat-value">${upgradeStats[statName]}${this.isPercentStat(statName) ? '%' : ''}</span>
                    </div>`;
            }
        }

        return `
            <div class="fg-upgrade-section fg-grade-upgrade">
                <h5>${belt.name} +${level}</h5>
                <div class="fg-slider-container">
                    <input type="range" min="0" max="20" value="${level}" class="fg-grade-slider" id="fg-grade-slider">
                    <div class="fg-grade-value">${level}</div>
                </div>
                <div class="fg-combined-stats">
                    ${statsHTML}
                </div>
            </div>
        `;
    },

    /**
     * Render epic option section for weapons
     * @param {Object} item - The weapon item
     * @returns {string} HTML content
     */
    renderEpicOptionSection: function(item) {
        const settings = this.system.data.weaponUpgrades.settings || {};
        const epicOption = settings.epicOption || { id: null, level: 0 };

        // Get all available epic options from EquipmentData
        const epicOptions = window.EquipmentData ? EquipmentData.epicOptions : [];

        // Selected epic option
        const selectedOption = epicOptions.find(o => o.id === epicOption.id) || epicOptions[0] || {};
        const optionValue = selectedOption.levels ?
            (selectedOption.levels[epicOption.level]?.value || 0) : 0;

        // Format options dropdown
        let optionsHTML = '';
        epicOptions.forEach(option => {
            optionsHTML += `<option value="${option.id}" ${epicOption.id === option.id ? 'selected' : ''}>${option.name}</option>`;
        });

        // Create a bonus list for the current epic option
        let bonuses = [];
        if (selectedOption.id && optionValue > 0) {
            // Use the stat ID directly (no mapping needed since IDs are now consistent)
            const statId = selectedOption.id;

            bonuses.push({ stat: statId, value: optionValue });
        }

        return `
            <div class="fg-upgrade-section fg-epic-option">
                <h5>Epic Option (Level ${epicOption.level})</h5>
                <div class="fg-epic-option-row">
                    <select class="fg-epic-option-select" id="fg-epic-option-select">
                        ${optionsHTML}
                    </select>
                </div>
                <div class="fg-epic-level-buttons">
                    <button class="fg-button fg-epic-level-btn ${epicOption.level === 0 ? 'fg-active' : ''}" data-level="0">Level 0</button>
                    <button class="fg-button fg-epic-level-btn ${epicOption.level === 1 ? 'fg-active' : ''}" data-level="1">Level 1</button>
                    <button class="fg-button fg-epic-level-btn ${epicOption.level === 2 ? 'fg-active' : ''}" data-level="2">Level 2</button>
                </div>
                <div class="fg-epic-bonuses">
                    ${this.formatBonusesList(bonuses)}
                </div>
            </div>
        `;
    },

    /**
     * Render slots section for weapons
     * @param {Object} item - The weapon item
     * @returns {string} HTML content
     */
    renderSlotsSection: function(item) {
        const settings = this.system.data.weaponUpgrades.settings || {};
        const activeSlots = settings.activeSlots || 1;
        const maxSlots = item.maxSlots || 3;

        // Get slot options from EquipmentData
        const slotOptions = window.EquipmentData ? EquipmentData.slotOptions : [];

        // Format slot options dropdown
        let slotOptionsHTML = '';
        slotOptions.forEach(option => {
            slotOptionsHTML += `<option value="${option.id}">${option.name} (+${option.value}${this.isPercentStat(option.id) ? '%' : ''})</option>`;
        });

        // Create a bonus list for all active slot options
        let bonuses = [];
        if (Array.isArray(settings.slotOptions)) {
            for (let i = 0; i < activeSlots; i++) {
                const optionId = settings.slotOptions[i];
                if (optionId) {
                    const option = slotOptions.find(o => o.id === optionId);
                    if (option) {
                        // Use the stat ID directly (no mapping needed since IDs are now consistent)
                        const statId = option.id;

                        // Check if this stat already exists in the bonuses array
                        const existingBonus = bonuses.find(b => b.stat === statId);
                        if (existingBonus) {
                            existingBonus.value += option.value;
                        } else {
                            bonuses.push({ stat: statId, value: option.value });
                        }
                    }
                }
            }
        }

        return `
            <div class="fg-upgrade-section fg-option-slots">
                <h5>Option Slots (${activeSlots}/${maxSlots})</h5>
                <div class="fg-slot-container">
                    <div class="fg-slot-row ${activeSlots >= 1 ? 'fg-slot-active' : 'fg-slot-inactive'}">
                        <div class="fg-slot-number">1</div>
                        <select class="fg-slot-option-select" id="fg-slot-option-1" ${activeSlots >= 1 ? '' : 'disabled'}>
                            ${slotOptionsHTML}
                        </select>
                    </div>
                    <div class="fg-slot-row ${activeSlots >= 2 ? 'fg-slot-active' : 'fg-slot-inactive'}">
                        <div class="fg-slot-number">2</div>
                        <select class="fg-slot-option-select" id="fg-slot-option-2" ${activeSlots >= 2 ? '' : 'disabled'}>
                            ${slotOptionsHTML}
                        </select>
                    </div>
                    <div class="fg-slot-row ${activeSlots >= 3 ? 'fg-slot-active' : 'fg-slot-inactive'}">
                        <div class="fg-slot-number">3</div>
                        <select class="fg-slot-option-select" id="fg-slot-option-3" ${activeSlots >= 3 ? '' : 'disabled'}>
                            ${slotOptionsHTML}
                        </select>
                    </div>
                </div>
                <div class="fg-slot-buttons">
                    <button class="fg-button fg-slot-count-btn ${activeSlots === 1 ? 'fg-active' : ''}" data-count="1">1 Slot</button>
                    <button class="fg-button fg-slot-count-btn ${activeSlots === 2 ? 'fg-active' : ''}" data-count="2" ${maxSlots < 2 ? 'disabled' : ''}>2 Slots</button>
                    <button class="fg-button fg-slot-count-btn ${activeSlots === 3 ? 'fg-active' : ''}" data-count="3" ${maxSlots < 3 ? 'disabled' : ''}>3 Slots</button>
                </div>
                <div class="fg-slots-bonuses">
                    ${this.formatBonusesList(bonuses)}
                </div>
            </div>
        `;
    },

    /**
     * Render extreme upgrade section
     * @param {Object} item - The equipment item
     * @returns {string} HTML content
     */
    renderExtremeUpgradeSection: function(item) {
        const settings = this.system.data.weaponUpgrades.settings || {};
        const extremeLevel = settings.extremeLevel || 0;
        const maxExtremeLevel = item.maxExtremeLevel || 7;

        // Get extreme upgrades directly from level7 array
        const extremeUpgrades = (window.EquipmentData && EquipmentData.extremeUpgrades) ?
            EquipmentData.extremeUpgrades.level7 : [];

        // Get current level bonuses
        let extremeBonuses = [];
        if (extremeLevel > 0 && extremeLevel < extremeUpgrades.length) {
            // Check if this is a two-handed weapon
            const isTwoHanded = item.handType === 'two-handed' || item.twoHanded === true;

            // Clone the bonuses array so we can modify it for display
            extremeBonuses = JSON.parse(JSON.stringify(extremeUpgrades[extremeLevel] || []));

            // Double the values for two-handed weapons for display purposes
            if (isTwoHanded) {
                extremeBonuses.forEach(bonus => {
                    bonus.value = bonus.value * 2;
                });
            }
        }

        return `
            <div class="fg-upgrade-section fg-extreme-upgrade">
                <h5>Extreme Upgrade (${extremeLevel}/${maxExtremeLevel})</h5>
                <div class="fg-slider-container">
                    <input type="range" min="0" max="${maxExtremeLevel}" value="${extremeLevel}" class="fg-extreme-slider" id="fg-extreme-slider">
                    <div class="fg-extreme-value">${extremeLevel}</div>
                </div>
                <div class="fg-extreme-bonuses">
                    ${this.formatBonusesList(extremeBonuses)}
                </div>
            </div>
        `;
    },

    /**
     * Render divine upgrade section
     * @param {Object} item - The equipment item
     * @returns {string} HTML content
     */
    renderDivineUpgradeSection: function(item) {
        const settings = this.system.data.weaponUpgrades.settings || {};
        const divineLevel = settings.divineLevel || 0;
        const maxDivineLevel = 15; // Standard max divine level

        // Get divine upgrades directly from data array (same pattern as extreme)
        let divineBonuses = [];

        const divineUpgrades = (window.WeaponsData && WeaponsData.divineUpgrades) ?
            WeaponsData.divineUpgrades.high : null;

        if (divineLevel > 0 && divineUpgrades) {
            // Convert the divine upgrade data to the same format as extreme
            for (const statName in divineUpgrades) {
                const statArray = divineUpgrades[statName];
                if (statArray && divineLevel < statArray.length) {
                    const value = statArray[divineLevel];
                    if (value > 0) {
                        divineBonuses.push({ stat: statName, value: value });
                    }
                }
            }
        }

        return `
            <div class="fg-upgrade-section fg-divine-upgrade">
                <h5>Divine Upgrade (${divineLevel}/${maxDivineLevel})</h5>
                <div class="fg-slider-container">
                    <input type="range" min="0" max="${maxDivineLevel}" value="${divineLevel}" class="fg-divine-slider" id="fg-divine-slider">
                    <div class="fg-divine-value">${divineLevel}</div>
                </div>
                <div class="fg-divine-bonuses">
                    ${this.formatBonusesList(divineBonuses)}
                </div>
            </div>
        `;
    },

    /**
     * Render chaos upgrade section for accessories
     * @param {Object} item - The accessory item
     * @returns {string} HTML content
     */
    renderChaosUpgradeSection: function(item) {
        const settings = this.system.data.weaponUpgrades.settings || {};
        const chaosLevel = settings.chaosLevel || 0;
        const maxChaosLevel = 15; // Standard max chaos level

        // Get tier from item data or default to gold
        const tier = item.chaosTier || 'gold';

        // Get chaos upgrades data from EquipmentData for the specific tier
        let chaosBonuses = [];
        if (window.EquipmentData && EquipmentData.chaosUpgrades && EquipmentData.chaosUpgrades[tier]) {
            const chaosUpgrades = EquipmentData.chaosUpgrades[tier] || [];
            chaosBonuses = chaosLevel > 0 && chaosLevel < chaosUpgrades.length ?
                chaosUpgrades[chaosLevel] : [];
        }

        return `
            <div class="fg-upgrade-section fg-chaos-upgrade">
                <h5>Chaos Upgrade (${chaosLevel}/${maxChaosLevel})</h5>
                <div class="fg-tier-info">Tier: ${tier.charAt(0).toUpperCase() + tier.slice(1)}</div>
                <div class="fg-slider-container">
                    <input type="range" min="0" max="${maxChaosLevel}" value="${chaosLevel}" class="fg-chaos-slider" id="fg-chaos-slider">
                    <div class="fg-chaos-value">${chaosLevel}</div>
                </div>
                <div class="fg-chaos-bonuses">
                    ${this.formatBonusesList(chaosBonuses)}
                </div>
            </div>
        `;
    },

    /**
     * Calculate and format total stats for an item including all upgrade bonuses
     * @param {Object} item - The equipment item
     * @returns {string} HTML content of total stats
     */
    calculateTotalItemStats: function(item) {
        // For weapons, calculate stats with all upgrade bonuses
        if (item.type === 'weapon') {
            // Get stats object from the system's stat calculator
            const stats = this.system.calculateStats();

            // Format stats grouped by category
            let statsHtml = '<div class="fg-stat-list-container"><ul class="fg-stat-list">';

            // Group stats by category
            const statCategories = {
                offensive: [],
                defensive: [],
                utility: []
            };

            // Helper to categorize a stat - use only StatsConfig, no fallbacks
            const categorize = (id, value) => {
                // Skip empty/zero values
                if (!value) return;

                let category = 'utility'; // Default category

                // Use StatsConfig to determine the category
                if (typeof StatsConfig !== 'undefined' && StatsConfig.getStatInfo) {
                    const info = StatsConfig.getStatInfo(id);
                    if (info && info.category) {
                        category = info.category;
                    }
                } else {
                    console.error('StatsConfig is required but not available');
                }

                // Add to appropriate category
                if (statCategories[category]) {
                    statCategories[category].push({
                        id: id,
                        value: value
                    });
                }
            };

            // Process each stat
            for (const statId in stats) {
                if (stats[statId]) {
                    categorize(statId, stats[statId]);
                }
            }

            // Define labels for categories
            const categoryLabels = {
                offensive: 'Attack Stats',
                defensive: 'Defense Stats',
                utility: 'Utility Stats'
            };

            for (const category in statCategories) {
                if (statCategories[category].length > 0) {
                    statsHtml += `<li class="fg-stat-category-header">${categoryLabels[category]}</li>`;

                    statCategories[category].forEach(stat => {
                        const statId = stat.id;
                        const value = stat.value;

                        // Format stat name and value using StatsConfig only
                        const statName = typeof StatsConfig !== 'undefined' ?
                            StatsConfig.getStatInfo(statId).name : statId;
                        const formattedValue = typeof StatsConfig !== 'undefined' ?
                            StatsConfig.formatStatValue(statId, value) : value;

                        // Use stat icon if available
                        let iconHtml = '';
                        if (typeof StatsConfig !== 'undefined' && StatsConfig.getStatIconUrl) {
                            const iconUrl = StatsConfig.getStatIconUrl(statId);
                            iconHtml = `<img src="${iconUrl}" alt="${statName}" class="fg-stat-icon">`;
                        }

                        statsHtml += `<li>${iconHtml}<span class="fg-stat-name">${statName}:</span> <span class="fg-stat-value">${formattedValue}</span></li>`;
                    });
                }
            }

            statsHtml += '</ul></div>';
            return statsHtml;
        }

        // For non-weapon items (placeholders for now)
        return `<div class="fg-total-stats-placeholder">
            <p>Base stats will apply.</p>
        </div>`;
    },

    /**
     * Setup event handlers for the base grade upgrade slider
     * @param {Object} item - The equipment item
     */
    setupGradeUpgradeEvents: function(item) {
        const gradeSlider = document.getElementById('fg-grade-slider');
        if (gradeSlider) {
            // Ensure the slider value matches the current settings
            const settings = this.system.data.weaponUpgrades.settings;
            const currentLevel = settings.grade || 0;
            gradeSlider.value = currentLevel;

            // Update the displayed value to match
            const gradeValueElement = document.querySelector('.fg-grade-value');
            if (gradeValueElement) {
                gradeValueElement.textContent = currentLevel;
            }

            // Update the heading to show the correct level
            const heading = document.querySelector('.fg-grade-upgrade h5');
            if (heading) {
                heading.textContent = `${item.name} +${currentLevel}`;
            }

            // Update the stats display to match the current level
            if (item.type === 'belt') {
                this.updateBeltUpgradeBonuses(item);
            } else {
                this.updateWeaponStats();
            }

            gradeSlider.addEventListener('input', (e) => {
                const value = parseInt(e.target.value);
                settings.grade = value;

                // Update the displayed value
                document.querySelector('.fg-grade-value').textContent = value;

                // Update the heading to show the correct level
                const heading = document.querySelector('.fg-grade-upgrade h5');
                if (heading) {
                    heading.textContent = `${item.name} +${value}`;
                }

                // Update bonuses display based on item type
                if (item.type === 'belt') {
                    this.updateBeltUpgradeBonuses(item);
                } else {
                    this.updateWeaponStats();
                }
            });
        }
    },



    /**
     * Update the belt upgrade bonuses display based on current upgrade settings
     * @param {Object} belt - The belt item
     */
    updateBeltUpgradeBonuses: function(belt) {
        console.log('BELT: updateBeltUpgradeBonuses called');
        // Get the current upgrade level
        const settings = this.system.data.weaponUpgrades.settings;
        const level = settings.grade || 0;

        // Update the heading to show the correct level
        const heading = document.querySelector('.fg-grade-upgrade h5');
        if (heading) {
            heading.textContent = `${belt.name} +${level}`;
        }

        // Get combined stats container
        const statsContainer = document.querySelector('.fg-combined-stats');
        console.log('BELT: statsContainer found:', !!statsContainer);
        if (!statsContainer) return;

        // Get belt data from BeltsData
        let upgradeStats = {};

        if (window.BeltsData && belt.upgradeData) {
            const beltData = window.BeltsData[belt.upgradeData];

            if (beltData && Array.isArray(beltData.upgrades) &&
                level >= 0 && level < beltData.upgrades.length) {
                upgradeStats = beltData.upgrades[level];
            }
        }

        // Format base stats with upgrades
        let statsHTML = '';
        const baseStats = belt.baseStats || {};

        // Add base stats first (only if they have a value)
        for (const statName in baseStats) {
            const baseValue = baseStats[statName];
            const upgradeValue = upgradeStats[statName] || 0;
            const totalValue = this.calculateTotalStatValue(baseValue, upgradeValue);

            if (totalValue === 0) continue; // Skip stats with zero total value

            statsHTML += `
                <div class="fg-stat-item">
                    <span class="fg-stat-name">${this.formatStatName(statName)}:</span>
                    <span class="fg-stat-value">${totalValue}${this.isPercentStat(statName) ? '%' : ''}</span>
                </div>`;
        }

        // Add upgrade stats that don't exist in base stats (only if they have a value)
        for (const statName in upgradeStats) {
            if (!baseStats[statName] && upgradeStats[statName] > 0) {
                statsHTML += `
                    <div class="fg-stat-item">
                        <span class="fg-stat-name">${this.formatStatName(statName)}:</span>
                        <span class="fg-stat-value">${upgradeStats[statName]}${this.isPercentStat(statName) ? '%' : ''}</span>
                    </div>`;
            }
        }

        // Update the combined stats display
        statsContainer.innerHTML = statsHTML;

        // Update total stats
        this.system.updateStats();
    },

    /**
     * Setup events for epic option controls
     * @param {Object} item - The equipment item
     */
    setupEpicOptionEvents: function(item) {
        // Epic option select
        const epicSelect = document.getElementById('fg-epic-option-select');
        if (epicSelect) {
            epicSelect.addEventListener('change', (e) => {
                const settings = this.system.data.weaponUpgrades.settings;
                settings.epicOption.id = e.target.value;
                this.updateWeaponStats();
            });

            // Initialize with a default value if not set
            const settings = this.system.data.weaponUpgrades.settings;
            if (!settings.epicOption || !settings.epicOption.id) {
                if (epicSelect.options.length > 0) {
                    settings.epicOption = {
                        id: epicSelect.options[0].value,
                        level: 0
                    };
                    epicSelect.value = settings.epicOption.id;
                }
            } else {
                epicSelect.value = settings.epicOption.id;
            }
        }

        // Epic level buttons
        const epicLevelButtons = document.querySelectorAll('.fg-epic-level-btn');
        epicLevelButtons.forEach(btn => {
            btn.addEventListener('click', (e) => {
                const level = parseInt(e.target.getAttribute('data-level'));
                const settings = this.system.data.weaponUpgrades.settings;
                settings.epicOption.level = level;

                // Update active button
                epicLevelButtons.forEach(b => b.classList.remove('fg-active'));
                e.target.classList.add('fg-active');

                this.updateWeaponStats();
            });
        });
    },

    /**
     * Setup events for slot option controls
     * @param {Object} item - The equipment item
     */
    setupSlotOptionEvents: function(item) {
        // Slot count buttons
        const slotCountButtons = document.querySelectorAll('.fg-slot-count-btn');
        slotCountButtons.forEach(btn => {
            btn.addEventListener('click', (e) => {
                if (e.target.hasAttribute('disabled')) return;

                const count = parseInt(e.target.getAttribute('data-count'));
                const settings = this.system.data.weaponUpgrades.settings;
                settings.activeSlots = count;

                // Update active button
                slotCountButtons.forEach(b => b.classList.remove('fg-active'));
                e.target.classList.add('fg-active');

                // Update slot rows
                const slotRows = document.querySelectorAll('.fg-slot-row');
                slotRows.forEach((row, index) => {
                    if (index < count) {
                        row.classList.remove('fg-slot-inactive');
                        row.classList.add('fg-slot-active');
                        row.querySelector('select').removeAttribute('disabled');
                    } else {
                        row.classList.remove('fg-slot-active');
                        row.classList.add('fg-slot-inactive');
                        row.querySelector('select').setAttribute('disabled', 'disabled');
                    }
                });

                this.updateWeaponStats();
            });
        });

        // Slot option selects
        for (let i = 1; i <= 3; i++) {
            const slotSelect = document.getElementById(`fg-slot-option-${i}`);
            if (slotSelect) {
                slotSelect.addEventListener('change', (e) => {
                    const settings = this.system.data.weaponUpgrades.settings;

                    // Make sure the slotOptions array is initialized
                    if (!Array.isArray(settings.slotOptions) || settings.slotOptions.length < i) {
                        settings.slotOptions = new Array(3).fill(null);
                    }

                    settings.slotOptions[i-1] = e.target.value;
                    this.updateWeaponStats();
                });

                // Initialize slot options from saved settings if available
                const settings = this.system.data.weaponUpgrades.settings;
                if (!Array.isArray(settings.slotOptions) || settings.slotOptions.length < 3) {
                    settings.slotOptions = new Array(3).fill(null);
                }

                if (settings.slotOptions && settings.slotOptions[i-1]) {
                    slotSelect.value = settings.slotOptions[i-1];
                } else {
                    // Default to first option
                    if (slotSelect.options.length > 0) {
                        const firstOption = slotSelect.options[0].value;
                        settings.slotOptions[i-1] = firstOption;
                        slotSelect.value = firstOption;
                    }
                }
            }
        }
    },

    /**
     * Setup events for extreme upgrade controls
     * @param {Object} item - The equipment item
     */
    setupExtremeUpgradeEvents: function(item) {
        const extremeSlider = document.getElementById('fg-extreme-slider');
        if (extremeSlider) {
            extremeSlider.addEventListener('input', (e) => {
                const value = parseInt(e.target.value);
                const settings = this.system.data.weaponUpgrades.settings;
                settings.extremeLevel = value;

                // Update the displayed value
                document.querySelector('.fg-extreme-value').textContent = value;

                // Update the heading to show the correct level
                const heading = document.querySelector('.fg-extreme-upgrade h5');
                if (heading) {
                    const maxLevel = item.maxExtremeLevel || 7;
                    heading.textContent = `Extreme Upgrade (${value}/${maxLevel})`;
                }

                console.log('EXTREME: Calling updateWeaponStats');
                this.updateWeaponStats();
            });
        }
    },

    /**
     * Setup events for divine upgrade controls
     * @param {Object} item - The equipment item
     */
    setupDivineUpgradeEvents: function(item) {
        const divineSlider = document.getElementById('fg-divine-slider');
        if (divineSlider) {
            // Ensure the slider value matches the current settings
            const settings = this.system.data.weaponUpgrades.settings;
            const currentLevel = settings.divineLevel || 0;
            divineSlider.value = currentLevel;

            divineSlider.addEventListener('input', (e) => {
                const value = parseInt(e.target.value);
                settings.divineLevel = value;

                // Update the displayed value
                document.querySelector('.fg-divine-value').textContent = value;

                // Update the heading to show the correct level
                const heading = document.querySelector('.fg-divine-upgrade h5');
                if (heading) {
                    heading.textContent = `Divine Upgrade (${value}/15)`;
                }

                console.log('DIVINE: Calling updateWeaponStats');
                this.updateWeaponStats();
            });
        }
    },

    /**
     * Setup events for chaos upgrade controls
     * @param {Object} item - The equipment item
     */
    setupChaosUpgradeEvents: function(item) {
        const chaosSlider = document.getElementById('fg-chaos-slider');
        if (chaosSlider) {
            chaosSlider.addEventListener('input', (e) => {
                const value = parseInt(e.target.value);
                const settings = this.system.data.weaponUpgrades.settings;
                settings.chaosLevel = value;

                // Update the displayed value
                document.querySelector('.fg-chaos-value').textContent = value;

                // Update the heading to show the correct level
                const heading = document.querySelector('.fg-chaos-upgrade h5');
                if (heading) {
                    heading.textContent = `Chaos Upgrade (${value}/15)`;
                }

                this.updateWeaponStats();
            });
        }
    },

    /**
     * Update the weapon stats display based on current upgrade settings
     */
    updateWeaponStats: function() {
        // Get the active item
        const slot = this.system.data.selectedSlot;
        if (!slot) return;

        const item = this.system.data.equippedItems[slot.id];
        if (!item) return;

        // Get current settings
        const settings = this.system.data.weaponUpgrades.settings;
        const level = settings.grade || 0;

        // Update the grade heading to show the correct level
        const heading = document.querySelector('.fg-grade-upgrade h5');
        if (heading) {
            heading.textContent = `${item.name} +${level}`;
        }

        // Update combined stats for weapons
        const statsContainer = document.querySelector('.fg-combined-stats');
        if (statsContainer) {
            // Use WeaponsData upgrade system instead of modifiers
            const weaponType = item.subtype || item.material || 'orb'; // Default to orb if no subtype

            let statsHTML = '';
            const baseStats = item.baseStats || {};

            if (baseStats.attack) {
                const baseAttack = baseStats.attack;
                const upgradeBonus = window.WeaponsData ? WeaponsData.getUpgradeStat(weaponType, 'attack', level) : 0;
                const totalAttack = baseAttack + upgradeBonus;

                statsHTML += `
                    <div class="fg-stat-item">
                        <span class="fg-stat-name">Attack:</span>
                        <span class="fg-stat-value">${totalAttack}</span>
                    </div>`;
            }

            if (baseStats.magicAttack) {
                const baseMagicAttack = baseStats.magicAttack;
                const upgradeBonus = window.WeaponsData ? WeaponsData.getUpgradeStat(weaponType, 'magicAttack', level) : 0;
                const totalMagicAttack = baseMagicAttack + upgradeBonus;

                statsHTML += `
                    <div class="fg-stat-item">
                        <span class="fg-stat-name">Magic Attack:</span>
                        <span class="fg-stat-value">${totalMagicAttack}</span>
                    </div>`;
            }

            if (baseStats.attackRate) {
                const baseAttackRate = baseStats.attackRate;
                const upgradeBonus = window.WeaponsData ? WeaponsData.getUpgradeStat(weaponType, 'attackRate', level) : 0;
                const totalAttackRate = baseAttackRate + upgradeBonus;

                statsHTML += `
                    <div class="fg-stat-item">
                        <span class="fg-stat-name">Attack Rate:</span>
                        <span class="fg-stat-value">${totalAttackRate}</span>
                    </div>`;
            }

            // Add other base stats that don't change with grade (only if they have a value)
            for (const statName in baseStats) {
                if (!['attack', 'magicAttack', 'attackRate'].includes(statName) && baseStats[statName] !== 0) {
                    statsHTML += `
                        <div class="fg-stat-item">
                            <span class="fg-stat-name">${this.formatStatName(statName)}:</span>
                            <span class="fg-stat-value">${baseStats[statName]}${this.isPercentStat(statName) ? '%' : ''}</span>
                        </div>`;
                }
            }

            statsContainer.innerHTML = statsHTML;
        }

        // Update epic option bonuses
        const epicContainer = document.querySelector('.fg-epic-bonuses');
        if (epicContainer) {
            const epicOptions = window.EquipmentData ? EquipmentData.epicOptions : [];
            const epicOption = settings.epicOption || { id: null, level: 0 };
            const selectedOption = epicOptions.find(o => o.id === epicOption.id) || epicOptions[0] || {};
            const optionValue = selectedOption.levels ?
                (selectedOption.levels[epicOption.level]?.value || 0) : 0;

            // Create bonus list for epic option
            let bonuses = [];
            if (selectedOption.id && optionValue > 0) {
                // Use the stat ID directly (no mapping needed since IDs are now consistent)
                const statId = selectedOption.id;

                bonuses.push({ stat: statId, value: optionValue });
            }

            epicContainer.innerHTML = this.formatBonusesList(bonuses);
        }

        // Update slot options bonuses
        const slotsContainer = document.querySelector('.fg-slots-bonuses');
        if (slotsContainer) {
            const slotOptions = window.EquipmentData ? EquipmentData.slotOptions : [];
            const activeSlots = settings.activeSlots || 1;

            // Create bonus list for all slot options
            let bonuses = [];
            if (Array.isArray(settings.slotOptions)) {
                for (let i = 0; i < activeSlots; i++) {
                    const optionId = settings.slotOptions[i];
                    if (optionId) {
                        const option = slotOptions.find(o => o.id === optionId);
                        if (option) {
                            // Use the stat ID directly (no mapping needed since IDs are now consistent)
                            const statId = option.id;

                            // Check if this stat already exists in the bonuses array
                            const existingBonus = bonuses.find(b => b.stat === statId);
                            if (existingBonus) {
                                existingBonus.value += option.value;
                            } else {
                                bonuses.push({ stat: statId, value: option.value });
                            }
                        }
                    }
                }
            }

            slotsContainer.innerHTML = this.formatBonusesList(bonuses);
        }

        // Update the extreme bonuses display for weapons
        if (item.type === 'weapon') {
            const extremeContainer = document.querySelector('.fg-extreme-bonuses');
            if (extremeContainer) {
                // Get extreme upgrades directly from level7 array
                const extremeUpgrades = (window.EquipmentData && EquipmentData.extremeUpgrades) ?
                    EquipmentData.extremeUpgrades.level7 : [];

                // Get current level bonuses
                let extremeBonuses = [];
                if (settings.extremeLevel > 0 && settings.extremeLevel < extremeUpgrades.length) {
                    // Check if this is a two-handed weapon
                    const isTwoHanded = ['greatsword', 'daikatana'].includes(item.subtype) ||
                                        item.handType === 'two-handed' ||
                                        item.twoHanded === true;

                    // Clone the bonuses array so we can modify it for display
                    extremeBonuses = JSON.parse(JSON.stringify(extremeUpgrades[settings.extremeLevel] || []));

                    // Double the values for two-handed weapons for display purposes
                    if (isTwoHanded) {
                        extremeBonuses.forEach(bonus => {
                            bonus.value = bonus.value * 2;
                        });
                    }
                }

                extremeContainer.innerHTML = this.formatBonusesList(extremeBonuses);
            }

            // Update the divine bonuses display
            const divineContainer = document.querySelector('.fg-divine-bonuses');
            if (divineContainer) {
                // Get divine upgrades directly from data array (same pattern as extreme)
                const divineUpgrades = (window.WeaponsData && WeaponsData.divineUpgrades) ?
                    WeaponsData.divineUpgrades.high : null;

                // Get current level bonuses
                let divineBonuses = [];
                if (settings.divineLevel > 0 && divineUpgrades) {
                    // Convert the divine upgrade data to the same format as extreme
                    for (const statName in divineUpgrades) {
                        const statArray = divineUpgrades[statName];
                        if (statArray && settings.divineLevel < statArray.length) {
                            const value = statArray[settings.divineLevel];
                            if (value > 0) {
                                divineBonuses.push({ stat: statName, value: value });
                            }
                        }
                    }
                }

                divineContainer.innerHTML = this.formatBonusesList(divineBonuses);
            }
        }

        // Update the chaos bonuses display for accessories
        if (['earring', 'bracelet', 'amulet'].includes(item.type)) {
            const chaosContainer = document.querySelector('.fg-chaos-bonuses');
            if (chaosContainer) {
                const tier = item.chaosTier || 'gold';
                let chaosBonuses = [];

                if (window.EquipmentData && EquipmentData.chaosUpgrades && EquipmentData.chaosUpgrades[tier]) {
                    const chaosUpgrades = EquipmentData.chaosUpgrades[tier];
                    chaosBonuses = settings.chaosLevel > 0 && settings.chaosLevel < chaosUpgrades.length ?
                        chaosUpgrades[settings.chaosLevel] : [];
                }

                chaosContainer.innerHTML = this.formatBonusesList(chaosBonuses);
            }
        }

        // Also update the main stats in the planner
        this.system.updateStats();
    },
};