/**
 * Pet System
 * Handles pet progression with three tiers:
 * - Normal Slot (Lv. 1-10)
 * - Covenant Slot (Lv. 11-20)
 * - Trust Slot (Lv. 21-30)
 */

// Define the system globally to ensure it's always available
window.PetSystem = {
    // Track system state
    isInitialized: false,

    // Cache DOM elements
    elements: {},

    // Current active pet tier
    activeTier: 'normal',

    // Track selected stats for each slot in each tier
    selectedStats: {
        normal: Array(10).fill(null),
        covenant: Array(10).fill(null),
        trust: Array(10).fill(null)
    },

    // Current selected slot for option popup
    currentSlot: null,

    // Selection window manager
    selectionWindow: null,

    /**
     * Initialize the system - this is called by the BuildPlanner core
     * when this system becomes active
     */
    init: function() {
        // Prevent multiple initializations
        if (this.isInitialized) {
            return;
        }

        // Get the system panel
        const panel = document.getElementById('fg-pet-system');
        if (!panel) {
            return;
        }

        // Cache DOM elements for better performance
        this.elements.panel = panel;

        // Initialize UI
        this.initUI();

        // Initialize the selection window manager
        this.selectionWindow = new SelectionWindowManager({
            id: 'fg-pet-stat-selector',
            title: 'Select Pet Attribute',
            className: 'fg-pet-stat-selector',
            fixedPosition: true,
            onSelect: (data) => {
                // Check for both camelCase and lowercase versions of the attribute
                const statId = data.statId || data.statid;
                if (statId) {
                    this.selectStat(statId);
                }
            },
            onClose: () => {
                this.currentSlot = null;
            }
        });

        // Load data from the central store if available
        this.loadFromStore();

        // Setup event listeners
        this.setupEventListeners();

        // Set default tier for other functionality
        this.activeTier = 'normal';

        // Mark as initialized
        this.isInitialized = true;
    },

    /**
     * Initialize the user interface
     */
    initUI: function() {
        // Replace the pet system placeholder with actual content
        this.createPetSystemUI();
    },

    // Create the pet system UI
    createPetSystemUI: function() {
        if (!this.elements.panel) {
            return;
        }

        // Create UI for pet system
        const petSystemHTML = `
            <div class="fg-pet-system-container">
                <!-- All tier content stacked vertically -->
                <div class="fg-pet-tier-content">
                    <!-- Normal tier (Lv. 1-10) -->
                    <div id="normal-tier" class="fg-pet-tier-panel">
                        <span class="fg-pet-tier-header">Normal (Level 1-10)</span>
                        <div class="fg-pet-slots-container" id="normal-slots">
                            ${this.createSlotHTML('normal', 1, 10)}
                        </div>
                    </div>

                    <!-- Covenant tier (Lv. 11-20) -->
                    <div id="covenant-tier" class="fg-pet-tier-panel">
                        <span class="fg-pet-tier-header">Covenant (Level 11-20)</span>
                        <div class="fg-pet-slots-container" id="covenant-slots">
                            ${this.createSlotHTML('covenant', 11, 20)}
                        </div>
                    </div>

                    <!-- Trust tier (Lv. 21-30) -->
                    <div id="trust-tier" class="fg-pet-tier-panel">
                        <span class="fg-pet-tier-header">Trust (Level 21-30)</span>
                        <div class="fg-pet-slots-container" id="trust-slots">
                            ${this.createSlotHTML('trust', 21, 30)}
                        </div>
                    </div>
                </div>

                <!-- Selected stats summary -->
                <div class="fg-pet-selected-stats">
                    <h3>Selected Pet Stats</h3>
                    <div class="fg-pet-selected-list">
                        <!-- Selected stats will be shown here -->
                    </div>
                </div>
            </div>
        `;

        // Replace placeholder with our UI
        this.elements.panel.innerHTML = petSystemHTML;

        // Cache additional elements
        this.elements.selectedList = this.elements.panel.querySelector('.fg-pet-selected-list');
        this.elements.normalSlots = this.elements.panel.querySelector('#normal-slots');
        this.elements.covenantSlots = this.elements.panel.querySelector('#covenant-slots');
        this.elements.trustSlots = this.elements.panel.querySelector('#trust-slots');

        // Initialize Quick Fill Button Manager
        if (typeof QuickFillButtonConfigs !== 'undefined') {
            this.quickFillManager = QuickFillButtonConfigs.initializeSystem('pet', this.elements.panel.querySelector('.fg-pet-system-container'));
        }

        // Set up slot click handlers immediately after creating the UI
        setTimeout(() => {
            this.setupSlotClickHandlers();
        }, 0);
    },

    /**
     * Fill all slots in a tier with a specific stat
     * @param {string} tier - The tier to fill ('normal', 'covenant', 'trust')
     * @param {string} statId - The stat ID to fill slots with
     */
    fillTierWithStat: function(tier, statId) {
        const stat = PetSystemData.tierStats[tier].find(s => s.id === statId);
        if (!stat) return;

        for (let i = 0; i < 10; i++) {
            this.selectedStats[tier][i] = { id: statId, value: stat.value };
            this.updateSlotUI(tier, i);
        }
    },

    // Quick fill endgame setup functionality
    quickFillEndgameSetup: function() {
        // Fill normal tier with Max Crit Rate
        this.fillTierWithStat('normal', 'maxCritRate');

        // Fill covenant and trust tiers with Penetration
        this.fillTierWithStat('covenant', 'penetration');
        this.fillTierWithStat('trust', 'penetration');

        // Update displays and save
        this.updateSelectedStatsDisplay();
        this.updatePetStats();
        this.saveToStore();
    },

    // Create HTML for pet slots
    createSlotHTML: function(tier, startLevel, endLevel) {
        let html = '';

        for (let i = 0; i < 10; i++) {
            const level = startLevel + i;
            const slotIndex = i;
            const hasSelection = this.selectedStats[tier][slotIndex] !== null;

            // Create wrapper for slot and value
            html += `<div class="fg-pet-slot-wrapper">`;

            html += `
                <div class="fg-pet-slot ${hasSelection ? 'selected' : 'empty'}"
                     data-tier="${tier}"
                     data-slot="${slotIndex}"
                     data-level="${level}">
                    <div class="fg-pet-slot-level">${level}</div>
                    <div class="fg-pet-slot-content">
                        ${this.getSlotContentHTML(tier, slotIndex)}
                    </div>
                </div>
            `;

            // Add stat value below slot
            if (hasSelection) {
                const stat = this.selectedStats[tier][slotIndex];
                const statDef = PetSystemData.tierStats[tier].find(s => s.id === stat.id);
                if (statDef) {
                    const suffix = this.getStatSuffix(stat.id);
                    html += `
                        <div class="fg-pet-slot-stat-value-below">
                            +${statDef.value}${suffix}
                        </div>
                    `;
                } else {
                    html += `<div class="fg-pet-slot-stat-value-below"></div>`;
                }
            } else {
                html += `<div class="fg-pet-slot-stat-value-below"></div>`;
            }

            // Close wrapper
            html += `</div>`;
        }

        return html;
    },

    /**
     * Get the content HTML for a slot
     * @param {string} tier - The tier ('normal', 'covenant', 'trust')
     * @param {number} slotIndex - The slot index (0-9)
     * @returns {string} HTML content for the slot
     */
    getSlotContentHTML: function(tier, slotIndex) {
        const stat = this.selectedStats[tier][slotIndex];
        const emptySlotHtml = `<div class="fg-pet-slot-empty"></div>`;

        // Return empty slot if no stat is selected or stat definition is missing
        if (!stat) return emptySlotHtml;

        // Find the stat definition
        const statDef = PetSystemData.tierStats[tier].find(s => s.id === stat.id);
        if (!statDef) return emptySlotHtml;

        // Get stat name and icon from StatsConfig if available
        let statName = stat.id;
        let iconHtml = '';

        if (typeof StatsConfig !== 'undefined') {
            // Get stat name
            const statInfo = StatsConfig.getStatInfo(stat.id);
            if (statInfo) statName = statInfo.name;

            // Get icon HTML
            try {
                const iconUrl = StatsConfig.getStatIconUrl(stat.id);
                iconHtml = `<div class="fg-pet-slot-stat-icon"><img src="${iconUrl}" alt="${statName}" onerror="this.onerror=null; this.style.display='none';"></div>`;
            } catch (e) {
                // Error generating icon - leave iconHtml empty
            }
        }

        return `
            <div class="fg-pet-slot-stat">
                <button class="fg-pet-slot-remove" data-tier="${tier}" data-slot="${slotIndex}" title="Remove attribute">&times;</button>
                ${iconHtml}
                <div class="fg-pet-slot-stat-name" style="display:none;">${statName}</div>
                <div class="fg-pet-slot-stat-value" style="display:none;">+${statDef.value}${this.getStatSuffix(stat.id)}</div>
            </div>
        `;
    },

    // Selection window is now handled by SelectionWindowManager

    /**
     * Show the stat selection popup for a specific slot
     */
    showStatSelectionPopup: function(tier, slotIndex, level) {
        // Set current slot for reference
        this.currentSlot = { tier, slotIndex, level };

        // Populate options from tier stats, sorted alphabetically
        const tierStatOptions = [...PetSystemData.tierStats[tier]].sort((a, b) => {
            // Get stat names for comparison
            let nameA = a.id;
            let nameB = b.id;

            if (typeof StatsConfig !== 'undefined') {
                const infoA = StatsConfig.getStatInfo(a.id);
                const infoB = StatsConfig.getStatInfo(b.id);

                if (infoA) nameA = infoA.name;
                if (infoB) nameB = infoB.name;
            }

            return nameA.localeCompare(nameB);
        });

        // Prepare options for the selection window
        const options = tierStatOptions.map(stat => {
            // Get stat name from StatsConfig
            let statName = stat.id;
            if (typeof StatsConfig !== 'undefined') {
                const statInfo = StatsConfig.getStatInfo(stat.id);
                if (statInfo) {
                    statName = statInfo.name;
                }
            }

            // Get icon HTML with error handling and fallback
            let iconHtml = '';
            if (typeof StatsConfig !== 'undefined') {
                try {
                    const iconUrl = StatsConfig.getStatIconUrl(stat.id);
                    iconHtml = `<img src="${iconUrl}" alt="${statName}" class="fg-selection-option-icon" onerror="this.onerror=null; this.style.display='none';">`;
                } catch (e) {
                    // Error generating icon for option
                }
            }

            return {
                html: `
                    <div class="fg-selection-option-with-icon">
                        ${iconHtml}
                        <span class="fg-selection-option-name">${statName}</span>
                    </div>
                    <span class="fg-selection-option-value">+${stat.value}${this.getStatSuffix(stat.id)}</span>
                `,
                data: {
                    statId: stat.id
                }
            };
        });

        // Show the selection window with a custom title that includes the level
        this.selectionWindow.show({
            title: `Select Attribute for Slot Lv.${level}`,
            options: options
        });
    },

    /**
     * Hide the stat selection popup
     */
    hideStatSelectionPopup: function() {
        this.selectionWindow.hide();
        this.currentSlot = null;
    },

    /**
     * Set up event listeners for user interactions
     */
    setupEventListeners: function() {
        // Set up slot click handlers
        this.setupSlotClickHandlers();

        // Set up event delegation for remove buttons using panel as container
        if (this.elements.panel) {
            this.elements.panel.addEventListener('click', (e) => {
                // Handle remove button clicks
                if (e.target.classList.contains('fg-pet-slot-remove')) {
                    e.stopPropagation(); // Prevent slot click
                    const tier = e.target.getAttribute('data-tier');
                    const slotIndex = parseInt(e.target.getAttribute('data-slot'));
                    this.removeStat(tier, slotIndex);
                }
            });
        }

        // No need for popup event handlers as they're handled by SelectionWindowManager
    },

    /**
     * Set up click handlers for pet slots
     */
    setupSlotClickHandlers: function() {
        // Use event delegation for slot clicks - add handler to each tier container
        const tierContainers = [
            this.elements.normalSlots,
            this.elements.covenantSlots,
            this.elements.trustSlots
        ];

        tierContainers.forEach(container => {
            if (!container) return;

            container.addEventListener('click', (e) => {
                const slot = e.target.closest('.fg-pet-slot');
                if (!slot) return;

                // Don't trigger slot click if the remove button was clicked
                if (e.target.classList.contains('fg-pet-slot-remove') ||
                    e.target.closest('.fg-pet-slot-remove')) {
                    return;
                }

                const tier = slot.getAttribute('data-tier');
                const slotIndex = parseInt(slot.getAttribute('data-slot'));
                const level = parseInt(slot.getAttribute('data-level'));
                this.showStatSelectionPopup(tier, slotIndex, level);
            });
        });
    },

    // Set the active tier (simplified - just tracks current tier for internal use)
    setActiveTier: function(tierId) {
        this.activeTier = tierId;
    },

    // Format large numbers with commas
    formatNumber: function(number) {
        return number.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",");
    },

    /**
     * Save current data to the central store
     */
    saveToStore: function() {
        // Check if BuildSaver exists
        if (window.BuildSaver && typeof BuildSaver.saveBuild === 'function') {
            return true;
        }

        return false;
    },

    /**
     * Select a stat for the current slot
     */
    selectStat: function(statId) {
        if (!this.currentSlot) return;

        const { tier, slotIndex } = this.currentSlot;

        // Find the stat in the tier options
        const statDef = PetSystemData.tierStats[tier].find(s => s.id === statId);
        if (!statDef) return;

        // Update selected stats
        this.selectedStats[tier][slotIndex] = {
            id: statId,
            value: statDef.value
        };

        // Update the slot UI
        this.updateSlotUI(tier, slotIndex);

        // Update selected stats display
        this.updateSelectedStatsDisplay();

        // Update stats in the main planner
        this.updatePetStats();

        // Prepare data for saving but don't auto-save
        this.saveToStore();
    },

    // Update the UI for a specific slot
    updateSlotUI: function(tier, slotIndex) {
        const slotElement = document.querySelector(`.fg-pet-slot[data-tier="${tier}"][data-slot="${slotIndex}"]`);
        if (!slotElement) return;

        const hasSelection = this.selectedStats[tier][slotIndex] !== null;

        // Update class
        if (hasSelection) {
            slotElement.classList.add('selected');
            slotElement.classList.remove('empty');
        } else {
            slotElement.classList.remove('selected');
            slotElement.classList.add('empty');
        }

        // Update content
        const contentElement = slotElement.querySelector('.fg-pet-slot-content');
        if (contentElement) {
            contentElement.innerHTML = this.getSlotContentHTML(tier, slotIndex);
        }

        // Update stat value below slot
        const wrapperElement = slotElement.closest('.fg-pet-slot-wrapper');
        if (wrapperElement) {
            const valueElement = wrapperElement.querySelector('.fg-pet-slot-stat-value-below');
            if (valueElement) {
                if (hasSelection) {
                    const stat = this.selectedStats[tier][slotIndex];
                    const statDef = PetSystemData.tierStats[tier].find(s => s.id === stat.id);
                    if (statDef) {
                        const suffix = this.getStatSuffix(stat.id);
                        valueElement.innerHTML = `+${statDef.value}${suffix}`;
                    } else {
                        valueElement.innerHTML = '';
                    }
                } else {
                    valueElement.innerHTML = '';
                }
            }
        }
    },

    /**
     * Get appropriate suffix for stat display (% for percentage stats)
     * @param {string} statId - The stat ID
     * @returns {string} The suffix to display after the stat value
     */
    getStatSuffix: function(statId) {
        // Return empty string if StatsConfig is not available
        if (typeof StatsConfig === 'undefined') return '';

        // Use getStatInfo method to properly handle PvP/PvE variants
        const statInfo = StatsConfig.getStatInfo(statId);
        if (statInfo && typeof statInfo.isPercentage !== 'undefined') {
            return statInfo.isPercentage ? '%' : '';
        }

        // Fallback: search by name (for backward compatibility with variant handling)
        for (const id in StatsConfig.stats) {
            const baseStatInfo = StatsConfig.getStatInfo(id);
            if (baseStatInfo && baseStatInfo.name === statId) {
                return baseStatInfo.isPercentage ? '%' : '';
            }
        }

        // Default: return empty suffix
        return '';
    },

    /**
     * Combine all selected stats into a single object
     * @returns {Object} Combined stats from all tiers and slots
     */
    getCombinedStats: function() {
        const combinedStats = {};

        // Process each tier
        for (const tier in this.selectedStats) {
            // Add up stats from all slots in this tier
            this.selectedStats[tier].forEach(slotStat => {
                if (!slotStat) return; // Skip empty slots

                const { id, value } = slotStat;

                // Special handling for "allAttackUp" - add to attack and magicAttack
                if (id === 'allAttackUp') {
                    // Add to Attack and Magic Attack
                    combinedStats['attack'] = (combinedStats['attack'] || 0) + value;
                    combinedStats['magicAttack'] = (combinedStats['magicAttack'] || 0) + value;
                    // Still keep allAttackUp for reference
                    combinedStats[id] = (combinedStats[id] || 0) + value;
                } else {
                    // Add to combined stats
                    combinedStats[id] = (combinedStats[id] || 0) + value;
                }
            });
        }

        return combinedStats;
    },

    /**
     * Update the selected stats display
     */
    updateSelectedStatsDisplay: function() {
        if (!this.elements.selectedList) {
            this.elements.selectedList = document.querySelector('.fg-pet-selected-list');
            if (!this.elements.selectedList) return;
        }

        // Get combined stats
        const petStats = this.getCombinedStats();

        // Use StatIntegrationService to generate uniform display
        if (typeof StatIntegrationService !== 'undefined') {
            this.elements.selectedList.innerHTML = StatIntegrationService.createStatSummaryHTML(petStats);
        } else {
            // Simple fallback if service isn't available
            this.elements.selectedList.innerHTML = '<p class="no-stats">No pet stats selected yet.</p>';
        }
    },

    // Update pet stats in the main planner
    updatePetStats: function() {
        // Get combined stats
        const petStats = this.getCombinedStats();

        // Update main planner stats
        if (typeof BuildPlanner !== 'undefined' && BuildPlanner.updateStats) {
            BuildPlanner.updateStats('pet', petStats);
        }
    },

    /**
     * Remove a stat from a slot
     */
    removeStat: function(tier, slotIndex) {
        // Clear the selected stat for this slot
        this.selectedStats[tier][slotIndex] = null;

        // Update the slot UI
        this.updateSlotUI(tier, slotIndex);

        // Update selected stats display
        this.updateSelectedStatsDisplay();

        // Update stats in the main planner
        this.updatePetStats();

        // Prepare data for saving but don't auto-save
        this.saveToStore();
    },

    /**
     * Reset all pet stats
     * - Remove all equipped stats from all tiers
     */
    resetAllStats: function() {
        // Reset all stats for all tiers
        this.selectedStats = {
            normal: Array(10).fill(null),
            covenant: Array(10).fill(null),
            trust: Array(10).fill(null)
        };

        // Update UI for all tiers
        ['normal', 'covenant', 'trust'].forEach(tier => {
            for (let i = 0; i < 10; i++) {
                this.updateSlotUI(tier, i);
            }
        });

        // Update selected stats display
        this.updateSelectedStatsDisplay();

        // Update stats in the main planner
        this.updatePetStats();

        // Save the changes
        this.saveToStore();
    },

    /**
     * Get the essential data for saving
     * Returns just the minimum data needed to restore the system state
     */
    getEssentialData: function() {
        return {
            selectedStats: JSON.parse(JSON.stringify(this.selectedStats))
        };
    },

    /**
     * Load data from the central BuildSaverStore
     */
    loadFromStore: function() {
        // Check if BuildSaverStore exists and has data
        if (window.BuildSaverStore && BuildSaverStore.dataLoaded) {
            // Get data for this system
            const systemData = BuildSaverStore.getSystemData('pet');
            if (systemData && systemData.selectedStats) {
                // Use the saved data
                return this.loadFromData(systemData);
            }
        }

        return false;
    },

    /**
     * Load data directly from provided data object
     * Used by both loadFromStore and the build sharing feature
     */
    loadFromData: function(data) {
        if (data && data.selectedStats) {
            // Use the saved data
            this.selectedStats = JSON.parse(JSON.stringify(data.selectedStats));

            // Update UI to reflect the loaded data
            for (const tier in this.selectedStats) {
                this.selectedStats[tier].forEach((_, index) => {
                    this.updateSlotUI(tier, index);
                });
            }

            // Update summary displays
            this.updateSelectedStatsDisplay();
            this.updatePetStats();

            return true;
        }

        return false;
    }
};

// Safety check to ensure the system is available when needed
document.addEventListener('DOMContentLoaded', function() {
    // Don't auto-initialize - let the BuildPlanner handle initialization
    // when the system tab is clicked instead
});