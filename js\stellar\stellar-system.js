/**
 * Stellar Link System
 * Handles character constellation progression with stat nodes
 * Allows users to select nodes in different constellation lines
 */

// Ensure StellarSystemData is loaded
(function() {
    // Check if StellarSystemData is already loaded (check for enhancedStatData as indicator)
    if (typeof window.enhancedStatData !== 'undefined' && typeof window.nodeColors !== 'undefined') {
        return;
    }

    // Function to load a script dynamically
    function loadScript(url) {
        return new Promise((resolve, reject) => {
            const script = document.createElement('script');
            script.src = url;
            script.onload = resolve;
            script.onerror = reject;
            document.head.appendChild(script);
        });
    }

    // Determine the base URL based on the current script
    const scripts = document.getElementsByTagName('script');
    const currentScript = scripts[scripts.length - 1];
    const currentPath = currentScript.src;

    // Use WordPress plugin URL from global variable instead of attempting to calculate
    const basePath = window.forceguidesPlannerData ? forceguidesPlannerData.pluginUrl : '';

    // Load the stellar-system-data.js file
    loadScript(basePath + 'js/stellar/stellar-system-data.js')
        .then(() => {})
        .catch(error => {});
})();

// Define the system globally to ensure it's always available
window.StellarSystem = {
    // Track system state
    isInitialized: false,

    // Cache DOM elements
    elements: {},

    // Constellation lines
    constellationLines: ['daedalus', 'icarus', 'vulcanos', 'minerva', 'pluto'],

    // Current selected node
    selectedNode: null,

    // Active nodes
    activeNodes: [],

    // Selection window managers
    statSelectionWindow: null,
    colorSelectionWindow: null,

    // Current stat selection data (for two-step process)
    currentStatSelection: null,

    // Node data with coordinates
    nodes: [
        {"id": 1, "line": "daedalus", "raw": {"x": 428, "y": 99}, "normalized": {"x": 0.5336658354114713, "y": 0.12295433291770573}, "currentStat": null, "color": null},
        {"id": 2, "line": "daedalus", "raw": {"x": 479, "y": 190}, "normalized": {"x": 0.5972568578553616, "y": 0.23642066708229426}, "currentStat": null, "color": null},
        {"id": 3, "line": "daedalus", "raw": {"x": 403, "y": 253}, "normalized": {"x": 0.5024937655860349, "y": 0.314974283042394}, "currentStat": null, "color": null},
        {"id": 4, "line": "daedalus", "raw": {"x": 404, "y": 328}, "normalized": {"x": 0.5037406483790524, "y": 0.40849049251870323}, "currentStat": null, "color": null},
        {"id": 5, "line": "icarus", "raw": {"x": 670, "y": 224}, "normalized": {"x": 0.8354114713216958, "y": 0.2788146820448878}, "currentStat": null, "color": null},
        {"id": 6, "line": "icarus", "raw": {"x": 637, "y": 318}, "normalized": {"x": 0.7942643391521197, "y": 0.39602166458852867}, "currentStat": null, "color": null},
        {"id": 7, "line": "icarus", "raw": {"x": 573, "y": 238}, "normalized": {"x": 0.7144638403990025, "y": 0.29627104114713215}, "currentStat": null, "color": null},
        {"id": 8, "line": "icarus", "raw": {"x": 501, "y": 281}, "normalized": {"x": 0.6246882793017456, "y": 0.34988700124688277}, "currentStat": null, "color": null},
        {"id": 9, "line": "icarus", "raw": {"x": 561, "y": 353}, "normalized": {"x": 0.699501246882793, "y": 0.43966256234413964}, "currentStat": null, "color": null},
        {"id": 10, "line": "icarus", "raw": {"x": 483, "y": 377}, "normalized": {"x": 0.6022443890274314, "y": 0.4695877493765586}, "currentStat": null, "color": null},
        {"id": 11, "line": "vulcanos", "raw": {"x": 623, "y": 622}, "normalized": {"x": 0.7768079800498753, "y": 0.7750740336658354}, "currentStat": null, "color": null},
        {"id": 12, "line": "vulcanos", "raw": {"x": 603, "y": 538}, "normalized": {"x": 0.7518703241895262, "y": 0.6703358790523691}, "currentStat": null, "color": null},
        {"id": 13, "line": "vulcanos", "raw": {"x": 716, "y": 503}, "normalized": {"x": 0.8927680798004988, "y": 0.6266949812967582}, "currentStat": null, "color": null},
        {"id": 14, "line": "vulcanos", "raw": {"x": 645, "y": 453}, "normalized": {"x": 0.8042394014962594, "y": 0.5643508416458853}, "currentStat": null, "color": null},
        {"id": 15, "line": "vulcanos", "raw": {"x": 735, "y": 355}, "normalized": {"x": 0.9164588528678305, "y": 0.44215632793017456}, "currentStat": null, "color": null},
        {"id": 16, "line": "vulcanos", "raw": {"x": 569, "y": 429}, "normalized": {"x": 0.7094763092269327, "y": 0.5344256546134664}, "currentStat": null, "color": null},
        {"id": 17, "line": "vulcanos", "raw": {"x": 505, "y": 518}, "normalized": {"x": 0.6296758104738155, "y": 0.64539822319202}, "currentStat": null, "color": null},
        {"id": 18, "line": "vulcanos", "raw": {"x": 453, "y": 458}, "normalized": {"x": 0.5648379052369077, "y": 0.5705852556109726}, "currentStat": null, "color": null},
        {"id": 19, "line": "minerva", "raw": {"x": 500, "y": 681}, "normalized": {"x": 0.6234413965087282, "y": 0.8486401184538653}, "currentStat": null, "color": null},
        {"id": 20, "line": "minerva", "raw": {"x": 500, "y": 602}, "normalized": {"x": 0.6234413965087282, "y": 0.7501363778054863}, "currentStat": null, "color": null},
        {"id": 21, "line": "minerva", "raw": {"x": 411, "y": 548}, "normalized": {"x": 0.5124688279301746, "y": 0.6828047069825436}, "currentStat": null, "color": null},
        {"id": 22, "line": "minerva", "raw": {"x": 386, "y": 693}, "normalized": {"x": 0.48129675810473815, "y": 0.8636027119700748}, "currentStat": null, "color": null},
        {"id": 23, "line": "minerva", "raw": {"x": 325, "y": 611}, "normalized": {"x": 0.40523690773067333, "y": 0.7613583229426434}, "currentStat": null, "color": null},
        {"id": 24, "line": "minerva", "raw": {"x": 238, "y": 655}, "normalized": {"x": 0.2967581047381546, "y": 0.8162211658354115}, "currentStat": null, "color": null},
        {"id": 25, "line": "minerva", "raw": {"x": 253, "y": 579}, "normalized": {"x": 0.31546134663341646, "y": 0.7214580735660848}, "currentStat": null, "color": null},
        {"id": 26, "line": "minerva", "raw": {"x": 135, "y": 574}, "normalized": {"x": 0.16832917705735662, "y": 0.7152236596009975}, "currentStat": null, "color": null},
        {"id": 27, "line": "minerva", "raw": {"x": 305, "y": 517}, "normalized": {"x": 0.3802992518703242, "y": 0.6441513403990025}, "currentStat": null, "color": null},
        {"id": 28, "line": "minerva", "raw": {"x": 355, "y": 460}, "normalized": {"x": 0.442643391521197, "y": 0.5730790211970075}, "currentStat": null, "color": null},
        {"id": 29, "line": "pluto", "raw": {"x": 260, "y": 135}, "normalized": {"x": 0.32418952618453867, "y": 0.16784211346633415}, "currentStat": null, "color": null},
        {"id": 30, "line": "pluto", "raw": {"x": 353, "y": 183}, "normalized": {"x": 0.4401496259351621, "y": 0.22769248753117208}, "currentStat": null, "color": null},
        {"id": 31, "line": "pluto", "raw": {"x": 308, "y": 284}, "normalized": {"x": 0.38403990024937656, "y": 0.3536276496259352}, "currentStat": null, "color": null},
        {"id": 32, "line": "pluto", "raw": {"x": 256, "y": 225}, "normalized": {"x": 0.3192019950124688, "y": 0.28006156483790523}, "currentStat": null, "color": null},
        {"id": 33, "line": "pluto", "raw": {"x": 152, "y": 210}, "normalized": {"x": 0.18952618453865336, "y": 0.2613583229426434}, "currentStat": null, "color": null},
        {"id": 34, "line": "pluto", "raw": {"x": 176, "y": 314}, "normalized": {"x": 0.2194513715710723, "y": 0.3910341334164589}, "currentStat": null, "color": null},
        {"id": 35, "line": "pluto", "raw": {"x": 79, "y": 360}, "normalized": {"x": 0.09850374064837905, "y": 0.44839074189526185}, "currentStat": null, "color": null},
        {"id": 36, "line": "pluto", "raw": {"x": 158, "y": 415}, "normalized": {"x": 0.1970074812967581, "y": 0.516969295511222}, "currentStat": null, "color": null},
        {"id": 37, "line": "pluto", "raw": {"x": 90, "y": 496}, "normalized": {"x": 0.11221945137157108, "y": 0.6179668017456359}, "currentStat": null, "color": null},
        {"id": 38, "line": "pluto", "raw": {"x": 247, "y": 450}, "normalized": {"x": 0.30798004987531175, "y": 0.5606101932668329}, "currentStat": null, "color": null},
        {"id": 39, "line": "pluto", "raw": {"x": 246, "y": 353}, "normalized": {"x": 0.30673316708229426, "y": 0.43966256234413964}, "currentStat": null, "color": null},
        {"id": 40, "line": "pluto", "raw": {"x": 326, "y": 379}, "normalized": {"x": 0.40648379052369077, "y": 0.47208151496259354}, "currentStat": null, "color": null}
    ],

    // Base stats from the system
    baseStats: {},

    /**
     * Quick fill stellar system with popular stat build
     * Automatically fills all nodes with predetermined stats and colors
     */
    quickFillStellarSystem: function() {
        // Popular build definition:
        const popularBuild = {
            // Line 1: all PvE Penetration all purple
            'daedalus': { stat: 'pvePenetration', color: 'emptiness' },
            // Line 2: all PvE crit dmg all purple
            'icarus': { stat: 'pveCritDamage', color: 'emptiness' },
            // Line 3: all all attack up all purple
            'vulcanos': { stat: 'allAttackUp', color: 'emptiness' },
            // Line 4: all penetration all purple
            'minerva': { stat: 'penetration', color: 'emptiness' },
            // Line 5: all crit dmg all purple
            'pluto': { stat: 'critDamage', color: 'emptiness' }
        };

        // Apply the build to each node
        this.nodes.forEach(node => {
            const lineBuild = popularBuild[node.line];
            if (lineBuild) {
                // Set the stat (level 5 for maximum stats)
                node.currentStat = lineBuild.stat;
                node.statLevel = 5;
                node.color = lineBuild.color;
            }
        });

        // Update stats first to populate baseStats
        this.updateStats();

        // Update UI (this will call calculateLineColorBonuses with populated baseStats)
        this.updateNodeSelection();

        // Save to store
        this.saveToStore();
    },

    /**
     * Initialize the system - this is called by the BuildPlanner core
     * when this system becomes active
     */
    init: function() {
        // Prevent multiple initializations
        if (this.isInitialized) {
            return;
        }

        console.log('Initializing Stellar System');

        // Get the system panel
        const panel = document.getElementById('fg-stellar-system');
        if (!panel) {
            console.error('Stellar system panel not found');
            return;
        }

        // Cache DOM elements for better performance
        this.elements.panel = panel;

        // Load data at initialization - return early if data is not loaded
        if (!this.initData()) {
            console.warn('Stellar system data not loaded');
            return;
        }

        // Initialize UI
        this.initUI();

        // Initialize selection windows
        this.initSelectionWindows();
        console.log('Selection windows initialized:', {
            statWindow: this.statSelectionWindow,
            colorWindow: this.colorSelectionWindow
        });

        // Load data from the central store if available
        this.loadFromStore();

        // Setup event listeners
        this.setupEventListeners();

        // Update stats
        this.updateStats();

        // Mark as initialized
        this.isInitialized = true;
        console.log('Stellar System initialized');
    },

    /**
     * Make sure the external data is loaded and log the results
     */
    initData: function() {
        // Check if data is available
        if (window.enhancedStatData && window.nodeColors) {
            return true;
        } else {
            // Set a retry mechanism
            setTimeout(() => {
                if (!this.isInitialized) {
                    this.init();
                }
            }, 500);

            return false;
        }
    },

    /**
     * Initialize the user interface
     */
    initUI: function() {
        // Replace the stellar system placeholder with actual content
        this.createStellarSystemUI();
    },

    /**
     * Initialize the selection windows
     */
    initSelectionWindows: function() {
        // Initialize the stat selection window
        this.statSelectionWindow = new SelectionWindowManager({
            id: 'fg-stellar-stat-selector',
            title: 'Select Node Stat',
            className: 'fg-stellar-stat-selector',
            fixedPosition: true,
            onSelect: (data) => {
                console.log('Stat selected:', data);
                if (data.statId) {
                    // Store the current stat selection for the two-step process
                    this.currentStatSelection = {
                        nodeId: this.selectedNode,
                        statId: data.statId,
                        level: data.level
                    };
                    console.log('Current stat selection:', this.currentStatSelection);

                    // Show the color selection window
                    this.showColorSelection(this.selectedNode);
                } else if (data.action === 'clear') {
                    // Clear the node stat
                    this.setNodeStat(this.selectedNode, null);
                }
            },
            onClose: () => {
                // Reset current selection if closed without selecting
                this.currentStatSelection = null;
            }
        });

        // Initialize the color selection window
        this.colorSelectionWindow = new SelectionWindowManager({
            id: 'fg-stellar-color-selector',
            title: 'Select Node Color',
            className: 'fg-stellar-color-selector',
            fixedPosition: true,
            onSelect: (data) => {
                console.log('Color selected:', data);
                if (data.colorId && this.currentStatSelection) {
                    // Complete the two-step process by setting both stat and color
                    const { nodeId, statId, level } = this.currentStatSelection;
                    console.log('Applying stat and color:', { nodeId, statId, level, colorId: data.colorId });

                    // First set the stat
                    this.setNodeStat(nodeId, statId, level);

                    // Then set the color
                    this.setNodeColor(nodeId, data.colorId);

                    // Reset the current selection
                    this.currentStatSelection = null;
                }
            },
            onClose: () => {
                // If color selection is closed without selecting, we still want to set the stat
                if (this.currentStatSelection) {
                    const { nodeId, statId, level } = this.currentStatSelection;
                    this.setNodeStat(nodeId, statId, level);
                    this.currentStatSelection = null;
                }
            }
        });
    },

    /**
     * Create the stellar system UI
     */
    createStellarSystemUI: function() {
        if (!this.elements.panel) return;

        const container = document.createElement('div');
        container.className = 'fg-stellar-system-container';

        // Main skill tree container
        const skillTreeContainer = document.createElement('div');
        skillTreeContainer.className = 'fg-stellar-skill-tree';

        // Add the background image
        const backgroundImg = document.createElement('div');
        backgroundImg.className = 'fg-stellar-background';
        skillTreeContainer.appendChild(backgroundImg);

        // Create nodes container
        const nodesContainer = document.createElement('div');
        nodesContainer.className = 'fg-stellar-nodes-container';

        // Add nodes to the container
        // Sort nodes by constellation line to ensure consistent overlapping order
        const sortedNodes = [...this.nodes].sort((a, b) => {
            // First sort by line
            if (a.line !== b.line) {
                return this.constellationLines.indexOf(a.line) - this.constellationLines.indexOf(b.line);
            }
            // Then by ID
            return a.id - b.id;
        });

        sortedNodes.forEach(node => {
            const nodeElement = document.createElement('div');
            nodeElement.className = 'fg-stellar-node';
            nodeElement.setAttribute('data-node-id', node.id);
            nodeElement.setAttribute('data-line', node.line);

            // Add a title attribute for better UX
            nodeElement.setAttribute('title', `Node #${node.id} (${this.capitalizeFirstLetter(node.line)})`);

            // Position the node using normalized coordinates with adjustment for larger size
            nodeElement.style.left = `${node.normalized.x * 100}%`;
            nodeElement.style.top = `${node.normalized.y * 100}%`;

            // Add node to container
            nodesContainer.appendChild(nodeElement);
        });

        skillTreeContainer.appendChild(nodesContainer);

        // Create overlay panels that will appear within the skill tree
        // Stat selection panel
        const statSelection = document.createElement('div');
        statSelection.className = 'fg-stellar-stat-selection';
        statSelection.style.display = 'none';
        skillTreeContainer.appendChild(statSelection);

        // Color selection panel
        const colorSelection = document.createElement('div');
        colorSelection.className = 'fg-stellar-color-selection';
        colorSelection.style.display = 'none';
        skillTreeContainer.appendChild(colorSelection);

        container.appendChild(skillTreeContainer);

        // Add summary section with renamed title
        const summarySection = document.createElement('div');
        summarySection.className = 'fg-stellar-color-bonus-section';
        summarySection.innerHTML = '<h3>Stellar System Summary</h3><div class="fg-stellar-color-bonus-content"></div>';
        container.appendChild(summarySection);

        // Replace the panel content with our new UI
        this.elements.panel.innerHTML = '';
        this.elements.panel.appendChild(container);

        // Cache important elements for later use
        this.elements.skillTree = this.elements.panel.querySelector('.fg-stellar-skill-tree');
        this.elements.nodes = this.elements.panel.querySelectorAll('.fg-stellar-node');
        this.elements.statSelection = this.elements.panel.querySelector('.fg-stellar-stat-selection');
        this.elements.colorSelection = this.elements.panel.querySelector('.fg-stellar-color-selection');
        this.elements.colorBonusContent = this.elements.panel.querySelector('.fg-stellar-color-bonus-content');

        // Initialize Quick Fill Button Manager
        if (typeof QuickFillButtonConfigs !== 'undefined') {
            this.quickFillManager = QuickFillButtonConfigs.initializeSystem('stellar', container);
        }
    },

    /**
     * Set up event listeners for UI interactions
     */
    setupEventListeners: function() {
        if (!this.elements.panel) return;

        // Node selection
        this.elements.nodes.forEach(node => {
            node.addEventListener('click', (e) => {
                const nodeId = parseInt(e.currentTarget.getAttribute('data-node-id'));
                this.selectNode(nodeId);
            });
        });

        // Close panels when clicking elsewhere on the skill tree
        this.elements.skillTree.addEventListener('click', (e) => {
            // Don't close if clicking on a node or within selection panels
            if (e.target.closest('.fg-stellar-node') ||
                e.target.closest('.fg-stellar-stat-selection') ||
                e.target.closest('.fg-stellar-color-selection')) {
                return;
            }

            // Hide all panels
            this.hideSelectionPanels();
        });

        // Window resize handler to maintain responsive positioning
        window.addEventListener('resize', () => {
            this.adjustNodePositions();
        });
    },

    /**
     * Adjust node positions on window resize
     */
    adjustNodePositions: function() {
        // This ensures nodes maintain their position relative to the background
        // regardless of screen size
        if (!this.elements.skillTree || !this.elements.nodes) return;

        // Nothing to do here as we're using percentage-based positioning
        // which automatically adjusts with the container size
    },

    /**
     * Select a node
     * @param {number} nodeId - The ID of the node to select
     */
    selectNode: function(nodeId) {
        console.log('Node selected:', nodeId);

        // Find the node data
        const nodeData = this.nodes.find(node => node.id === nodeId);
        if (!nodeData) {
            console.error('Node data not found for ID:', nodeId);
            return;
        }

        // Set the selected node
        this.selectedNode = nodeId;
        console.log('Selected node set to:', this.selectedNode);

        // Update UI
        this.updateNodeSelection();

        // Show stat selection for this node
        this.showStatSelection(nodeId);

        // Save to store
        this.saveToStore();
    },

    /**
     * Update UI to reflect the currently selected node
     */
    updateNodeSelection: function() {
        // Skip if not initialized
        if (!this.elements.nodes) return;

        // Remove selected class from all nodes
        this.elements.nodes.forEach(node => {
            node.classList.remove('selected');
        });

        // Add selected class to the correct node
        if (this.selectedNode) {
            const selectedNode = Array.from(this.elements.nodes)
                .find(node => parseInt(node.getAttribute('data-node-id')) === this.selectedNode);

            if (selectedNode) {
                selectedNode.classList.add('selected');
            }
        }

        // Update active nodes
        this.updateActiveNodes();
    },

    /**
     * Update active nodes based on selected stats
     */
    updateActiveNodes: function() {
        if (!this.elements.nodes) return;

        // Clear existing icons first
        const existingIcons = this.elements.skillTree.querySelectorAll('.fg-stellar-node-icon');
        existingIcons.forEach(icon => icon.remove());

        // Apply active class to nodes with stats
        this.elements.nodes.forEach(nodeElement => {
            const nodeId = parseInt(nodeElement.getAttribute('data-node-id'));
            const nodeData = this.nodes.find(node => node.id === nodeId);

            if (nodeData && nodeData.currentStat) {
                nodeElement.classList.add('active');
                nodeElement.setAttribute('data-stat', nodeData.currentStat);

                // Place icon directly in the container using node coordinates
                this.addStatIconToNode(nodeData);

                // Set color data attribute and apply visual effects
                if (nodeData.color) {
                    nodeElement.setAttribute('data-color', nodeData.color);
                } else {
                    nodeElement.removeAttribute('data-color');
                }

                // Remove any styling that might have been applied
                nodeElement.style.backgroundColor = '';
                nodeElement.style.boxShadow = '';
                nodeElement.style.border = '';
            } else {
                // For empty nodes - remove classes and attributes but no styling
                nodeElement.classList.remove('active');
                nodeElement.removeAttribute('data-stat');
                nodeElement.removeAttribute('data-color');

                // Remove any styling that might have been applied
                nodeElement.style.backgroundColor = '';
                nodeElement.style.boxShadow = '';
                nodeElement.style.border = '';
            }
        });

        // After updating nodes, check for line bonuses
        this.calculateLineColorBonuses();
    },

    /**
     * Add stat icon to the skill tree using node coordinates
     * @param {Object} nodeData - The node data containing coordinates and stat info
     */
    addStatIconToNode: function(nodeData) {
        if (!nodeData || !nodeData.currentStat) return;

        const statId = nodeData.currentStat;
        const line = nodeData.line;

        // Get icon URL from StatsConfig
        let iconUrl = null;
        if (typeof StatsConfig !== 'undefined') {
            iconUrl = StatsConfig.getStatIconUrl(statId);
        }

        // If we have an icon URL, create and add the icon
        if (iconUrl) {
            // Create icon container
            const iconElement = document.createElement('div');
            iconElement.className = 'fg-stellar-node-icon';
            iconElement.setAttribute('data-node-id', nodeData.id);

            // Add color data attribute if available for glow effect
            if (nodeData.color) {
                iconElement.setAttribute('data-color', nodeData.color);
            }

            // Position the icon using the node's normalized coordinates
            iconElement.style.left = `${nodeData.normalized.x * 100}%`;
            iconElement.style.top = `${nodeData.normalized.y * 100}%`;

            // Create image
            const iconImg = document.createElement('img');
            iconImg.src = iconUrl;

            // Set alt and title
            let statName = statId;
            if (typeof StatsConfig !== 'undefined') {
                statName = StatsConfig.getStatInfo(statId).name;
            } else if (line && window.enhancedStatData && window.enhancedStatData[line]) {
                const enhancedStat = window.enhancedStatData[line].find(stat => stat.id === statId);
                if (enhancedStat) {
                    statName = enhancedStat.name;
                }
            }

            iconImg.alt = statName;
            iconImg.title = statName;

            // Create a level indicator with the actual stat value
            const level = nodeData.statLevel || 1;
            const levelIndicator = document.createElement('div');
            levelIndicator.className = 'fg-stellar-level-indicator';

            // Get the stat value from enhancedStatData
            if (window.enhancedStatData && window.enhancedStatData[line]) {
                const stat = window.enhancedStatData[line].find(s => s.id === statId);
                if (stat && stat.values[level-1]) {
                    let value = stat.values[level-1];
                    // Do NOT apply color bonus here - color bonuses are separate stats
                    levelIndicator.textContent = value + (stat.isPercentage ? '%' : '');
                } else {
                    levelIndicator.textContent = level;
                }
            } else {
                levelIndicator.textContent = level;
            }

            iconElement.appendChild(levelIndicator);

            // Add image to container and container to node container
            iconElement.appendChild(iconImg);
            this.elements.skillTree.querySelector('.fg-stellar-nodes-container').appendChild(iconElement);
        }
    },

    /**
     * Show stat selection UI for a node
     * @param {number} nodeId - The ID of the node
     */
    showStatSelection: function(nodeId) {
        // Find the node data
        const nodeData = this.nodes.find(node => node.id === nodeId);
        if (!nodeData) return;

        // No manual positioning needed - using fixed center positioning

        // Get the constellation line for this node
        const line = nodeData.line;

        // Get the current stat and level if any
        const currentStatId = nodeData.currentStat;
        const currentLevel = nodeData.statLevel || 1;

        // Prepare options for the selection window
        const options = [];

        // Get stats for this constellation line
        if (window.enhancedStatData && window.enhancedStatData[line]) {
            const lineStats = window.enhancedStatData[line];

            // Add options for each stat type
            lineStats.forEach(stat => {
                // Get icon if available
                let iconHtml = '';
                if (typeof StatsConfig !== 'undefined') {
                    const iconUrl = StatsConfig.getStatIconUrl(stat.id);
                    iconHtml = `<img src="${iconUrl}" alt="${stat.name}" class="fg-selection-option-icon" />`;
                } else if (stat.icon) {
                    // Fallback to stat.icon if available
                    iconHtml = `<img src="${stat.icon}" alt="${stat.name}" class="fg-selection-option-icon" />`;
                }

                // We don't need category headers for each stat type

                // Create options for each tier of this stat
                for (let i = 0; i < stat.values.length; i++) {
                    const level = i + 1;
                    const value = stat.values[i];
                    const formattedValue = stat.isPercentage ? `${value}%` : value;
                    const isSelected = currentStatId === stat.id && currentLevel === level;

                    options.push({
                        html: `
                            <div class="fg-selection-option-with-icon">
                                ${iconHtml}
                                <span class="fg-selection-option-level">+${level}</span>
                                <span class="fg-selection-option-name">${stat.name}</span>
                            </div>
                            <span class="fg-selection-option-value">+${formattedValue}</span>
                        `,
                        className: isSelected ? 'selected' : '',
                        data: {
                            statId: stat.id,
                            level: level
                        }
                    });
                }

                // We don't need separators between stat groups
            });
        } else {
            options.push({
                html: `<div class="fg-selection-empty-state">No stats available - Data file not loaded!</div>`,
                data: {}
            });
        }

        // Add clear option if a stat is already selected
        if (nodeData.currentStat) {
            options.push({
                html: `
                    <div class="fg-selection-option-with-icon">
                        <span class="fg-selection-option-name">Clear Selection</span>
                    </div>
                `,
                className: 'fg-selection-clear-option',
                data: {
                    action: 'clear'
                }
            });
        }

        // Show the selection window centered on screen
        this.statSelectionWindow.show({
            title: `Select Stat for Node ${nodeId} (${this.capitalizeFirstLetter(nodeData.line)})`,
            options: options
        });
    },

    /**
     * Set a stat for a node
     * @param {number} nodeId - The ID of the node
     * @param {string|null} statId - The ID of the stat, or null to clear
     * @param {number} [level] - The level of the stat (1-5), defaults to 1
     */
    setNodeStat: function(nodeId, statId, level = 1) {
        console.log('Setting node stat:', { nodeId, statId, level });

        // Find the node data
        const nodeIndex = this.nodes.findIndex(node => node.id === nodeId);
        if (nodeIndex === -1) {
            console.error('Node not found with ID:', nodeId);
            return;
        }

        // Update the node's stat and level
        this.nodes[nodeIndex].currentStat = statId;
        this.nodes[nodeIndex].statLevel = statId ? level : null;

        // If clearing the stat, also clear the color and hide panels
        if (statId === null) {
            this.nodes[nodeIndex].color = null;
            this.nodes[nodeIndex].statLevel = null;

            // Hide selection windows
            if (this.statSelectionWindow) {
                this.statSelectionWindow.hide();
            }
            if (this.colorSelectionWindow) {
                this.colorSelectionWindow.hide();
            }
        }

        // Find the node element and update its attributes
        if (this.elements.nodes) {
            const nodeElement = Array.from(this.elements.nodes).find(el =>
                parseInt(el.getAttribute('data-node-id')) === nodeId
            );

            if (nodeElement) {
                if (statId) {
                    nodeElement.classList.add('active');
                    nodeElement.setAttribute('data-stat', statId);
                    nodeElement.setAttribute('data-stat-level', level);
                } else {
                    nodeElement.classList.remove('active');
                    nodeElement.removeAttribute('data-stat');
                    nodeElement.removeAttribute('data-stat-level');
                }
            }
        }

        // Update UI to refresh all node icons
        this.updateNodeSelection();

        // If we're not clearing and this is a direct call (not part of the two-step process),
        // first hide stat selection window, then show color selection
        if (statId !== null && !this.currentStatSelection) {
            // Hide stat selection window before showing color selection
            if (this.statSelectionWindow) {
                this.statSelectionWindow.hide();
            }

            // Show color selection window
            this.showColorSelection(nodeId);
        } else {
            // Update stats immediately if clearing
            this.updateStats();

            // Calculate line color bonuses to update the summary display
            this.calculateLineColorBonuses();

            // Save to store
            this.saveToStore();
        }
    },

    /**
     * Show color selection UI for a node
     * @param {number} nodeId - The ID of the node
     */
    showColorSelection: function(nodeId) {
        console.log('Showing color selection for node:', nodeId);

        // Find the node data
        const nodeData = this.nodes.find(node => node.id === nodeId);
        if (!nodeData) {
            console.error('Node data not found for ID:', nodeId);
            return;
        }

        // No manual positioning needed - using fixed center positioning

        // Prepare options for the color selection window
        const options = [];

        // Use colors from data file only
        if (window.nodeColors) {
            // Add options for all available colors
            Object.entries(window.nodeColors).forEach(([colorId, colorData]) => {
                const isSelected = nodeData.color === colorId;
                const colorName = colorData.name;
                const cssColor = colorData.cssColor;
                const borderColor = colorData.borderColor;

                options.push({
                    html: `
                        <div class="fg-selection-option-with-icon" style="border-left: 3px solid ${cssColor}">
                            <span class="fg-selection-option-name">${colorName}</span>
                            <span class="fg-selection-option-color-preview" style="background-color: ${cssColor}; border: 1px solid ${borderColor};"></span>
                        </div>
                    `,
                    className: isSelected ? 'selected' : '',
                    data: {
                        colorId: colorId
                    }
                });
            });
        } else {
            options.push({
                html: `<div class="fg-selection-empty-state">No colors available - Data file not loaded!</div>`,
                data: {}
            });
        }

        // Show the color selection window centered on screen
        this.colorSelectionWindow.show({
            title: `Select Color for Node ${nodeId}`,
            options: options
        });
    },

    /**
     * Set a color for a node
     * @param {number} nodeId - The ID of the node
     * @param {string} colorId - The ID of the color
     */
    setNodeColor: function(nodeId, colorId) {
        // Find the node data
        const nodeIndex = this.nodes.findIndex(node => node.id === nodeId);
        if (nodeIndex === -1) return;

        // Update the node's color
        this.nodes[nodeIndex].color = colorId;

        // Find the node element and update its attributes
        if (this.elements.nodes) {
            const nodeElement = Array.from(this.elements.nodes).find(el =>
                parseInt(el.getAttribute('data-node-id')) === nodeId
            );

            if (nodeElement) {
                nodeElement.setAttribute('data-color', colorId);
            }
        }

        // Hide color selection window
        if (this.colorSelectionWindow) {
            this.colorSelectionWindow.hide();
        }

        // Update UI to refresh all node icons
        this.updateNodeSelection();

        // Update stats to include line bonuses
        this.updateStats();

        // Calculate line color bonuses to update the summary display
        this.calculateLineColorBonuses();

        // Save to store
        this.saveToStore();
    },

    /**
     * Hide all selection windows
     */
    hideSelectionPanels: function() {
        if (this.statSelectionWindow) {
            this.statSelectionWindow.hide();
        }
        if (this.colorSelectionWindow) {
            this.colorSelectionWindow.hide();
        }
    },

    /**
     * Get essential data for saving
     * Returns just the minimum data needed to restore the system state
     */
    getEssentialData: function() {
        // Create a simplified version of nodes with only id, currentStat, statLevel, and color
        const savedNodes = this.nodes.map(node => {
            // Only save nodes that have stats assigned
            if (node.currentStat) {
                return {
                    id: node.id,
                    currentStat: node.currentStat,
                    statLevel: node.statLevel || 1,
                    color: node.color
                };
            }
            return null;
        }).filter(node => node !== null);

        // Return only the essential data needed to restore state
        // We don't need to save constellation bonuses as they are derived from node colors
        return {
            nodes: savedNodes,
            selectedNode: this.selectedNode
        };
    },

    /**
     * Load data from the central store
     */
    loadFromStore: function() {
        // Only load if BuildSaverStore is available
        if (typeof BuildSaverStore === 'undefined') {
            return;
        }

        // Get data from store with the consistent hyphenated ID
        const storedData = BuildSaverStore.getSystemData('stellar');
        if (!storedData) {
            return;
        }

        // Use the common loadFromData method
        return this.loadFromData(storedData);
    },

    /**
     * Load data directly from provided data object
     * Used by both loadFromStore and the build sharing feature
     */
    loadFromData: function(data) {
        if (!data) {
            return false;
        }

        // First reset all nodes
        this.nodes.forEach(node => {
            node.currentStat = null;
            node.statLevel = null;
            node.color = null;
        });

        // Apply stored node data
        if (data.nodes && Array.isArray(data.nodes)) {
            data.nodes.forEach(savedNode => {
                if (savedNode.id && savedNode.currentStat) {
                    const nodeIndex = this.nodes.findIndex(node => node.id === savedNode.id);
                    if (nodeIndex !== -1) {
                        this.nodes[nodeIndex].currentStat = savedNode.currentStat;
                        this.nodes[nodeIndex].statLevel = savedNode.statLevel || 1;
                        this.nodes[nodeIndex].color = savedNode.color || null;
                    }
                }
            });
        }

        // Apply selected node
        if (data.selectedNode) {
            this.selectedNode = data.selectedNode;
        }

        // Update UI
        this.updateNodeSelection();

        // Update stats after loading data
        this.updateStats();

        // Important: Explicitly calculate line color bonuses to update the summary display
        this.calculateLineColorBonuses();

        return true;
    },

    /**
     * Save data to the central store
     * Note: This method doesn't actually trigger a save, it just prepares data
     * The actual save is triggered by the user clicking the Save Build button
     */
    saveToStore: function() {
        // Check if BuildSaverStore exists
        if (typeof BuildSaverStore !== 'undefined') {
            return true;
        }

        return false;
    },

    /**
     * Reset all stellar nodes
     * - Remove all stats and colors from all nodes
     */
    resetAllNodes: function() {
        // Reset all nodes
        this.nodes.forEach(node => {
            node.currentStat = null;
            node.statLevel = null;
            node.color = null;
        });

        // Clear selected node
        this.selectedNode = null;

        // Update UI
        this.updateNodeSelection();

        // Update stats
        this.updateStats();

        // Recalculate line color bonuses
        this.calculateLineColorBonuses();

        // Save the changes
        this.saveToStore();
    },

    /**
     * Calculate line color bonuses based on active nodes with same color
     * Adds bonus stats when all nodes in a constellation line have the same color
     */
    calculateLineColorBonuses: function() {
        // Initialize line bonus stats
        this.lineBonusStats = {};

        // Need both lineEffects and nodeColors data
        if (!window.lineEffects || !window.nodeColors) {
            return;
        }

        // Clear previous bonus display
        if (this.elements.colorBonusContent) {
            this.elements.colorBonusContent.innerHTML = '';
        }

        // Track if any bonuses were found
        let foundBonuses = false;
        let bonusHTML = '';

        // Process each constellation line
        this.constellationLines.forEach(line => {
            // Get all nodes for this line
            const lineNodes = this.nodes.filter(node => node.line === line);

            // Only proceed if we have nodes with stats
            const activeNodes = lineNodes.filter(node => node.currentStat && node.color);
            if (activeNodes.length === 0) return;

            // Check if all active nodes have the same color
            const firstColor = activeNodes[0].color;
            const allSameColor = activeNodes.every(node => node.color === firstColor);

            // Apply line bonus if all nodes have the same color and we have a minimum number of nodes
            if (allSameColor && activeNodes.length >= 3) {
                foundBonuses = true;

                // Get the effect for this line and color
                const effect = window.lineEffects[line][firstColor];
                if (!effect) return;

                // Get color name
                const colorName = window.nodeColors[firstColor]?.name || firstColor;

                // Start line bonus HTML - more compact version
                bonusHTML += `<div class="fg-stellar-line-bonus">
                    <span class="fg-stellar-line-name">${this.capitalizeFirstLetter(line)}</span>
                    <span class="fg-stellar-line-color" style="color:${window.nodeColors[firstColor]?.cssColor || '#fff'}">
                        ${colorName}:
                    </span>`;

                // Add the bonus stats inline
                let effectsAdded = false;
                if (effect.effect1 && effect.effect1.statId) {
                    const statId = effect.effect1.statId;
                    const value = effect.effect1.value;

                    // Get stat name and isPercentage from StatsConfig
                    let statName = statId;
                    let isPercentage = false;
                    if (typeof StatsConfig !== 'undefined') {
                        const statInfo = StatsConfig.getStatInfo(statId);
                        if (statInfo) {
                            statName = statInfo.name;
                            isPercentage = statInfo.isPercentage;
                        }
                    }

                    // Add to bonus HTML
                    bonusHTML += `<span class="fg-stellar-line-effect">
                        ${statName} +${value}${isPercentage ? '%' : ''}
                    </span>`;
                    effectsAdded = true;

                    if (this.lineBonusStats[statId]) {
                        this.lineBonusStats[statId] += value;
                    } else {
                        this.lineBonusStats[statId] = value;
                    }
                }

                if (effect.effect2 && effect.effect2.statId) {
                    const statId = effect.effect2.statId;
                    const value = effect.effect2.value;

                    // Get stat name and isPercentage from StatsConfig
                    let statName = statId;
                    let isPercentage = false;
                    if (typeof StatsConfig !== 'undefined') {
                        const statInfo = StatsConfig.getStatInfo(statId);
                        if (statInfo) {
                            statName = statInfo.name;
                            isPercentage = statInfo.isPercentage;
                        }
                    }

                    // Add comma if we already added an effect
                    if (effectsAdded) {
                        bonusHTML += ', ';
                    }

                    // Add to bonus HTML
                    bonusHTML += `<span class="fg-stellar-line-effect">
                        ${statName} +${value}${isPercentage ? '%' : ''}
                    </span>`;

                    if (this.lineBonusStats[statId]) {
                        this.lineBonusStats[statId] += value;
                    } else {
                        this.lineBonusStats[statId] = value;
                    }
                }

                // Close line bonus HTML
                bonusHTML += `</div>`;
            }
        });

        // Update the bonus display
        if (this.elements.colorBonusContent) {
            // Build the complete content with both sections
            let completeHTML = '';

            // First add constellation bonuses section
            completeHTML += '<div class="fg-stellar-constellation-bonuses">';
            completeHTML += '<h4>Constellation Color Bonuses</h4>';
            if (foundBonuses) {
                completeHTML += bonusHTML;
            } else {
                completeHTML += '<p>No color bonuses active yet. Complete constellations with the same color to activate bonuses.</p>';
            }
            completeHTML += '</div>';

            // Then add node stats summary
            completeHTML += '<div class="fg-stellar-stats-summary">';
            completeHTML += '<h4>Node Stats Summary</h4>';

            // Use StatIntegrationService for consistent stat display
            if (typeof StatIntegrationService !== 'undefined' && Object.keys(this.baseStats).length > 0) {
                completeHTML += StatIntegrationService.createStatSummaryHTML(this.baseStats);
            } else if (Object.keys(this.baseStats).length > 0) {
                // Fallback to basic summary if StatIntegrationService is not available
                for (const statId in this.baseStats) {
                    const value = this.baseStats[statId];
                    const statName = this.getStatName(statId);
                    const isPercentage = typeof StatsConfig !== 'undefined' ?
                        StatsConfig.getStatInfo(statId).isPercentage : false;

                    completeHTML += `<div class="fg-stellar-stat-item">
                        <span class="fg-stellar-stat-name">${statName}</span>
                        <span class="fg-stellar-stat-value">+${value}${isPercentage ? '%' : ''}</span>
                    </div>`;
                }
            } else {
                completeHTML += '<p>No stats from nodes yet. Select nodes and assign stats to see summary.</p>';
            }

            completeHTML += '</div>';

            // Set the complete HTML
            this.elements.colorBonusContent.innerHTML = completeHTML;
        }
    },

    /**
     * Calculate stats based on active nodes
     */
    calculateStats: function() {
        // Initialize empty stats object
        const stats = {};

        // Go through all nodes with stats assigned
        this.nodes.forEach(node => {
            if (node.currentStat) {
                const statId = node.currentStat;
                let statValue = 0;

                // Use statLevel if available, default to 1
                const level = node.statLevel || 1;
                const levelIndex = level - 1; // Convert to 0-based index

                // Check for enhanced stat data
                if (window.enhancedStatData && window.enhancedStatData[node.line]) {
                    const enhancedStat = window.enhancedStatData[node.line].find(stat => stat.id === statId);

                    if (enhancedStat) {
                        // Use the value from the selected level
                        statValue = enhancedStat.values[levelIndex];

                        // Get percentage flag from StatsConfig
                        const isPercentage = typeof StatsConfig !== 'undefined' ?
                            StatsConfig.getStatInfo(statId).isPercentage : false;

                        // Round values to avoid floating point issues
                        statValue = Math.round(statValue * 100) / 100;

                        // Add stat value to total
                        if (stats[statId]) {
                            stats[statId] += statValue;
                        } else {
                            stats[statId] = statValue;
                        }
                    }
                }
            }
        });

        // Add line bonus stats
        if (this.lineBonusStats) {
            for (const statId in this.lineBonusStats) {
                const bonusValue = this.lineBonusStats[statId];

                if (stats[statId]) {
                    stats[statId] += bonusValue;
                } else {
                    stats[statId] = bonusValue;
                }
            }
        }

        return stats;
    },

    /**
     * Update global stats
     */
    updateStats: function() {
        // Calculate stats from active nodes
        const calculatedStats = this.calculateStats();

        // Save calculated stats
        this.baseStats = calculatedStats;

        // Skip if BuildPlanner is not available
        if (typeof BuildPlanner === 'undefined') {
            return;
        }

        // Send stats to BuildPlanner
        BuildPlanner.updateStats('stellar', calculatedStats);
    },

    /**
     * Get the name of a stat by ID
     * @param {string} statId - The ID of the stat
     * @returns {string} - The stat name
     */
    getStatName: function(statId) {
        // Use StatsConfig as primary source for stat names
        if (typeof StatsConfig !== 'undefined') {
            return StatsConfig.getStatInfo(statId).name;
        }

        // Fall back to enhancedStatData if StatsConfig is not available
        if (window.enhancedStatData) {
            for (const line in window.enhancedStatData) {
                const stat = window.enhancedStatData[line].find(s => s.id === statId);
                if (stat) return stat.name;
            }
        }

        return statId || 'Unknown';
    },

    /**
     * Get the name of a color by ID
     * @param {string} colorId - The ID of the color
     * @returns {string} - The color name
     */
    getColorName: function(colorId) {
        // Try to get the color from the data file
        if (window.nodeColors && window.nodeColors[colorId]) {
            return window.nodeColors[colorId].name;
        }

        return colorId || 'Unknown';
    },

    /**
     * Helper function to capitalize the first letter of a string
     * @param {string} string - The input string
     * @returns {string} - The string with the first letter capitalized
     */
    capitalizeFirstLetter: function(string) {
        return string.charAt(0).toUpperCase() + string.slice(1);
    }
};