/**
 * Overlord Mastery System Data
 * Contains data for the Overlord Mastery system that unlocks at level 200
 * Defines skills and their stats for both attack and defense categories
 * Based on actual game data from the images provided
 */

window.OverlordMasteryData = {
    // System configuration
    unlockLevel: 200,

    // Categories for skills
    categories: {
        attack: {
            name: "Attack",
            id: "attack"
        },
        defense: {
            name: "Defense",
            id: "defense"
        }
    },

    // All skills grouped by category with grid positions
    skills: {
        // Attack skills - 4x4 grid layout based on game image
        attack: [
            // Column 1: All Attack skills
            {
                id: "allAttackI",
                name: "All Attack I",
                statId: "allAttackUp",
                maxLevel: 5,
                tier: 1,
                gridPosition: { row: 1, col: 1 },
                opRequired: 1,
                values: [2, 4, 6, 8, 10]
            },
            {
                id: "allAttackII",
                name: "All Attack II",
                statId: "allAttackUp",
                maxLevel: 5,
                tier: 2,
                gridPosition: { row: 2, col: 1 },
                opRequired: 2,
                values: [3, 6, 9, 12, 15]
            },
            {
                id: "allAttackIII",
                name: "All Attack III",
                statId: "allAttackUp",
                maxLevel: 5,
                tier: 4,
                gridPosition: { row: 4, col: 1 },
                opRequired: 3,
                values: [4, 8, 12, 16, 20]
            },

            // Column 2: Critical DMG and Ignore Resist Crit Rate
            {
                id: "critDmgI",
                name: "Critical DMG I",
                statId: "critDamage",
                maxLevel: 5,
                tier: 1,
                gridPosition: { row: 1, col: 2 },
                opRequired: 2,
                isPercentage: true,
                values: [1, 2, 3, 4, 5]
            },
            {
                id: "critDmgII",
                name: "Critical DMG II",
                statId: "critDamage",
                maxLevel: 3,
                tier: 2,
                gridPosition: { row: 2, col: 2 },
                opRequired: 3,
                isPercentage: true,
                values: [2, 4, 6]
            },
            {
                id: "ignoreResistCritRate",
                name: "Ignore Resist Critical Rate",
                statId: "ignoreResistCritRate",
                maxLevel: 5,
                tier: 3,
                gridPosition: { row: 3, col: 2 },
                opRequired: 4,
                values: [10, 20, 30, 40, 50]
            },

            // Column 3: Attack Rate and Accuracy
            {
                id: "attackRateI",
                name: "Attack Rate I",
                statId: "attackRate",
                maxLevel: 5,
                tier: 1,
                gridPosition: { row: 1, col: 3 },
                opRequired: 1,
                values: [20, 40, 60, 80, 100]
            },
            {
                id: "accuracyI",
                name: "Accuracy I",
                statId: "accuracy",
                maxLevel: 5,
                tier: 2,
                gridPosition: { row: 2, col: 3 },
                opRequired: 1,
                values: [40, 60, 80, 100, 120]
            },
            {
                id: "attackRateII",
                name: "Attack Rate II",
                statId: "attackRate",
                maxLevel: 5,
                tier: 3,
                gridPosition: { row: 3, col: 3 },
                opRequired: 2,
                values: [30, 60, 90, 120, 150]
            },
            {
                id: "accuracyII",
                name: "Accuracy II",
                statId: "accuracy",
                maxLevel: 5,
                tier: 4,
                gridPosition: { row: 4, col: 3 },
                opRequired: 5,
                values: [50, 70, 90, 110, 130]
            },

            // Column 4: Add DMG and Penetration
            {
                id: "addDmgI",
                name: "Add. DMG I",
                statId: "addDamage",
                maxLevel: 5,
                tier: 1,
                gridPosition: { row: 1, col: 4 },
                opRequired: 1,
                values: [2, 4, 6, 8, 10]
            },
            {
                id: "penetrationI",
                name: "Penetration I",
                statId: "penetration",
                maxLevel: 5,
                tier: 2,
                gridPosition: { row: 2, col: 4 },
                opRequired: 2,
                values: [2, 4, 6, 8, 10]
            },
            {
                id: "addDmgII",
                name: "Add. DMG II",
                statId: "addDamage",
                maxLevel: 5,
                tier: 3,
                gridPosition: { row: 3, col: 4 },
                opRequired: 5,
                values: [3, 6, 9, 12, 15]
            },
            {
                id: "penetrationII",
                name: "Penetration II",
                statId: "penetration",
                maxLevel: 3,
                tier: 4,
                gridPosition: { row: 4, col: 4 },
                opRequired: 10,
                values: [3, 6, 9]
            }
        ],

        // Defense skills - 4x4 grid layout based on game image
        defense: [
            // Column 1: Defense skills
            {
                id: "defenseI",
                name: "Defense I",
                statId: "defense",
                maxLevel: 5,
                tier: 1,
                gridPosition: { row: 1, col: 1 },
                opRequired: 1,
                values: [3, 6, 9, 12, 15]
            },
            {
                id: "dmgReduction",
                name: "DMG Reduction",
                statId: "damageReduce",
                maxLevel: 5,
                tier: 2,
                gridPosition: { row: 2, col: 1 },
                opRequired: 2,
                values: [5, 10, 15, 20, 25]
            },
            {
                id: "defenseII",
                name: "Defense II",
                statId: "defense",
                maxLevel: 5,
                tier: 4,
                gridPosition: { row: 4, col: 1 },
                opRequired: 3,
                values: [15, 20, 25, 30, 35]
            },

            // Column 2: HP and Resist Critical DMG
            {
                id: "hpUpI",
                name: "HP UP I",
                statId: "hp",
                maxLevel: 5,
                tier: 1,
                gridPosition: { row: 1, col: 2 },
                opRequired: 1,
                values: [20, 40, 60, 80, 100]
            },
            {
                id: "resistCritDmg",
                name: "Resist Critical DMG",
                statId: "resistCritDmg",
                maxLevel: 5,
                tier: 2,
                gridPosition: { row: 2, col: 2 },
                opRequired: 2,
                isPercentage: true,
                values: [4, 8, 12, 16, 20]
            },
            {
                id: "hpUpII",
                name: "HP UP II",
                statId: "hp",
                maxLevel: 5,
                tier: 3,
                gridPosition: { row: 3, col: 2 },
                opRequired: 3,
                values: [20, 40, 60, 80, 100]
            },
            // Column 3: Resist skills
            {
                id: "resistKnockback",
                name: "Resist Knockback",
                statId: "resistKnockback",
                maxLevel: 5,
                tier: 1,
                gridPosition: { row: 1, col: 3 },
                opRequired: 1,
                isPercentage: true,
                values: [2, 4, 6, 8, 10]
            },
            {
                id: "resistDown",
                name: "Resist Down",
                statId: "resistDown",
                maxLevel: 5,
                tier: 2,
                gridPosition: { row: 2, col: 3 },
                opRequired: 2,
                isPercentage: true,
                values: [2, 4, 6, 8, 10]
            },
            {
                id: "resistStun",
                name: "Resist Stun",
                statId: "resistStun",
                maxLevel: 5,
                tier: 3,
                gridPosition: { row: 3, col: 3 },
                opRequired: 3,
                isPercentage: true,
                values: [2, 4, 6, 8, 10]
            },
            {
                id: "resistSkillAmp",
                name: "Resist Skill Amp.",
                statId: "resistSkillAmp",
                maxLevel: 5,
                tier: 4,
                gridPosition: { row: 4, col: 3 },
                opRequired: 5,
                isPercentage: true,
                values: [3, 6, 9, 12, 15]
            },

            // Column 4: Ignore Accuracy and Ignore Penetration
            {
                id: "ignoreAccuracyI",
                name: "Ignore Accuracy I",
                statId: "ignoreAccuracy",
                maxLevel: 5,
                tier: 1,
                gridPosition: { row: 1, col: 4 },
                opRequired: 1,
                values: [40, 60, 80, 100, 120]
            },
            {
                id: "ignorePenetrationI",
                name: "Ignore Penetration I",
                statId: "ignorePenetration",
                maxLevel: 5,
                tier: 2,
                gridPosition: { row: 2, col: 4 },
                opRequired: 3,
                values: [5, 10, 15, 20, 25]
            },
            {
                id: "ignoreAccuracyII",
                name: "Ignore Accuracy II",
                statId: "ignoreAccuracy",
                maxLevel: 5,
                tier: 3,
                gridPosition: { row: 3, col: 4 },
                opRequired: 5,
                values: [50, 70, 90, 110, 130]
            },
            {
                id: "ignorePenetrationII",
                name: "Ignore Penetration II",
                statId: "ignorePenetration",
                maxLevel: 3,
                tier: 4,
                gridPosition: { row: 4, col: 4 },
                opRequired: 10,
                values: [5, 10, 15]
            }
        ]
    },

    // Get skill value at specific level
    getSkillValueAtLevel: function(skill, level) {
        if (!skill || level <= 0) return 0;
        if (level > skill.maxLevel) level = skill.maxLevel;

        // Return the value from the values array (level is 1-based, array is 0-based)
        return skill.values[level - 1] || 0;
    },

    // Get all skills as a flat array
    getAllSkills: function() {
        const allSkills = [];
        for (const category in this.skills) {
            allSkills.push(...this.skills[category]);
        }
        return allSkills;
    },

    // Get skill by ID
    getSkillById: function(skillId) {
        const allSkills = this.getAllSkills();
        return allSkills.find(skill => skill.id === skillId);
    },

    // Get skills by category
    getSkillsByCategory: function(category) {
        return this.skills[category] || [];
    }
};