/**
 * Stats System
 * Handles character class selection and base attribute distribution
 * Calculates derived stats based on STR, INT, DEX and class
 */

// Define the system globally to ensure it's always available
window.ClassSystem = {
    // Track system state
    isInitialized: false,

    // Cache DOM elements
    elements: {},

    // Current selected class
    selectedClass: null,

    // Available classes
    classes: [
        { id: 'warrior', name: 'Warrior' },
        { id: 'wizard', name: '<PERSON>' },
        { id: 'blader', name: '<PERSON><PERSON>' },
        { id: 'dark-mage', name: 'Dark Mage' },
        { id: 'gladiator', name: 'Gladiator' },
        { id: 'force-gunner', name: 'Force Gunner' },
        { id: 'force-blader', name: 'Force Blader' },
        { id: 'force-archer', name: '<PERSON> Archer' },
        { id: 'force-shielder', name: 'Force Shielder' }
    ],

    // Base stats and distribution at level 200
    baseStats: {
        level: 200,
        baseStr: 10,
        baseInt: 10,
        baseDex: 10,
        totalStatPoints: 1379, // Points to distribute
        currentStr: 10,
        currentInt: 10,
        currentDex: 10,
        remainingPoints: 1379
    },

    // Stat ranges for scaling calculation
    statRanges: [
        { min: 0, max: 100 },
        { min: 101, max: 1000 },
        { min: 1001, max: 2000 },
        { min: 2001, max: 999999 }
    ],

    // Class-specific stat scaling
    classScaling: {
        'warrior': {
            str: {
                hp: [1.2, 1.0, 0.8, 0.5],
                attack: [0.8, 0.6, 0.4, 0.2],
                damageReduce: [0.25, 0.2, 0.15, 0.1],
                ignorePenetration: [0.3, 0.25, 0.2, 0.15]
            },
            int: {
                magicAttack: [0.3, 0.25, 0.2, 0.15],
                resistCritRate: [0.06, 0.05, 0.04, 0.03],
                resistCritDmg: [0.12, 0.1, 0.08, 0.06],
                resistSkillAmp: [0.06, 0.05, 0.04, 0.03],
                resistKnockback: [0.06, 0.05, 0.04, 0.03],
                resistStun: [0.06, 0.05, 0.04, 0.03]
            },
            dex: {
                attackRate: [0.5, 0.4, 0.3, 0.2],
                defenseRate: [0.3, 0.25, 0.2, 0.15],
                evasion: [0.5, 0.4, 0.3, 0.2],
                resistDown: [0.06, 0.05, 0.04, 0.03],
                resistUnableToMove: [0.06, 0.05, 0.04, 0.03]
            }
        },
        'wizard': {
            str: {
                hp: [0.5, 0.4, 0.3, 0.2],
                attack: [0.3, 0.25, 0.2, 0.15],
                damageReduce: [0.15, 0.12, 0.1, 0.08],
                ignorePenetration: [0.2, 0.15, 0.12, 0.1]
            },
            int: {
                magicAttack: [1.2, 1.0, 0.8, 0.6],
                resistCritRate: [0.12, 0.1, 0.08, 0.06],
                resistCritDmg: [0.2, 0.15, 0.12, 0.1],
                resistSkillAmp: [0.12, 0.1, 0.08, 0.06],
                resistKnockback: [0.08, 0.06, 0.05, 0.04],
                resistStun: [0.08, 0.06, 0.05, 0.04]
            },
            dex: {
                attackRate: [0.4, 0.3, 0.25, 0.2],
                defenseRate: [0.3, 0.25, 0.2, 0.15],
                evasion: [0.4, 0.3, 0.25, 0.2],
                resistDown: [0.08, 0.06, 0.05, 0.04],
                resistUnableToMove: [0.08, 0.06, 0.05, 0.04]
            }
        },
        // Default scaling for all other classes (will be populated for all classes in init)
        'default': {
            str: {
                hp: [1.0, 0.8, 0.6, 0.4],
                attack: [0.6, 0.5, 0.4, 0.3],
                damageReduce: [0.2, 0.15, 0.12, 0.1],
                ignorePenetration: [0.25, 0.2, 0.15, 0.1]
            },
            int: {
                magicAttack: [0.6, 0.5, 0.4, 0.3],
                resistCritRate: [0.1, 0.08, 0.06, 0.04],
                resistCritDmg: [0.15, 0.12, 0.1, 0.08],
                resistSkillAmp: [0.1, 0.08, 0.06, 0.04],
                resistKnockback: [0.07, 0.06, 0.05, 0.04],
                resistStun: [0.07, 0.06, 0.05, 0.04]
            },
            dex: {
                attackRate: [0.45, 0.35, 0.25, 0.15],
                defenseRate: [0.3, 0.25, 0.2, 0.15],
                evasion: [0.45, 0.35, 0.25, 0.15],
                resistDown: [0.07, 0.06, 0.05, 0.04],
                resistUnableToMove: [0.07, 0.06, 0.05, 0.04]
            }
        }
    },

    /**
     * Initialize the system - this is called by the BuildPlanner core
     * when this system becomes active
     */
    init: function() {
        // Prevent multiple initializations
        if (this.isInitialized) {
            return;
        }

        console.log('🔄 Initializing Class System');

        // Copy default scaling to other classes that don't have specific scaling
        this.populateDefaultClassScaling();

        // Get the system panel
        const panel = document.getElementById('fg-class-system');
        if (!panel) {
            console.error('❌ Could not find class system panel element');
            return;
        }

        // Cache DOM elements for better performance
        this.elements.panel = panel;

        // Initialize UI
        this.initUI();

        // Load data from the central store if available
        this.loadFromStore();

        // Make sure a class is selected
        if (!this.selectedClass && this.classes.length > 0) {
            this.selectedClass = this.classes[0].id;
            this.updateClassSelection();
        }

        // Update stats for the first time
        this.updateDerivedStats();

        // Setup event listeners
        this.setupEventListeners();

        // Mark as initialized
        this.isInitialized = true;
        console.log('✅ Class System initialized');
    },

    /**
     * Populate default scaling values for classes without specific scaling
     */
    populateDefaultClassScaling: function() {
        // Loop through all classes
        this.classes.forEach(classInfo => {
            // If this class doesn't have specific scaling, copy from default
            if (!this.classScaling[classInfo.id]) {
                this.classScaling[classInfo.id] = JSON.parse(JSON.stringify(this.classScaling.default));
            }
        });
    },

    /**
     * Initialize the user interface
     */
    initUI: function() {
        // Replace the class system placeholder with actual content
        this.createClassSystemUI();
    },

    /**
     * Create the class system UI
     */
    createClassSystemUI: function() {
        if (!this.elements.panel) {
            console.error('❌ Panel element not initialized');
            return;
        }

        console.log('🔄 Creating Class System UI');

        const container = document.createElement('div');
        container.className = 'fg-class-system-container';

        // Class selection section
        const classSelectionSection = document.createElement('div');
        classSelectionSection.className = 'fg-class-selection-section';

        const classHeader = document.createElement('h3');
        classHeader.className = 'fg-section-header';
        classHeader.textContent = 'Select Your Class';
        classSelectionSection.appendChild(classHeader);

        const classGrid = document.createElement('div');
        classGrid.className = 'fg-class-grid';

        // Create class selection buttons
        this.classes.forEach(classInfo => {
            const classBtn = document.createElement('div');
            classBtn.className = 'fg-class-option';
            classBtn.dataset.classId = classInfo.id;

            const classIcon = document.createElement('div');
            classIcon.className = 'fg-class-icon';

            // Use the appropriate icon based on class ID
            let iconFileName;
            switch(classInfo.id) {
                case 'warrior':
                    iconFileName = 'warrior_icon.png';
                    break;
                case 'wizard':
                    iconFileName = 'wizard_icon.png';
                    break;
                case 'blader':
                    iconFileName = 'bkader_icon.png'; // Using the available bkader icon for blader
                    break;
                case 'dark-mage':
                    iconFileName = 'dark_mage_icon.png';
                    break;
                case 'gladiator':
                    iconFileName = 'gladiator_icon.png';
                    break;
                case 'force-gunner':
                    iconFileName = 'force_gunner_icon.png';
                    break;
                case 'force-blader':
                    iconFileName = 'force_blader_icon.png';
                    break;
                case 'force-archer':
                    iconFileName = 'force_archer_icon.png';
                    break;
                case 'force-shielder':
                    iconFileName = 'force_shielder_icon.png';
                    break;
                default:
                    iconFileName = '';
            }

            if (iconFileName) {
                classIcon.innerHTML = `<img src="${StatsConfig.getPluginUrl()}assets/images/class-icons/${iconFileName}" alt="${classInfo.name}" class="fg-class-icon-img">`;
            } else {
                classIcon.innerHTML = `<i class="fg-icon-placeholder"></i>`;
            }

            const className = document.createElement('div');
            className.className = 'fg-class-name';
            className.textContent = classInfo.name;

            classBtn.appendChild(classIcon);
            classBtn.appendChild(className);
            classGrid.appendChild(classBtn);
        });

        classSelectionSection.appendChild(classGrid);
        container.appendChild(classSelectionSection);

        // Attribute distribution section
        const attributeSection = document.createElement('div');
        attributeSection.className = 'fg-attribute-section';

        const attributeHeader = document.createElement('h3');
        attributeHeader.className = 'fg-section-header';
        attributeHeader.textContent = 'Distribute Attribute Points';
        attributeSection.appendChild(attributeHeader);

        // Info about points
        const pointsInfo = document.createElement('div');
        pointsInfo.className = 'fg-points-info';
        pointsInfo.innerHTML = `
            <p>Character Level: <span class="fg-level">200</span></p>
            <p>Total Points: <span class="fg-total-points">1379</span></p>
            <p>Remaining Points: <span class="fg-remaining-points">1379</span></p>
        `;
        attributeSection.appendChild(pointsInfo);

        // Create attribute controls
        const attributesContainer = document.createElement('div');
        attributesContainer.className = 'fg-attributes-container';

        ['str', 'int', 'dex'].forEach(attr => {
            const attrControl = document.createElement('div');
            attrControl.className = 'fg-attribute-control';

            const attrLabel = document.createElement('div');
            attrLabel.className = 'fg-attribute-label';

            // Icon for the attribute
            const attrIcon = document.createElement('div');
            attrIcon.className = 'fg-attribute-icon';
            if (StatsConfig && StatsConfig.stats[attr] && StatsConfig.stats[attr].icon) {
                attrIcon.innerHTML = `<img src="${StatsConfig.getStatIconUrl(attr)}" alt="${attr.toUpperCase()}">`;
            } else {
                attrIcon.innerHTML = `<span>${attr.toUpperCase()}</span>`;
            }

            const attrName = document.createElement('span');
            attrName.textContent = attr.toUpperCase();

            attrLabel.appendChild(attrIcon);
            attrLabel.appendChild(attrName);

            // Attribute value and controls
            const attrValueControl = document.createElement('div');
            attrValueControl.className = 'fg-attribute-value-control';

            const decrementBtn = document.createElement('button');
            decrementBtn.className = 'fg-attribute-btn fg-decrement';
            decrementBtn.textContent = '-';
            decrementBtn.dataset.attr = attr;

            const attrValue = document.createElement('span');
            attrValue.className = `fg-attribute-value fg-${attr}-value`;
            attrValue.textContent = '10';

            const incrementBtn = document.createElement('button');
            incrementBtn.className = 'fg-attribute-btn fg-increment';
            incrementBtn.textContent = '+';
            incrementBtn.dataset.attr = attr;

            // Add +10 and +100 buttons for faster allocation
            const increment10Btn = document.createElement('button');
            increment10Btn.className = 'fg-attribute-btn fg-increment-10';
            increment10Btn.textContent = '+10';
            increment10Btn.dataset.attr = attr;

            const increment100Btn = document.createElement('button');
            increment100Btn.className = 'fg-attribute-btn fg-increment-100';
            increment100Btn.textContent = '+100';
            increment100Btn.dataset.attr = attr;

            // Add them to the control
            attrValueControl.appendChild(decrementBtn);
            attrValueControl.appendChild(attrValue);
            attrValueControl.appendChild(incrementBtn);
            attrValueControl.appendChild(increment10Btn);
            attrValueControl.appendChild(increment100Btn);

            // Add tooltip trigger
            const tooltipTrigger = document.createElement('div');
            tooltipTrigger.className = 'fg-attribute-tooltip-trigger';
            tooltipTrigger.dataset.attr = attr;
            tooltipTrigger.innerHTML = '<i class="fg-icon-info">?</i>';

            // Combine everything
            attrControl.appendChild(attrLabel);
            attrControl.appendChild(attrValueControl);
            attrControl.appendChild(tooltipTrigger);

            attributesContainer.appendChild(attrControl);
        });

        attributeSection.appendChild(attributesContainer);

        container.appendChild(attributeSection);

        // Derived stats section
        const derivedStatsSection = document.createElement('div');
        derivedStatsSection.className = 'fg-derived-stats-section';

        const derivedHeader = document.createElement('h3');
        derivedHeader.className = 'fg-section-header';
        derivedHeader.textContent = 'Derived Stats';
        derivedStatsSection.appendChild(derivedHeader);

        // Create sections for each attribute's derived stats
        const statsContainer = document.createElement('div');
        statsContainer.className = 'fg-derived-stats-container';

        // STR-derived stats
        const strStats = document.createElement('div');
        strStats.className = 'fg-derived-stats-group';
        strStats.innerHTML = `
            <h4 class="fg-stats-group-header">STR-based Stats</h4>
            <div class="fg-derived-stats fg-str-derived-stats"></div>
        `;

        // INT-derived stats
        const intStats = document.createElement('div');
        intStats.className = 'fg-derived-stats-group';
        intStats.innerHTML = `
            <h4 class="fg-stats-group-header">INT-based Stats</h4>
            <div class="fg-derived-stats fg-int-derived-stats"></div>
        `;

        // DEX-derived stats
        const dexStats = document.createElement('div');
        dexStats.className = 'fg-derived-stats-group';
        dexStats.innerHTML = `
            <h4 class="fg-stats-group-header">DEX-based Stats</h4>
            <div class="fg-derived-stats fg-dex-derived-stats"></div>
        `;

        statsContainer.appendChild(strStats);
        statsContainer.appendChild(intStats);
        statsContainer.appendChild(dexStats);

        derivedStatsSection.appendChild(statsContainer);
        container.appendChild(derivedStatsSection);

        // Tooltip container (for showing stat derivations)
        const tooltipContainer = document.createElement('div');
        tooltipContainer.className = 'fg-attribute-tooltip';
        tooltipContainer.style.display = 'none';
        container.appendChild(tooltipContainer);

        // Replace the panel content with our new UI
        this.elements.panel.innerHTML = '';
        this.elements.panel.appendChild(container);

        // Cache important elements for later use
        this.elements.classOptions = this.elements.panel.querySelectorAll('.fg-class-option');
        this.elements.remainingPoints = this.elements.panel.querySelector('.fg-remaining-points');
        this.elements.strValue = this.elements.panel.querySelector('.fg-str-value');
        this.elements.intValue = this.elements.panel.querySelector('.fg-int-value');
        this.elements.dexValue = this.elements.panel.querySelector('.fg-dex-value');
        this.elements.strDerivedStats = this.elements.panel.querySelector('.fg-str-derived-stats');
        this.elements.intDerivedStats = this.elements.panel.querySelector('.fg-int-derived-stats');
        this.elements.dexDerivedStats = this.elements.panel.querySelector('.fg-dex-derived-stats');
        this.elements.tooltipContainer = this.elements.panel.querySelector('.fg-attribute-tooltip');

        // Initialize Quick Fill Button Manager
        if (typeof QuickFillButtonConfigs !== 'undefined') {
            this.quickFillManager = QuickFillButtonConfigs.initializeSystem('class', this.elements.panel.querySelector('.fg-class-system-container'));
        }

        // Verify that the derived stats elements were found
        if (!this.elements.strDerivedStats || !this.elements.intDerivedStats || !this.elements.dexDerivedStats) {
            console.error('❌ Could not find derived stats containers in the DOM');
        } else {
            console.log('✅ Derived stats containers found in the DOM');
        }
    },

    /**
     * Set up event listeners for UI interactions
     */
    setupEventListeners: function() {
        if (!this.elements.panel) return;

        // Class selection
        this.elements.classOptions.forEach(option => {
            option.addEventListener('click', (e) => {
                const classId = e.currentTarget.dataset.classId;
                this.selectClass(classId);
            });
        });

        // Attribute increment/decrement buttons
        const attributeBtns = this.elements.panel.querySelectorAll('.fg-attribute-btn');
        attributeBtns.forEach(btn => {
            btn.addEventListener('click', (e) => {
                const attr = e.currentTarget.dataset.attr;
                let amount = 1;

                if (e.currentTarget.classList.contains('fg-decrement')) {
                    amount = -1;
                } else if (e.currentTarget.classList.contains('fg-increment-10')) {
                    amount = 10;
                } else if (e.currentTarget.classList.contains('fg-increment-100')) {
                    amount = 100;
                }

                this.adjustAttribute(attr, amount);
            });
        });

        // Tooltip triggers
        const tooltipTriggers = this.elements.panel.querySelectorAll('.fg-attribute-tooltip-trigger');
        tooltipTriggers.forEach(trigger => {
            trigger.addEventListener('mouseenter', (e) => {
                const attr = e.currentTarget.dataset.attr;
                this.showTooltip(attr, e);
            });

            trigger.addEventListener('mouseleave', () => {
                this.hideTooltip();
            });
        });
    },

    /**
     * Get the essential data for saving
     * Returns just the minimum data needed to restore the system state
     */
    getEssentialData: function() {
        // Return only the essential data needed to restore state
        return {
            selectedClass: this.selectedClass,
            attributes: {
                str: this.baseStats.currentStr,
                int: this.baseStats.currentInt,
                dex: this.baseStats.currentDex
            }
        };
    },

    /**
     * Load data from the central store
     */
    loadFromStore: function() {
        // Only load if BuildSaverStore is available
        if (typeof BuildSaverStore === 'undefined') {
            return;
        }

        // Get data from store with the consistent ID
        const storedData = BuildSaverStore.getSystemData('class');
        if (!storedData) {
            return;
        }

        // Apply stored data
        if (storedData.selectedClass) {
            this.selectedClass = storedData.selectedClass;
            // Update UI for selected class
            this.updateClassSelection();
        }

        if (storedData.attributes) {
            // Update base stats
            this.baseStats.currentStr = storedData.attributes.str || this.baseStats.baseStr;
            this.baseStats.currentInt = storedData.attributes.int || this.baseStats.baseInt;
            this.baseStats.currentDex = storedData.attributes.dex || this.baseStats.baseDex;

            // Calculate remaining points
            this.baseStats.remainingPoints = this.baseStats.totalStatPoints -
                (this.baseStats.currentStr - this.baseStats.baseStr) -
                (this.baseStats.currentInt - this.baseStats.baseInt) -
                (this.baseStats.currentDex - this.baseStats.baseDex);

            // Update UI
            this.updateAttributeValues();
        }

        // Update derived stats after loading data
        this.updateDerivedStats();
    },

    /**
     * Save data to the central store
     */
    saveToStore: function() {
        // Check if BuildSaverStore exists
        if (typeof BuildSaverStore !== 'undefined') {
            // The actual save is triggered by the user clicking the Save Build button
            // But we need to ensure our system ID is correct for when that happens
            return true;
        }

        return false;
    },

    /**
     * Select a character class
     * @param {string} classId - The ID of the class to select
     */
    selectClass: function(classId) {
        // Validate class ID
        const validClass = this.classes.find(c => c.id === classId);
        if (!validClass) {
            return;
        }

        // Set the selected class
        this.selectedClass = classId;

        // Update UI
        this.updateClassSelection();

        // Update derived stats based on new class
        this.updateDerivedStats();

        // Save to store
        this.saveToStore();
    },

    /**
     * Update UI to reflect the currently selected class
     */
    updateClassSelection: function() {
        // Skip if not initialized
        if (!this.elements.classOptions) return;

        // Remove selected class from all options
        this.elements.classOptions.forEach(option => {
            option.classList.remove('selected');
        });

        // Add selected class to the correct option
        if (this.selectedClass) {
            const selectedOption = Array.from(this.elements.classOptions)
                .find(option => option.dataset.classId === this.selectedClass);

            if (selectedOption) {
                selectedOption.classList.add('selected');
            }
        }
    },

    /**
     * Adjust an attribute value
     * @param {string} attr - The attribute to adjust (str, int, dex)
     * @param {number} amount - The amount to adjust by (positive or negative)
     */
    adjustAttribute: function(attr, amount) {
        // Validate attribute
        if (!['str', 'int', 'dex'].includes(attr)) {
            return;
        }

        // Check if we can make this adjustment
        if (amount > 0 && this.baseStats.remainingPoints < amount) {
            // Not enough points
            return;
        }

        // Calculate new value
        const currentValue = this.baseStats[`current${attr.charAt(0).toUpperCase() + attr.slice(1)}`];
        const newValue = currentValue + amount;

        // Check if we can decrease below base value
        if (amount < 0 && newValue < this.baseStats[`base${attr.charAt(0).toUpperCase() + attr.slice(1)}`]) {
            return;
        }

        // Update the value
        this.baseStats[`current${attr.charAt(0).toUpperCase() + attr.slice(1)}`] = newValue;

        // Update remaining points
        this.baseStats.remainingPoints -= amount;

        // Update UI
        this.updateAttributeValues();

        // Update derived stats
        this.updateDerivedStats();

        // Save to store
        this.saveToStore();
    },

    /**
     * Reset attributes to base values
     */
    resetAttributes: function() {
        // Reset attributes to base values
        this.baseStats.currentStr = this.baseStats.baseStr;
        this.baseStats.currentInt = this.baseStats.baseInt;
        this.baseStats.currentDex = this.baseStats.baseDex;

        // Reset remaining points
        this.baseStats.remainingPoints = this.baseStats.totalStatPoints;

        // Update UI
        this.updateAttributeValues();

        // Update derived stats
        this.updateDerivedStats();

        // Save to store
        this.saveToStore();
    },

    /**
     * Update attribute values in the UI
     */
    updateAttributeValues: function() {
        // Skip if not initialized
        if (!this.elements.strValue) return;

        // Update attribute values
        this.elements.strValue.textContent = this.baseStats.currentStr;
        this.elements.intValue.textContent = this.baseStats.currentInt;
        this.elements.dexValue.textContent = this.baseStats.currentDex;

        // Update remaining points
        this.elements.remainingPoints.textContent = this.baseStats.remainingPoints;
    },

    /**
     * Calculate and update derived stats based on current attributes and class
     */
    updateDerivedStats: function() {
        // Skip if no class is selected
        if (!this.selectedClass) return;

        // Get class scaling values
        const scaling = this.classScaling[this.selectedClass];

        // Calculate derived stats
        const derivedStats = {};

        // Process STR-derived stats
        Object.keys(scaling.str).forEach(statId => {
            derivedStats[statId] = this.calculateStatValue('str', statId);
        });

        // Process INT-derived stats
        Object.keys(scaling.int).forEach(statId => {
            derivedStats[statId] = this.calculateStatValue('int', statId);
        });

        // Process DEX-derived stats
        Object.keys(scaling.dex).forEach(statId => {
            derivedStats[statId] = this.calculateStatValue('dex', statId);
        });

        // Update stats in the UI
        this.updateDerivedStatsUI(derivedStats);

        // Update global stats
        this.updateGlobalStats(derivedStats);
    },

    /**
     * Calculate a single stat value based on attribute and scaling
     * @param {string} attr - The attribute (str, int, dex)
     * @param {string} statId - The stat ID to calculate
     * @returns {number} - The calculated stat value
     */
    calculateStatValue: function(attr, statId) {
        // Get the current attribute value
        const attrValue = this.baseStats[`current${attr.charAt(0).toUpperCase() + attr.slice(1)}`];

        // Get scaling values for this stat
        const scaling = this.classScaling[this.selectedClass][attr][statId];

        let total = 0;

        // Apply scaling based on attribute value ranges
        this.statRanges.forEach((range, index) => {
            if (attrValue <= range.max) {
                // If attr value is within this range, calculate for the portion within range
                const pointsInRange = Math.min(attrValue, range.max) - Math.max(range.min, 0);
                if (pointsInRange > 0) {
                    total += pointsInRange * scaling[index];
                }
            } else {
                // If attr value exceeds this range, calculate for the entire range
                const pointsInRange = range.max - Math.max(range.min, 0);
                if (pointsInRange > 0) {
                    total += pointsInRange * scaling[index];
                }
            }
        });

        // Use StatsConfig to determine if a stat is a percentage stat
        const isPercentage = StatsConfig.stats[statId] ? StatsConfig.stats[statId].isPercentage : false;

        return isPercentage ? Math.round(total * 100) / 100 : Math.floor(total);
    },

    /**
     * Update the UI display of derived stats
     * @param {Object} derivedStats - The calculated derived stats
     */
    updateDerivedStatsUI: function(derivedStats) {
        // Skip if not initialized
        if (!this.elements.strDerivedStats) return;

        // Clear previous stats
        this.elements.strDerivedStats.innerHTML = '';
        this.elements.intDerivedStats.innerHTML = '';
        this.elements.dexDerivedStats.innerHTML = '';

        // Add STR-derived stats
        Object.keys(this.classScaling[this.selectedClass].str).forEach(statId => {
            const value = derivedStats[statId];
            const statElement = this.createStatElement(statId, value);
            this.elements.strDerivedStats.appendChild(statElement);
        });

        // Add INT-derived stats
        Object.keys(this.classScaling[this.selectedClass].int).forEach(statId => {
            const value = derivedStats[statId];
            const statElement = this.createStatElement(statId, value);
            this.elements.intDerivedStats.appendChild(statElement);
        });

        // Add DEX-derived stats
        Object.keys(this.classScaling[this.selectedClass].dex).forEach(statId => {
            const value = derivedStats[statId];
            const statElement = this.createStatElement(statId, value);
            this.elements.dexDerivedStats.appendChild(statElement);
        });
    },

    /**
     * Update global stats in the BuildPlanner
     * @param {Object} derivedStats - The calculated derived stats
     */
    updateGlobalStats: function(derivedStats) {
        // Skip if BuildPlanner is not available
        if (typeof BuildPlanner === 'undefined') {
            console.warn('BuildPlanner not available - cannot update global stats');
            return;
        }

        // Add base attributes to derived stats
        derivedStats.str = this.baseStats.currentStr;
        derivedStats.int = this.baseStats.currentInt;
        derivedStats.dex = this.baseStats.currentDex;

        // Send stats to BuildPlanner
        BuildPlanner.updateStats('class', derivedStats);
    },

    /**
     * Show tooltip for an attribute
     * @param {string} attr - The attribute to show tooltip for
     * @param {Event} event - The event that triggered the tooltip
     */
    showTooltip: function(attr, event) {
        // Skip if no class selected
        if (!this.selectedClass) return;

        // Get the tooltip container
        const tooltip = this.elements.tooltipContainer;

        // Get attribute value
        const attrValue = this.baseStats[`current${attr.charAt(0).toUpperCase() + attr.slice(1)}`];

        // Create tooltip content
        let tooltipContent = `<h4>${attr.toUpperCase()} Stats Breakdown</h4>`;
        tooltipContent += `<p>Your ${attr.toUpperCase()} value: ${attrValue}</p>`;
        tooltipContent += `<div class="fg-tooltip-stats">`;

        // Add stats for this attribute
        Object.keys(this.classScaling[this.selectedClass][attr]).forEach(statId => {
            // Get stat name
            let statName = statId;
            if (StatsConfig.stats[statId]) {
                statName = StatsConfig.stats[statId].name;
            }

            // Get the value
            const value = this.calculateStatValue(attr, statId);

            // Format value (add % for percentage stats)
            const isPercentage = StatsConfig.stats[statId] ? StatsConfig.stats[statId].isPercentage : false;
            const formattedValue = isPercentage ? `${value}%` : value.toLocaleString();

            tooltipContent += `
                <div class="fg-tooltip-stat">
                    <span class="fg-tooltip-stat-name">${statName}</span>
                    <span class="fg-tooltip-stat-value">${formattedValue}</span>
                </div>
            `;
        });

        tooltipContent += `</div>`;

        // Show the tooltip
        tooltip.innerHTML = tooltipContent;
        tooltip.style.display = 'block';

        // Position the tooltip
        const rect = event.target.getBoundingClientRect();
        const panelRect = this.elements.panel.getBoundingClientRect();

        tooltip.style.left = `${rect.right - panelRect.left + 10}px`;
        tooltip.style.top = `${rect.top - panelRect.top - 10}px`;
    },

    /**
     * Hide the attribute tooltip
     */
    hideTooltip: function() {
        if (this.elements.tooltipContainer) {
            this.elements.tooltipContainer.style.display = 'none';
        }
    },

    createStatElement: function(statId, value) {
        const statContainer = document.createElement('div');
        statContainer.className = 'fg-derived-stat';

        // Add icon if available
        let iconHtml = '';
        if (StatsConfig.stats[statId] && StatsConfig.stats[statId].icon) {
            iconHtml = `<img src="${StatsConfig.getStatIconUrl(statId)}" alt="${statId}">`;
        }

        // Get stat name
        let statName = statId;
        if (StatsConfig.stats[statId]) {
            statName = StatsConfig.stats[statId].name;
        }

        // Format value (add % for percentage stats)
        const isPercentage = StatsConfig.stats[statId] ? StatsConfig.stats[statId].isPercentage : false;
        const formattedValue = isPercentage ? `${value}%` : value.toLocaleString();

        statContainer.innerHTML = `
            <div class="fg-derived-stat-icon">${iconHtml}</div>
            <div class="fg-derived-stat-info">
                <span class="fg-derived-stat-name">${statName}</span>
                <span class="fg-derived-stat-value">${formattedValue}</span>
            </div>
        `;

        return statContainer;
    }
};