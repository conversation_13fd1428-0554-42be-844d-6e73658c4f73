<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Achievement System Prototype</title>
    <style>
        :root {
            /* Base colors from pet system */
            --fg-dark-bg: #1c1e22;
            --fg-border: #3c3f44;
            --fg-darker-bg: rgba(0, 0, 0, 0.2);
            --fg-text: #d0d0d0;

            /* Accent colors */
            --fg-accent: #66bb6a;
            --fg-highlight: #4c8e50;
            --fg-header: #ff3333;
            --fg-hover-bg: rgba(27, 94, 32, 0.2);

            /* Achievement specific colors */
            --fg-progress-bg: #ffa500;
            --fg-progress-fill: linear-gradient(90deg, #ffa500, #ffb84d);
            --fg-category-active: #4a4a4a;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: Arial, sans-serif;
            background: var(--fg-dark-bg);
            color: var(--fg-text);
            padding: 20px;
        }

        .achievement-system {
            max-width: 1200px;
            margin: 0 auto;
            background: var(--fg-dark-bg);
            border-radius: 5px;
            border: 1px solid var(--fg-border);
            overflow: hidden;
            min-height: 600px;
            position: relative;
        }

        .system-header {
            background: #272a2e;
            padding: 20px;
            text-align: center;
            border-bottom: 1px solid var(--fg-border);
        }

        .system-header h1 {
            color: var(--fg-header);
            margin-bottom: 5px;
            font-size: 1.5rem;
        }

        .system-header p {
            color: var(--fg-text);
            font-size: 0.9rem;
            margin: 0;
        }

        .achievement-layout {
            display: flex;
            width: 100%;
            height: calc(100% - 80px);
        }

        .category-sidebar {
            width: 200px;
            background: rgba(28, 30, 34, 0.5);
            border-right: 1px solid var(--fg-border);
            padding: 15px 0;
        }

        .category-item {
            padding: 12px 20px;
            cursor: pointer;
            transition: background 0.3s;
            border-bottom: 1px solid rgba(60, 63, 68, 0.3);
            color: var(--fg-text);
            font-size: 0.9rem;
        }

        .category-item:hover {
            background: var(--fg-hover-bg);
        }

        .category-item.active {
            background: var(--fg-category-active);
            border-left: 3px solid var(--fg-accent);
            color: #fff;
        }

        .category-count {
            float: right;
            color: #888;
            font-size: 0.8rem;
        }

        .main-content {
            flex: 1;
            padding: 20px;
            overflow-y: auto;
            height: 100%;
        }

        .achievement-card {
            background: rgba(28, 30, 34, 0.5);
            border: 1px solid var(--fg-border);
            border-radius: 8px;
            margin-bottom: 15px;
            padding: 20px;
            transition: border-color 0.3s;
        }

        .achievement-card:hover {
            border-color: var(--fg-accent);
        }

        .achievement-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
            padding-bottom: 8px;
            border-bottom: 1px dashed rgba(255, 51, 51, 0.3);
        }

        .achievement-name {
            font-size: 16px;
            font-weight: bold;
            color: var(--fg-header);
            text-shadow: 0 1px 1px rgba(0, 0, 0, 0.5);
        }

        .achievement-type {
            background: var(--fg-darker-bg);
            border: 1px solid var(--fg-border);
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 11px;
            text-transform: uppercase;
            color: var(--fg-text);
        }

        .progress-container {
            margin-bottom: 15px;
        }

        .progress-bar {
            width: 100%;
            height: 18px;
            background: var(--fg-darker-bg);
            border: 1px solid var(--fg-border);
            border-radius: 4px;
            overflow: hidden;
            position: relative;
        }

        .progress-fill {
            height: 100%;
            background: var(--fg-progress-fill);
            transition: width 0.3s ease;
            position: relative;
        }

        .progress-text {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            font-size: 11px;
            font-weight: bold;
            color: #000;
            z-index: 2;
            text-shadow: 0 1px 1px rgba(255, 255, 255, 0.3);
        }

        .milestone-selector {
            margin-bottom: 15px;
        }

        .milestone-selector label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: var(--fg-text);
            font-size: 0.9rem;
        }

        .milestone-selector select {
            width: 100%;
            padding: 8px;
            background: var(--fg-darker-bg);
            color: var(--fg-text);
            border: 1px solid var(--fg-border);
            border-radius: 4px;
            font-size: 0.9rem;
        }

        .milestone-selector select:focus {
            border-color: var(--fg-accent);
            outline: none;
        }

        .stats-display {
            background: var(--fg-darker-bg);
            padding: 15px;
            border-radius: 4px;
            border: 1px solid var(--fg-border);
        }

        .stats-title {
            font-weight: bold;
            margin-bottom: 10px;
            color: #e0e0e0;
            font-size: 0.9rem;
            border-bottom: 1px solid var(--fg-border);
            padding-bottom: 5px;
        }

        .stat-item {
            display: flex;
            justify-content: space-between;
            margin-bottom: 5px;
            padding: 4px 0;
        }

        .stat-name {
            color: var(--fg-text);
            font-size: 0.85rem;
        }

        .stat-value {
            color: var(--fg-accent);
            font-weight: bold;
            font-size: 0.85rem;
        }



        .checkbox-container {
            display: flex;
            align-items: center;
            margin-bottom: 10px;
        }

        .checkbox-container input[type="checkbox"] {
            margin-right: 10px;
            transform: scale(1.2);
            accent-color: var(--fg-accent);
        }

        .checkbox-container label {
            color: var(--fg-text);
            font-size: 0.9rem;
        }
    </style>
</head>
<body>
    <div class="achievement-system">
        <div class="system-header">
            <h1>Achievement System</h1>
            <p>Select your completed achievements to gain stat bonuses</p>
        </div>

        <div class="achievement-layout">
            <div class="category-sidebar">
                <div class="category-item active" data-category="quests">
                    Quests <span class="category-count">(19/33)</span>
                </div>
                <div class="category-item" data-category="dungeons">
                    Dungeon <span class="category-count">(51/173)</span>
                </div>
                <div class="category-item" data-category="pvp">
                    PvP <span class="category-count">(3/32)</span>
                </div>
                <div class="category-item" data-category="mission-war">
                    Mission War <span class="category-count">(13/45)</span>
                </div>
                <div class="category-item" data-category="hunting">
                    Hunting <span class="category-count">(0/106)</span>
                </div>
                <div class="category-item" data-category="shared">
                    Shared Achievements <span class="category-count">(13/38)</span>
                </div>
                <div class="category-item" data-category="normal">
                    Normal <span class="category-count">(56/98)</span>
                </div>
            </div>

            <div class="main-content">
                <div id="quests-content" class="category-panel">
                    <p style="text-align: center; color: #888; padding: 40px;">Quest achievements coming soon...</p>
                </div>
                <div id="dungeons-content" class="category-panel" style="display: none;">
                    <!-- Dungeon achievements will be populated here -->
                </div>
                <div id="pvp-content" class="category-panel" style="display: none;">
                    <p style="text-align: center; color: #888; padding: 40px;">PvP achievements coming soon...</p>
                </div>
                <div id="mission-war-content" class="category-panel" style="display: none;">
                    <p style="text-align: center; color: #888; padding: 40px;">Mission War achievements coming soon...</p>
                </div>
                <div id="hunting-content" class="category-panel" style="display: none;">
                    <p style="text-align: center; color: #888; padding: 40px;">Hunting achievements coming soon...</p>
                </div>
                <div id="shared-content" class="category-panel" style="display: none;">
                    <p style="text-align: center; color: #888; padding: 40px;">Shared achievements coming soon...</p>
                </div>
                <div id="normal-content" class="category-panel" style="display: none;">
                    <p style="text-align: center; color: #888; padding: 40px;">Normal achievements coming soon...</p>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Sample achievement data
        const achievementData = {
            dungeons: [
                {
                    id: 'illusion-castle-underworld',
                    name: 'Illusion Castle - Underworld',
                    type: 'milestone',
                    milestones: [
                        { threshold: 100, stats: { 'Defense Rate': 2 } },
                        { threshold: 500, stats: { 'Defense Rate': 4 } },
                        { threshold: 1000, stats: { 'Defense Rate': 6 } },
                        { threshold: 5000, stats: { 'Defense Rate': 8 } }
                    ],
                    currentMilestone: 0
                },
                {
                    id: 'illusion-castle-radiant',
                    name: 'Illusion Castle - Radiant Hall',
                    type: 'milestone',
                    milestones: [
                        { threshold: 100, stats: { 'Attack Rate': 2 } },
                        { threshold: 500, stats: { 'Attack Rate': 4 } },
                        { threshold: 1000, stats: { 'Attack Rate': 6 } },
                        { threshold: 5000, stats: { 'Attack Rate': 8 } }
                    ],
                    currentMilestone: 0
                },
                {
                    id: 'tutorial-complete',
                    name: 'Tutorial Master',
                    type: 'single',
                    stats: { 'Experience Rate': 10, 'Movement Speed': 5 },
                    completed: false
                }
            ]
        };

        // Initialize the system
        function initAchievementSystem() {
            setupTabs();
            renderDungeonAchievements();
        }

        function setupTabs() {
            const categoryItems = document.querySelectorAll('.category-item');
            categoryItems.forEach(item => {
                item.addEventListener('click', () => {
                    // Remove active class from all items
                    categoryItems.forEach(i => i.classList.remove('active'));
                    // Add active class to clicked item
                    item.classList.add('active');

                    // Hide all panels
                    document.querySelectorAll('.category-panel').forEach(panel => {
                        panel.style.display = 'none';
                    });

                    // Show selected panel
                    const category = item.dataset.category;
                    const targetPanel = document.getElementById(category + '-content');
                    if (targetPanel) {
                        targetPanel.style.display = 'block';
                    }
                });
            });
        }

        function renderDungeonAchievements() {
            const container = document.getElementById('dungeons-content');
            container.innerHTML = '';

            achievementData.dungeons.forEach(achievement => {
                const card = createAchievementCard(achievement);
                container.appendChild(card);
            });
        }

        function createAchievementCard(achievement) {
            const card = document.createElement('div');
            card.className = 'achievement-card';

            if (achievement.type === 'milestone') {
                card.innerHTML = `
                    <div class="achievement-header">
                        <div class="achievement-name">${achievement.name}</div>
                        <div class="achievement-type">Milestone</div>
                    </div>
                    <div class="progress-container">
                        <div class="progress-bar">
                            <div class="progress-fill" style="width: ${getProgressPercentage(achievement)}%"></div>
                            <div class="progress-text">${achievement.currentMilestone}/${achievement.milestones.length}</div>
                        </div>
                    </div>
                    <div class="milestone-selector">
                        <label>Current Milestone:</label>
                        <select onchange="updateMilestone('${achievement.id}', this.value)">
                            <option value="0">Not Started</option>
                            ${achievement.milestones.map((milestone, index) =>
                                `<option value="${index + 1}" ${achievement.currentMilestone === index + 1 ? 'selected' : ''}>
                                    ${milestone.threshold} completions
                                </option>`
                            ).join('')}
                        </select>
                    </div>
                    <div class="stats-display">
                        <div class="stats-title">Cumulative Rewards:</div>
                        ${renderMilestoneStats(achievement)}
                    </div>
                `;
            } else {
                card.innerHTML = `
                    <div class="achievement-header">
                        <div class="achievement-name">${achievement.name}</div>
                        <div class="achievement-type">Single</div>
                    </div>
                    <div class="checkbox-container">
                        <input type="checkbox" ${achievement.completed ? 'checked' : ''}
                               onchange="toggleAchievement('${achievement.id}', this.checked)">
                        <label>Achievement Completed</label>
                    </div>
                    <div class="progress-container">
                        <div class="progress-bar">
                            <div class="progress-fill" style="width: ${achievement.completed ? 100 : 0}%"></div>
                            <div class="progress-text">${achievement.completed ? 'Complete' : 'Not Complete'}</div>
                        </div>
                    </div>
                    <div class="stats-display">
                        <div class="stats-title">Rewards:</div>
                        ${renderSingleStats(achievement.stats)}
                    </div>
                `;
            }

            return card;
        }

        function renderMilestoneStats(achievement) {
            const totalStats = {};

            // Calculate cumulative stats up to current milestone
            for (let i = 0; i < achievement.currentMilestone; i++) {
                const milestone = achievement.milestones[i];
                Object.entries(milestone.stats).forEach(([stat, value]) => {
                    totalStats[stat] = (totalStats[stat] || 0) + value;
                });
            }

            return Object.entries(totalStats).map(([stat, value]) =>
                `<div class="stat-item">
                    <span class="stat-name">${stat}:</span>
                    <span class="stat-value">+${value}</span>
                </div>`
            ).join('');
        }

        function renderSingleStats(stats) {
            return Object.entries(stats).map(([stat, value]) =>
                `<div class="stat-item">
                    <span class="stat-name">${stat}:</span>
                    <span class="stat-value">+${value}</span>
                </div>`
            ).join('');
        }

        function getProgressPercentage(achievement) {
            return (achievement.currentMilestone / achievement.milestones.length) * 100;
        }

        function updateMilestone(achievementId, milestone) {
            const achievement = achievementData.dungeons.find(a => a.id === achievementId);
            if (achievement) {
                achievement.currentMilestone = parseInt(milestone);
                renderDungeonAchievements();
            }
        }

        function toggleAchievement(achievementId, completed) {
            const achievement = achievementData.dungeons.find(a => a.id === achievementId);
            if (achievement) {
                achievement.completed = completed;
            }
        }



        // Initialize when page loads
        document.addEventListener('DOMContentLoaded', initAchievementSystem);
    </script>
</body>
</html>
