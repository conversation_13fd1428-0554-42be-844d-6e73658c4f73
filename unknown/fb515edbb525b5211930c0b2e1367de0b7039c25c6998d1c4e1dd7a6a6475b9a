/**
 * Stats Summary
 * Handles the display and management of the combined stat summary for all systems
 */

window.StatsSummary = {
    // Track total stats from all systems
    totalStats: {},

    // Track derived stats (after applying modifiers)
    derivedStats: {},

    // Initialize the stats summary
    init: function() {
        // Get stats list containers
        const atkStatsContainer = document.getElementById('atk-stats-list');
        const defStatsContainer = document.getElementById('def-stats-list');
        const otherStatsContainer = document.getElementById('other-stats-list');

        // Skip if elements don't exist
        if (!atkStatsContainer || !defStatsContainer || !otherStatsContainer) {
            return;
        }

        // Clear containers
        atkStatsContainer.innerHTML = '';
        defStatsContainer.innerHTML = '';
        otherStatsContainer.innerHTML = '';

        // Create placeholder content
        const placeholder = `<div class="fg-stat-item">No stats yet</div>`;
        atkStatsContainer.innerHTML = placeholder;
        defStatsContainer.innerHTML = placeholder;
        otherStatsContainer.innerHTML = placeholder;

        // Setup tab events if needed
        this.setupTabEvents();
    },

    // Update total stats from all systems
    updateStats: function(systemId, systemStats) {
        // Request stats update from BuildPlanner (using same method as all systems)
        if (BuildPlanner && BuildPlanner.updateStats) {
            BuildPlanner.updateStats(systemId, systemStats);
        }
    },

    // Calculate and display total stats
    updateTotalStats: function(combinedStats) {
        // Store the original combined stats
        this.totalStats = combinedStats;

        // Calculate derived stats by applying modifiers
        this.calculateDerivedStats();

        // Update UI with derived stats
        this.updateStatsSummaryUI(this.derivedStats);
    },

    // Calculate derived stats by applying modifiers like ALL ATTACK and ALL SKILL AMP
    calculateDerivedStats: function() {
        // Start with a copy of the original stats
        this.derivedStats = {...this.totalStats};

        // Apply ALL ATTACK to both attack and magicAttack
        if (this.totalStats.allAttackUp) {
            // Increase attack by allAttackUp
            if (typeof this.derivedStats.attack === 'number') {
                this.derivedStats.attack += this.totalStats.allAttackUp;
            } else {
                this.derivedStats.attack = this.totalStats.allAttackUp;
            }

            // Increase magicAttack by allAttackUp
            if (typeof this.derivedStats.magicAttack === 'number') {
                this.derivedStats.magicAttack += this.totalStats.allAttackUp;
            } else {
                this.derivedStats.magicAttack = this.totalStats.allAttackUp;
            }
        }

        // Apply ALL SKILL AMP to both swordSkillAmp and magicSkillAmp
        // Note: Handle both allSkillAmp (standard) and allAmp (legacy) for backward compatibility
        const allAmpValue = (this.totalStats.allSkillAmp || 0) + (this.totalStats.allAmp || 0);
        if (allAmpValue > 0) {
            // Increase swordSkillAmp by allAmpValue
            if (typeof this.derivedStats.swordSkillAmp === 'number') {
                this.derivedStats.swordSkillAmp += allAmpValue;
            } else {
                this.derivedStats.swordSkillAmp = allAmpValue;
            }

            // Increase magicSkillAmp by allAmpValue
            if (typeof this.derivedStats.magicSkillAmp === 'number') {
                this.derivedStats.magicSkillAmp += allAmpValue;
            } else {
                this.derivedStats.magicSkillAmp = allAmpValue;
            }
        }

        // Apply additional stat modifiers here if needed in the future
    },

    // Set up tab events
    setupTabEvents: function() {
        // Stats summary tab buttons
        const summaryTabs = document.querySelectorAll('.fg-summary-tab');
        summaryTabs.forEach(tab => {
            tab.addEventListener('click', () => {
                const tabId = tab.getAttribute('data-tab');
                this.setActiveStatsTab(tabId);
            });
        });
    },

    // Set the active stats tab
    setActiveStatsTab: function(tabId) {
        // Update tab buttons
        document.querySelectorAll('.fg-summary-tab').forEach(tab => {
            if (tab.getAttribute('data-tab') === tabId) {
                tab.classList.add('active');
            } else {
                tab.classList.remove('active');
            }
        });

        // Update tab content
        document.querySelectorAll('.fg-summary-tab-content').forEach(content => {
            if (content.id === `${tabId}-tab`) {
                content.classList.add('active');
            } else {
                content.classList.remove('active');
            }
        });
    },

    // Update the stats summary UI
    updateStatsSummaryUI: function(totalStats) {
        // Get stats list containers
        const atkStatsContainer = document.getElementById('atk-stats-list');
        const defStatsContainer = document.getElementById('def-stats-list');
        const otherStatsContainer = document.getElementById('other-stats-list');

        // Skip if elements don't exist
        if (!atkStatsContainer || !defStatsContainer || !otherStatsContainer) {
            return;
        }

        // Clear containers
        atkStatsContainer.innerHTML = '';
        defStatsContainer.innerHTML = '';
        otherStatsContainer.innerHTML = '';

        // Define stat categories
        const allStats = {
            offensive: {
                general: [],
                pvp: [],
                pve: []
            },
            defensive: {
                general: [],
                pvp: [],
                pve: []
            },
            utility: {
                general: []
            }
        };

        // Populate stats from StatsConfig if available
        if (typeof StatsConfig !== 'undefined') {
            // Process all available stats from the base list
            Object.keys(StatsConfig.stats).forEach(statId => {
                const stat = StatsConfig.stats[statId];

                // Skip invalid entries or the default fallback entry
                if (!stat || typeof stat !== 'object' || statId === 'default') return;

                // Skip entries without a proper category
                if (!stat.category || !['offensive', 'defensive', 'utility'].includes(stat.category)) {
                    return;
                }

                // Add base stat to general column
                if (stat.category === 'offensive') {
                    allStats.offensive.general.push({ id: statId, name: stat.name });
                } else if (stat.category === 'defensive') {
                    allStats.defensive.general.push({ id: statId, name: stat.name });
                } else if (stat.category === 'utility') {
                    allStats.utility.general.push({ id: statId, name: stat.name });
                }

                // Add variant stats to their respective columns
                if (stat.variants && Array.isArray(stat.variants)) {
                    stat.variants.forEach(variant => {
                        // Generate variant stat ID (e.g., "pvpAttack")
                        const variantId = variant + statId.charAt(0).toUpperCase() + statId.slice(1);

                        // Generate variant name (e.g., "PVP Attack")
                        const variantName = `${variant.toUpperCase()} ${stat.name}`;

                        // Add to appropriate category and column
                        if (stat.category === 'offensive') {
                            if (variant === 'pvp') {
                                allStats.offensive.pvp.push({ id: variantId, name: variantName });
                            } else if (variant === 'pve') {
                                allStats.offensive.pve.push({ id: variantId, name: variantName });
                            }
                        } else if (stat.category === 'defensive') {
                            if (variant === 'pvp') {
                                allStats.defensive.pvp.push({ id: variantId, name: variantName });
                            } else if (variant === 'pve') {
                                allStats.defensive.pve.push({ id: variantId, name: variantName });
                            }
                        }
                    });
                }
            });

            // No alphabetical sorting - stats will display in the order defined in StatsConfig
        }

        // Create 3-column layouts for each tab
        const createStatTable = (statsGroup, statsData) => {
            const container = document.createElement('div');
            container.className = 'fg-stats-table';

            // Create columns
            const generalCol = document.createElement('div');
            generalCol.className = 'fg-stats-column';

            const pvpCol = document.createElement('div');
            pvpCol.className = 'fg-stats-column';

            const pveCol = document.createElement('div');
            pveCol.className = 'fg-stats-column';

            // Function to render a stat
            const renderStat = (statDef) => {
                if (!statDef) return null;

                // Get stat value
                const value = statsData[statDef.id] || 0;

                // Create stat element
                const statElement = this.createStatElement(statDef.name, this.formatStatValue(statDef.id, value), statDef.id);
                return statElement;
            };

            // Populate general stats
            if (Array.isArray(statsGroup.general)) {
                statsGroup.general.forEach(statDef => {
                    const element = renderStat(statDef);
                    if (element) generalCol.appendChild(element);
                });
            }

            // Populate PVP stats
            if (Array.isArray(statsGroup.pvp)) {
                statsGroup.pvp.forEach(statDef => {
                    const element = renderStat(statDef);
                    if (element) pvpCol.appendChild(element);
                });
            }

            // Populate PVE stats
            if (Array.isArray(statsGroup.pve)) {
                statsGroup.pve.forEach(statDef => {
                    const element = renderStat(statDef);
                    if (element) pveCol.appendChild(element);
                });
            }

            // Add columns to container
            container.appendChild(generalCol);
            container.appendChild(pvpCol);
            container.appendChild(pveCol);

            return container;
        };

        // Create and add stat tables to each tab
        atkStatsContainer.appendChild(createStatTable(allStats.offensive, totalStats));
        defStatsContainer.appendChild(createStatTable(allStats.defensive, totalStats));
        otherStatsContainer.appendChild(createStatTable(allStats.utility, totalStats));
    },

    // Format stat value based on stat type
    formatStatValue: function(statId, value) {
        // Special case for critical rate
        if (statId === 'critRate') {
            const maxCritRate = this.derivedStats.maxCritRate || 50;
            return `${value}%/${maxCritRate}%`;
        }

        // Use StatsConfig to format the value
        return StatsConfig.formatStatValue(statId, value);
    },

    // Create a stat element for display
    createStatElement: function(name, value, statId) {
        const element = document.createElement('div');
        element.className = 'fg-stat-item';
        element.setAttribute('data-stat', statId);

        element.innerHTML = `
            <span class="fg-stat-name">${name}</span>
            <span class="fg-stat-value">${value}</span>
        `;

        return element;
    }
};