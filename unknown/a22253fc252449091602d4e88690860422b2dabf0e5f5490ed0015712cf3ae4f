/**
 * Force Wing System Styles
 * Provides styling for the force wing progression system
 */

/* Main container */
.fg-force-wing-container {
    padding: 20px;
    margin: 0 auto;
}

/* Header section with level controls */
.fg-force-wing-header {
    margin-bottom: 30px;
    text-align: center;
}

.fg-force-wing-header h2 {
    margin-bottom: 15px;
    color: #333;
    font-size: 24px;
}

.fg-force-wing-level-section {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 15px;
    flex-wrap: wrap;
}

.fg-force-wing-level-section label {
    font-weight: bold;
    color: #555;
}

#fg-force-wing-level {
    padding: 8px 12px;
    border: 2px solid #ddd;
    border-radius: 4px;
    font-size: 16px;
    width: 100px;
    text-align: center;
}

#fg-force-wing-level:focus {
    outline: none;
    border-color: #4CAF50;
    box-shadow: 0 0 5px rgba(76, 175, 80, 0.3);
}

.fg-force-wing-level-stats {
    color: #4CAF50;
    font-weight: bold;
    font-size: 14px;
}

/* Force Wing Skill Tree Container - simple approach */
.fg-force-wing-skill-tree {
    position: relative;
    display: flex;
    justify-content: center;
    margin: 30px auto;
    /* REMOVED max-width constraint - let image control the size */
}

/* Background image - using img element approach like equipment system */
.fg-force-wing-background {
    position: relative;
    display: inline-block;
}

.fg-force-wing-ui-image {
    width: 550px;
    height: auto;
    display: block;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.5);
}

/* Slots container overlay */
.fg-force-wing-slots-container {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 2;
}

/* Individual slot styling (following stellar system pattern) */
.fg-force-wing-slot {
    position: absolute;
    width: 70px;
    height: 70px;
    background-image: url('../assets/images/force-wing-ui/slot_force_wing.png');
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center;
    transform: translate(-50%, -50%); /* Center on coordinates like stellar system */
    cursor: pointer;
    z-index: 3;
    transition: all 0.3s ease;
}

.fg-force-wing-slot:hover {
    transform: translate(-50%, -50%) scale(1.1);
    filter: brightness(1.2);
}

.fg-force-wing-slot.filled {
    filter: brightness(1.3) saturate(1.2);
}

/* Slot content styling */
.fg-force-wing-slot-content {
    position: relative;
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    pointer-events: none; /* Allow clicks to pass through to slot */
}

.fg-force-wing-slot-icon-container {
    position: relative;
    width: 40px;
    height: 40px;
    display: flex;
    justify-content: center;
    align-items: center;
    overflow: visible; /* Allow level indicator to overflow */
    pointer-events: none;
}

.fg-force-wing-slot-icon {
    width: 40px;
    height: 40px;
    object-fit: cover;
    object-position: center;
    border-radius: 50%;
    filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.5));
}

.fg-force-wing-slot-level {
    position: absolute;
    bottom: -6px;
    right: -6px;
    background-color: rgba(0, 0, 0, 0.8);
    color: #fff;
    font-size: 10px;
    font-weight: bold;
    width: 16px;
    height: 16px;
    border-radius: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 7;
    box-shadow: 0 0 3px rgba(0, 0, 0, 0.8);
    text-shadow: 0 1px 1px rgba(0, 0, 0, 0.8);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.fg-force-wing-slot.filled:hover {
    transform: translate(-50%, -50%) scale(1.1);
    filter: brightness(1.4) saturate(1.3);
}

/* Slot content styles - will be added when slot system is implemented */

/* Summary section */
.fg-force-wing-summary-section {
    margin-top: 30px;
    padding: 20px;
    background: rgba(28, 30, 34, 0.5);
    border-radius: 8px;
    border: 1px solid #3c3f44;
}

.fg-force-wing-summary-section h3 {
    margin-bottom: 15px;
    color: #f0f0f0;
    text-align: center;
}

.fg-force-wing-summary-content {
    min-height: 50px;
}

.fg-force-wing-summary-content .no-stats {
    text-align: center;
    color: #888;
    font-style: italic;
}

/* Responsive design */
@media (max-width: 768px) {
    .fg-force-wing-container {
        padding: 15px;
    }

    .fg-force-wing-level-section {
        flex-direction: column;
        gap: 10px;
    }

    /* Slot responsive styles - will be added when slot system is implemented */
}

@media (max-width: 480px) {
    .fg-force-wing-header h2 {
        font-size: 20px;
    }

    /* Slot responsive styles - will be added when slot system is implemented */
}
