<div id="fg-force-wing-system" class="fg-system-panel">
    <!-- Force Wing System -->
    <div class="fg-force-wing-container">
        <div class="fg-force-wing-header">
            <h2>Force Wing System</h2>
            <div class="fg-force-wing-level-section">
                <label for="fg-force-wing-level">Wing Level:</label>
                <input type="number" id="fg-force-wing-level" min="1" max="400" value="1" />
                <span class="fg-force-wing-level-stats">+1 HP, +1 All Attack, +1 Defense</span>
            </div>
        </div>

        <!-- Force Wing UI Container -->
        <div class="fg-force-wing-skill-tree">
            <div class="fg-force-wing-background">
                <!-- Background image as img element -->
                <img src="<?php echo plugin_dir_url(dirname(__FILE__)) . '../assets/images/force-wing-ui/force_wing_ui_final.png'; ?>"
                     alt="Force Wing UI" class="fg-force-wing-ui-image" />

                <!-- Slots overlay container -->
                <div class="fg-force-wing-slots-container">
                    <!-- 12 slots will be positioned here with normalized coordinates -->
                </div>
            </div>
        </div>

        <!-- Stats Summary Section -->
        <div class="fg-force-wing-summary-section">
            <h3>Force Wing Stats Summary</h3>
            <div class="fg-force-wing-summary-content">
                <p class="no-stats">No additional stats selected yet.</p>
            </div>
        </div>
    </div>
</div>