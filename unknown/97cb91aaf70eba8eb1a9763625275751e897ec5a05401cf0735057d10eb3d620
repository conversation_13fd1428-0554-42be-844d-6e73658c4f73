/**
 * Weapons Data for Build Planner
 * Contains definitions for all weapon items, their stats, and upgrade paths
 */

window.WeaponsData = {
    /**
     * All available weapons in the game
     */
    weapons: [
        // Weapons are now generated from templates in the initialization code below
    ],

    /**
     * Material grade mapping
     */
    materialGrades: {
        mithril: 'high',
        archridium: 'highest',
        palladium: 'ultimate',
        demonite: 'ultimate',
        dragonium: 'ultimate'
    },

    /**
     * Templates for highest grade weapon types (mithril, archridium, palladium)
     * This reduces code duplication since these weapons share upgrade patterns
     */
    weaponTemplates: {
        // Weapon type templates with their base stats per material grade
        orb: {
            type: 'weapon',
            subtype: 'orb',
            material: 'orb',
            class: 'mage',
            maxSlots: 3,
            // Base stats and max extreme upgrades per material grade
            grades: {
                dragonium: {
                    baseStats: {
                        attack: 370,
                        magicAttack: 240,
                        attackRate: 105
                    },
                    maxExtremeLevel: 7,
                    imagePath: 'orb dragonium final.png',
                    description: 'An orb crafted from the legendary dragonium crystal. Immense magical power flows through it, enhancing spell potency.'
                },
                demonite: {
                    baseStats: {
                        attack: 360,
                        magicAttack: 230,
                        attackRate: 110
                    },
                    maxExtremeLevel: 7,
                    imagePath: 'orb demonite final.png',
                    description: 'A sinister orb made of rare demonite. Channels dark energies for devastating magical attacks.'
                },
                palladium: {
                    baseStats: {
                        attack: 350,
                        magicAttack: 220,
                        attackRate: 100
                    },
                    maxExtremeLevel: 6,
                    imagePath: 'orb palladium final.png',
                    description: 'A powerful orb made of rare palladium crystal. Enhances magical abilities and critical strikes.'
                },
                archridium: {
                    baseStats: {
                        attack: 320,
                        magicAttack: 200,
                        attackRate: 95
                    },
                    maxExtremeLevel: 6,
                    imagePath: 'orb archridium final.png',
                    description: 'An orb crafted from the mystic metal archridium. Provides excellent magical power.'
                },
                mithril: {
                    baseStats: {
                        attack: 290,
                        magicAttack: 180,
                        attackRate: 90
                    },
                    maxExtremeLevel: 5,
                    imagePath: 'orb mithril final.png',
                    description: 'A finely crafted orb of mithril. Enhances magical abilities.'
                }
            }
        },
        crystal: {
            type: 'weapon',
            subtype: 'crystal',
            material: 'orb', // Uses same material upgrade table as orbs
            class: 'mage',
            maxSlots: 3,
            // Base stats and max extreme upgrades per material grade
            grades: {
                dragonium: {
                    baseStats: {
                        attack: 340,
                        magicAttack: 260,
                        attackRate: 100
                    },
                    maxExtremeLevel: 7,
                    imagePath: 'crystal dragonium final.png',
                    description: 'A crystal forged from the core of dragon remains. Perfectly channels magical energies for devastating spells.'
                },
                demonite: {
                    baseStats: {
                        attack: 330,
                        magicAttack: 250,
                        attackRate: 105
                    },
                    maxExtremeLevel: 7,
                    imagePath: 'crystal demonite final.png',
                    description: 'A dark crystal formed from demonic essence. Amplifies magical power with chaotic energies.'
                },
                palladium: {
                    baseStats: {
                        attack: 320,
                        magicAttack: 240,
                        attackRate: 95
                    },
                    maxExtremeLevel: 7,
                    imagePath: 'crystal palladium final.png',
                    description: 'A powerful crystal made of rare palladium. Maximizes magical potential and enhances casting speed.'
                },
                archridium: {
                    baseStats: {
                        attack: 290,
                        magicAttack: 220,
                        attackRate: 90
                    },
                    maxExtremeLevel: 6,
                    imagePath: 'crystal archridium final.png',
                    description: 'A crystal crafted from the mystic metal archridium. Provides excellent magical power and cast rates.'
                },
                mithril: {
                    baseStats: {
                        attack: 260,
                        magicAttack: 200,
                        attackRate: 85
                    },
                    maxExtremeLevel: 5,
                    imagePath: 'crystal mithril final.png',
                    description: 'A finely crafted crystal of mithril. Enhances magical abilities.'
                }
            }
        },
        blade: {
            type: 'weapon',
            subtype: 'blade',
            material: 'sword',
            class: 'warrior',
            maxSlots: 3,
            // Base stats and max extreme upgrades per material grade
            grades: {
                dragonium: {
                    baseStats: {
                        attack: 400,
                        magicAttack: 170,
                        attackRate: 115
                    },
                    maxExtremeLevel: 7,
                    imagePath: 'blade dragonium final.png',
                    description: 'A blade forged from dragonium scales. Cuts through enemies with incredible sharpness and precision.'
                },
                demonite: {
                    baseStats: {
                        attack: 390,
                        magicAttack: 160,
                        attackRate: 120
                    },
                    maxExtremeLevel: 7,
                    imagePath: 'blade demonite final.png',
                    description: 'A blade infused with demonite. Strikes with supernatural speed and leaves wounds that burn like hellfire.'
                },
                palladium: {
                    baseStats: {
                        attack: 380,
                        magicAttack: 150,
                        attackRate: 110
                    },
                    maxExtremeLevel: 7,
                    imagePath: 'blade palladium final.png',
                    description: 'A powerful blade forged from rare palladium. Delivers precise strikes at lightning speed.'
                },
                archridium: {
                    baseStats: {
                        attack: 350,
                        magicAttack: 130,
                        attackRate: 105
                    },
                    maxExtremeLevel: 6,
                    imagePath: 'blade archridium final.png',
                    description: 'A blade crafted from mystic archridium metal. Provides excellent cutting power.'
                },
                mithril: {
                    baseStats: {
                        attack: 320,
                        magicAttack: 110,
                        attackRate: 100
                    },
                    maxExtremeLevel: 5,
                    imagePath: 'blade mithril final.png',
                    description: 'A finely crafted blade of mithril. Perfect balance for swift attacks.'
                }
            }
        },
        katana: {
            type: 'weapon',
            subtype: 'katana',
            material: 'sword',
            class: 'warrior',
            maxSlots: 3,
            // Base stats and max extreme upgrades per material grade
            grades: {
                dragonium: {
                    baseStats: {
                        attack: 420,
                        magicAttack: 150,
                        attackRate: 105
                    },
                    maxExtremeLevel: 7,
                    imagePath: 'katana dragonium final.png',
                    description: 'A legendary katana forged with dragonium. Each swing creates a wind that can slice through armor like paper.'
                },
                demonite: {
                    baseStats: {
                        attack: 410,
                        magicAttack: 140,
                        attackRate: 110
                    },
                    maxExtremeLevel: 7,
                    imagePath: 'katana demonite final.png',
                    description: 'A sinister katana made from demonite. Feeds on the lifeforce of enemies it cuts, strengthening the wielder.'
                },
                palladium: {
                    baseStats: {
                        attack: 400,
                        magicAttack: 130,
                        attackRate: 100
                    },
                    maxExtremeLevel: 7,
                    imagePath: 'katana palladium final.png',
                    description: 'A masterfully crafted katana of rare palladium. Delivers devastating damage with precision.'
                },
                archridium: {
                    baseStats: {
                        attack: 370,
                        magicAttack: 110,
                        attackRate: 95
                    },
                    maxExtremeLevel: 6,
                    imagePath: 'katana archridium final.png',
                    description: 'A katana forged from mystic archridium. Provides excellent cutting power.'
                },
                mithril: {
                    baseStats: {
                        attack: 340,
                        magicAttack: 90,
                        attackRate: 90
                    },
                    maxExtremeLevel: 5,
                    imagePath: 'katana mithril final.png',
                    description: 'A finely crafted katana of mithril. Delivers powerful strikes.'
                }
            }
        },
        // Add additional weapon types as needed (chakram, greatsword, etc.)
        greatsword: {
            type: 'weapon',
            subtype: 'greatsword',
            material: 'sword',
            class: 'warrior',
            maxSlots: 3,
            // Base stats and max extreme upgrades per material grade
            grades: {
                dragonium: {
                    baseStats: {
                        attack: 470,
                        magicAttack: 130,
                        attackRate: 85
                    },
                    maxExtremeLevel: 7,
                    imagePath: 'greatsword dragonium final.png',
                    description: 'A colossal greatsword of dragonium, said to be forged from a dragon\'s spine. One swing can cleave mountains.'
                },
                demonite: {
                    baseStats: {
                        attack: 460,
                        magicAttack: 120,
                        attackRate: 90
                    },
                    maxExtremeLevel: 7,
                    imagePath: 'greatsword demonite final.png',
                    description: 'A demonic greatsword that pulses with dark energy. Strikes fear into enemies before crushing them.'
                },
                palladium: {
                    baseStats: {
                        attack: 450,
                        magicAttack: 110,
                        attackRate: 80
                    },
                    maxExtremeLevel: 7,
                    imagePath: 'greatsword palladium final.png',
                    description: 'A massive greatsword forged from rare palladium. Devastating power.'
                },
                archridium: {
                    baseStats: {
                        attack: 420,
                        magicAttack: 90,
                        attackRate: 75
                    },
                    maxExtremeLevel: 6,
                    imagePath: 'greatsword archridium final.png',
                    description: 'A greatsword crafted from mystic archridium. Provides overwhelming strength.'
                },
                mithril: {
                    baseStats: {
                        attack: 390,
                        magicAttack: 70,
                        attackRate: 70
                    },
                    maxExtremeLevel: 5,
                    imagePath: 'greatsword mithril final.png',
                    description: 'A finely crafted greatsword of mithril. Brings great power to battle.'
                }
            }
        },
        daikatana: {
            type: 'weapon',
            subtype: 'daikatana',
            material: 'sword',
            class: 'warrior',
            maxSlots: 3,
            // Base stats and max extreme upgrades per material grade
            grades: {
                dragonium: {
                    baseStats: {
                        attack: 450,
                        magicAttack: 150,
                        attackRate: 90
                    },
                    maxExtremeLevel: 7,
                    imagePath: 'daikatana dragonium final.png',
                    description: 'A legendary daikatana of dragonium. Its blade gleams with the colors of dragon scales and cuts with unrivaled precision.'
                },
                demonite: {
                    baseStats: {
                        attack: 440,
                        magicAttack: 140,
                        attackRate: 95
                    },
                    maxExtremeLevel: 7,
                    imagePath: 'daikatana demonite final.png',
                    description: 'A terrifying daikatana of demonite. Each swing leaves trails of unholy energy that harm lingering foes.'
                },
                palladium: {
                    baseStats: {
                        attack: 430,
                        magicAttack: 130,
                        attackRate: 85
                    },
                    maxExtremeLevel: 7,
                    imagePath: 'daikatana palladium final.png',
                    description: 'A massive daikatana forged from rare palladium. Combines power with precision.'
                },
                archridium: {
                    baseStats: {
                        attack: 400,
                        magicAttack: 110,
                        attackRate: 80
                    },
                    maxExtremeLevel: 6,
                    imagePath: 'daikatana archridium final.png',
                    description: 'A daikatana crafted from mystic archridium. An imposing weapon of great reach.'
                },
                mithril: {
                    baseStats: {
                        attack: 370,
                        magicAttack: 90,
                        attackRate: 75
                    },
                    maxExtremeLevel: 5,
                    imagePath: 'daikatana mithril final.png',
                    description: 'A finely crafted daikatana of mithril. Perfect for devastating sweeping attacks.'
                }
            }
        },
        chakram: {
            type: 'weapon',
            subtype: 'chakram',
            material: 'orb',
            class: 'ranger',
            maxSlots: 3,
            // Base stats and max extreme upgrades per material grade
            grades: {
                dragonium: {
                    baseStats: {
                        attack: 380,
                        magicAttack: 200,
                        attackRate: 125
                    },
                    maxExtremeLevel: 7,
                    imagePath: 'chakram dragonium final.png',
                    description: 'A chakram formed from dragonium scales. Returns to the wielder with supernatural accuracy and cuts through any defense.'
                },
                demonite: {
                    baseStats: {
                        attack: 370,
                        magicAttack: 190,
                        attackRate: 130
                    },
                    maxExtremeLevel: 7,
                    imagePath: 'chakram demonite final.png',
                    description: 'A demonite chakram that leaves trails of dark energy. Can strike multiple foes in a single throw.'
                },
                palladium: {
                    baseStats: {
                        attack: 360,
                        magicAttack: 180,
                        attackRate: 120
                    },
                    maxExtremeLevel: 7,
                    imagePath: 'chakram palladium final.png',
                    description: 'A perfectly balanced chakram made of rare palladium. Enables rapid attacks at range.'
                },
                archridium: {
                    baseStats: {
                        attack: 330,
                        magicAttack: 160,
                        attackRate: 115
                    },
                    maxExtremeLevel: 6,
                    imagePath: 'chakram archridium final.png',
                    description: 'A chakram crafted from mystic archridium. Provides excellent range and speed.'
                },
                mithril: {
                    baseStats: {
                        attack: 300,
                        magicAttack: 140,
                        attackRate: 110
                    },
                    maxExtremeLevel: 5,
                    imagePath: 'chakram mithril final.png',
                    description: 'A finely crafted chakram of mithril. Perfect for rapid ranged attacks.'
                }
            }
        }
    },

    /**
     * Cumulative upgrade stat bonuses for highest grade weapons (levels 1-20)
     * Based on the official upgrade table - these are TOTAL BONUS stats added to base stats
     * Table shows incremental bonuses per level within ranges: 1-3, 4-6, 7-9, 10-12, 13-15, 16, 17, 18, 19, 20
     */
    upgradeStats: {
        // Two-handed weapons (Greatsword/Daikatana)
        // Per level bonuses: 1-3(+12,+40,+10), 4-6(+18,+52,+16), 7-9(+24,+64,+22), 10-12(+30,+76,+28), 13-15(+36,+88,+34), 16(+47,+90,+45), 17(+38,+92,+36), 18(+39,+94,+37), 19(+40,+96,+38), 20(+41,+98,+39)
        twoHanded: {
            attack: [0, 12, 24, 36, 54, 72, 90, 114, 138, 162, 192, 222, 252, 288, 324, 360, 407, 445, 484, 524, 565],
            attackRate: [0, 40, 80, 120, 172, 224, 276, 340, 404, 468, 544, 620, 696, 784, 872, 960, 1050, 1142, 1236, 1332, 1430],
            magicAttack: [0, 10, 20, 30, 46, 62, 78, 100, 122, 144, 172, 200, 228, 262, 296, 330, 375, 411, 448, 486, 525]
        },
        // One-handed weapons (Blade/Katana/Chakram)
        // Per level bonuses: 1-3(+6,+20,+5), 4-6(+9,+26,+8), 7-9(+12,+32,+11), 10-12(+15,+38,+14), 13-15(+18,+44,+17), 16(+29,+46,+28), 17(+20,+48,+19), 18(+21,+50,+20), 19(+22,+52,+21), 20(+23,+54,+22)
        oneHanded: {
            attack: [0, 6, 12, 18, 27, 36, 45, 57, 69, 81, 96, 111, 126, 144, 162, 180, 209, 229, 250, 272, 295],
            attackRate: [0, 20, 40, 60, 86, 112, 138, 170, 202, 234, 272, 310, 348, 392, 436, 480, 526, 574, 624, 676, 730],
            magicAttack: [0, 5, 10, 15, 23, 31, 39, 50, 61, 72, 86, 100, 114, 131, 148, 165, 193, 212, 232, 253, 275]
        },
        // Magic weapons (Orb/Crystal)
        // Per level bonuses: 1-3(+5,+20,+6), 4-6(+8,+26,+9), 7-9(+11,+32,+12), 10-12(+14,+38,+15), 13-15(+17,+44,+18), 16(+28,+46,+29), 17(+19,+48,+20), 18(+20,+50,+21), 19(+21,+52,+22), 20(+22,+54,+23)
        magic: {
            attack: [0, 5, 10, 15, 23, 31, 39, 50, 61, 72, 86, 100, 114, 131, 148, 165, 193, 212, 232, 253, 275],
            attackRate: [0, 20, 40, 60, 86, 112, 138, 170, 202, 234, 272, 310, 348, 392, 436, 480, 526, 574, 624, 676, 730],
            magicAttack: [0, 6, 12, 18, 27, 36, 45, 57, 69, 81, 96, 111, 126, 144, 162, 180, 209, 229, 250, 272, 295]
        }
    },

    /**
     * Helper to get the correct upgrade stat category for a weapon
     */
    getUpgradeStatType: function(weaponType) {
        if (weaponType === 'greatsword' || weaponType === 'daikatana') {
            return 'twoHanded';
        } else if (weaponType === 'blade' || weaponType === 'katana' || weaponType === 'chakram') {
            return 'oneHanded';
        } else {
            return 'magic'; // Orb and Crystal
        }
    },

    /**
     * Get exact upgrade stat value at a given level
     */
    getUpgradeStat: function(weaponType, statType, level) {
        const category = this.getUpgradeStatType(weaponType);
        // Handle upgrade levels beyond our table (use last value)
        const validLevel = Math.min(level, this.upgradeStats[category][statType].length - 1);
        return this.upgradeStats[category][statType][validLevel];
    },



    /**
     * Extreme upgrade levels for different weapon types
     */
    extremeUpgrades: {
        // For all weapon types (one-handed values, two-handed are doubled)
        level7: [
            // Level 0 - No bonuses
            [],
            // Level 1
            [
                { stat: 'allAttackUp', value: 20 },
                { stat: 'attackRate', value: 50 },
                { stat: 'accuracy', value: 80 }
            ],
            // Level 2
            [
                { stat: 'allAttackUp', value: 60 },
                { stat: 'attackRate', value: 120 },
                { stat: 'accuracy', value: 140 }
            ],
            // Level 3
            [
                { stat: 'allAttackUp', value: 100 },
                { stat: 'attackRate', value: 190 },
                { stat: 'accuracy', value: 200 },
                { stat: 'critDamage', value: 7 }
            ],
            // Level 4
            [
                { stat: 'allAttackUp', value: 130 },
                { stat: 'attackRate', value: 270 },
                { stat: 'accuracy', value: 260 },
                { stat: 'critDamage', value: 15 },
                { stat: 'penetration', value: 30 }
            ],
            // Level 5
            [
                { stat: 'allAttackUp', value: 160 },
                { stat: 'attackRate', value: 360 },
                { stat: 'accuracy', value: 330 },
                { stat: 'critDamage', value: 23 },
                { stat: 'penetration', value: 55 }
            ],
            // Level 6
            [
                { stat: 'allAttackUp', value: 200 },
                { stat: 'attackRate', value: 460 },
                { stat: 'accuracy', value: 420 },
                { stat: 'critDamage', value: 31 },
                { stat: 'penetration', value: 80 }
            ],
            // Level 7 (only highest material weapons can reach this)
            [
                { stat: 'allAttackUp', value: 250 },
                { stat: 'attackRate', value: 600 },
                { stat: 'accuracy', value: 420 },
                { stat: 'critDamage', value: 40 },
                { stat: 'penetration', value: 110 }
            ]
        ]

        /*
         * Note for two-handed weapons: Their extreme upgrade stats are exactly
         * double the values of one-handed weapons for each level. For example,
         * Level 1 gives +40 Attack Up for two-handed vs +20 for one-handed.
         * Implementation automatically multiplies values by 2 for two-handed weapons.
         */
    },

    /**
     * Divine upgrade levels for weapons (0-15) based on grade
     */
    divineUpgrades: {
        // High grade (mithril) upgrade stats
        high: {
            allAttackUp: [0, 18, 22, 26, 30, 34, 38, 42, 46, 50, 54, 58, 62, 66, 70, 74],
            attackRate: [0, 5, 10, 15, 20, 30, 40, 50, 60, 70, 90, 110, 130, 150, 180, 210],
            critDamage: [0, 0, 0, 0, 0, 0, 0, 1, 2, 3, 4, 5, 6, 7, 9, 11],
            accuracy: [0, 0, 0, 0, 0, 0, 0, 0, 0, 60, 75, 90, 105, 120, 135, 150],
            penetration: [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 25, 45]
        },

        // Highest grade (archridium) upgrade stats
        highest: {
            allAttackUp: [0, 27, 31, 35, 39, 43, 47, 51, 55, 59, 63, 67, 71, 75, 79, 83],
            attackRate: [0, 10, 20, 30, 40, 60, 80, 100, 120, 140, 160, 180, 200, 220, 240, 270],
            critDamage: [0, 0, 0, 0, 0, 0, 0, 1, 2, 3, 4, 5, 6, 8, 10, 13],
            accuracy: [0, 0, 0, 0, 0, 0, 0, 0, 0, 80, 95, 120, 135, 150, 165, 180],
            penetration: [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 10, 30, 50]
        },

        // Ultimate grade (palladium) upgrade stats
        ultimate: {
            allAttackUp: [0, 36, 40, 44, 48, 52, 56, 60, 64, 68, 72, 76, 80, 84, 88, 92],
            attackRate: [0, 15, 30, 45, 60, 90, 120, 150, 180, 210, 230, 250, 270, 290, 300, 330],
            critDamage: [0, 0, 0, 0, 0, 0, 0, 1, 2, 3, 4, 5, 6, 9, 11, 15],
            accuracy: [0, 0, 0, 0, 0, 0, 0, 0, 0, 100, 115, 150, 165, 180, 195, 210],
            penetration: [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 15, 35, 55]
        }
    },

    /**
     * Epic options available for weapons
     */
    epicOptions: [
        {
            id: 'critDamage',
            name: 'Critical DMG.',
            levels: [
                { level: 0, value: 20 }, // 20%
                { level: 1, value: 22 }, // 22%
                { level: 2, value: 25 }  // 25%
            ]
        },
        {
            id: 'critRate',
            name: 'Critical Rate',
            levels: [
                { level: 0, value: 15 }, // 15%
                { level: 1, value: 17 }, // 17%
                { level: 2, value: 20 }  // 20%
            ]
        },
        {
            id: 'allSkillAmp',
            name: 'Skill Amp.',
            levels: [
                { level: 0, value: 10 }, // 10%
                { level: 1, value: 12 }, // 12%
                { level: 2, value: 15 }  // 15%
            ]
        }
    ],

    /**
     * Regular options that can be slotted into weapons
     */
    slotOptions: [
        { id: 'critDamage', name: 'Critical DMG.', value: 20 }, // 20%
        { id: 'critRate', name: 'Critical Rate', value: 15 }, // 15%
        { id: 'swordSkillAmp', name: 'Sword Amp.', value: 10 }, // 10%
        { id: 'magicSkillAmp', name: 'Magic Amp.', value: 10 }, // 10%
        { id: 'accuracy', name: 'Accuracy', value: 100 } // +100
    ],

    /**
     * Get divine upgrade stats based on weapon grade and level
     */
    getDivineUpgradeStats: function(materialGrade, level) {
        // Map material grade to high/highest/ultimate
        const gradeKey = this.materialGrades[materialGrade] || 'high';

        // Get stats for this grade at this level
        const stats = {};
        const gradeStats = this.divineUpgrades[gradeKey];

        // Only include stats that have values at this level
        if (gradeStats.allAttackUp[level] > 0) stats.allAttackUp = gradeStats.allAttackUp[level];
        if (gradeStats.attackRate[level] > 0) stats.attackRate = gradeStats.attackRate[level];
        if (gradeStats.critDamage[level] > 0) stats.critDamage = gradeStats.critDamage[level];
        if (gradeStats.accuracy[level] > 0) stats.accuracy = gradeStats.accuracy[level];
        if (gradeStats.penetration[level] > 0) stats.penetration = gradeStats.penetration[level];

        return stats;
    },

    /**
     * Initialize the weapons array from templates
     */
    initialize: function() {
        // Generate all weapons from templates
        const pluginImagePath = forceguidesPlannerData.pluginUrl + 'assets/images/equipment/';

        Object.keys(this.weaponTemplates).forEach(weaponType => {
            const template = this.weaponTemplates[weaponType];

            Object.keys(template.grades).forEach(materialGrade => {
                const gradeInfo = template.grades[materialGrade];

                // Create weapon object based on template
                const weapon = {
                    id: materialGrade + '_' + weaponType,
                    name: materialGrade.charAt(0).toUpperCase() + materialGrade.slice(1) + ' ' +
                          weaponType.charAt(0).toUpperCase() + weaponType.slice(1),
                    type: template.type,
                    subtype: template.subtype,
                    material: template.material,
                    class: template.class,
                    grade: this.materialGrades[materialGrade],
                    imagePath: pluginImagePath + gradeInfo.imagePath,
                    description: gradeInfo.description,
                    baseStats: gradeInfo.baseStats,
                    maxSlots: template.maxSlots,
                    maxExtremeLevel: gradeInfo.maxExtremeLevel,
                    // Add upgrade stat calculator functions for use in UI
                    getUpgradedAttack: function(level) {
                        return WeaponsData.getUpgradeStat(weaponType, 'attack', level);
                    },
                    getUpgradedAttackRate: function(level) {
                        return WeaponsData.getUpgradeStat(weaponType, 'attackRate', level);
                    },
                    getUpgradedMagicAttack: function(level) {
                        return WeaponsData.getUpgradeStat(weaponType, 'magicAttack', level);
                    }
                };

                // Add to weapons array
                this.weapons.push(weapon);
            });
        });
    }
};

// Initialize weapons when the data is loaded
(function() {
    // Wait for the DOM to be fully loaded
    document.addEventListener('DOMContentLoaded', function() {
        // Initialize weapons from templates
        if (window.WeaponsData) {
            window.WeaponsData.initialize();
        }
    });
})();