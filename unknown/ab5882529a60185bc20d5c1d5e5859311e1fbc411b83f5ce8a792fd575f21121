/**
 * Equipment System Styles
 */

/* Main container for equipment system */
.fg-equipment-container {
    display: flex;
    width: 100%;
    gap: 20px;
}

/* Equipment Image Container */
.fg-equipment-image-container {
    width: 60%;
    min-width: 350px;
    position: relative;
}

/* Equipment UI Image */
.fg-equipment-ui-image {
    width: 100%;
    display: block;
    border-top-left-radius: 25px;
    border-top-right-radius: 25px;
}

/* Image Overlay for clickable regions */
.fg-equipment-image-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 2;
}

/* Equipment Details Panel */
.fg-equipment-details-panel {
    width: 40%;
    min-width: 300px;
    background-color: #1a1a1a;
    border: 2px solid #333;
    border-radius: 5px;
    padding: 15px;
    color: #ccc;
}

.fg-equipment-details-title {
    margin-top: 0;
    color: #f8f8f8;
    border-bottom: 1px solid #444;
    padding-bottom: 10px;
    margin-bottom: 15px;
}

.fg-equipment-details-content {
    min-height: 200px;
}

.fg-equipment-details-placeholder {
    color: #777;
    text-align: center;
    margin-top: 50px;
}

/* Equipment Regions - Clickable areas */
.fg-equipment-region {
    position: absolute;
    background-color: transparent;
    z-index: 10;
    cursor: pointer;
}

/* Add an instruction tooltip at the start */
.fg-equipment-tooltip {
    position: absolute;
    top: 10px;
    left: 50%;
    transform: translateX(-50%);
    background-color: rgba(0, 0, 0, 0.7);
    color: white;
    padding: 8px 15px;
    border-radius: 5px;
    font-size: 14px;
    white-space: nowrap;
    pointer-events: none;
    z-index: 30;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.3);
    opacity: 0.9;
    animation: fg-tooltip-fade 5s forwards;
}

@keyframes fg-tooltip-fade {
    0%, 80% { opacity: 0.9; }
    100% { opacity: 0; }
}

/* Mobile styles for regions */
@media (max-width: 768px) {
    /* Removed indicator styles
    .fg-equipment-indicator {
        width: 20px;
        height: 20px;
        border-width: 3px;
    }

    .fg-equipment-region .fg-equipment-region-label {
        opacity: 0;
        font-size: 9px;
    }

    .fg-equipment-region:active .fg-equipment-region-label {
        opacity: 1;
        top: -15px;
    }
    */

    .fg-equipment-tooltip {
        font-size: 12px;
        padding: 5px 10px;
    }
}

/* Equipped Item */
.fg-equipped-item h4 {
    margin-top: 0;
    color: #4c8;
}

.fg-item-stats {
    margin: 15px 0;
    padding: 10px;
    background-color: #222;
    border-radius: 4px;
}

/* Empty Slot */
.fg-empty-slot {
    text-align: center;
    padding: 20px 0;
}

.fg-slot-type {
    color: #888;
    margin-bottom: 20px;
}

/* Buttons */
.fg-button {
    background-color: #2a2a2a;
    border: 1px solid #444;
    color: #ccc;
    padding: 8px 16px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    transition: background-color 0.2s;
}

.fg-button:hover {
    background-color: #3a3a3a;
}

.fg-equip-item-btn {
    background-color: #2c5282;
    color: #fff;
}

.fg-equip-item-btn:hover {
    background-color: #3a69a8;
}

.fg-remove-item-btn {
    background-color: #822c2c;
    color: #fff;
}

.fg-remove-item-btn:hover {
    background-color: #a83a3a;
}

/* Modal styles */
.fg-equipment-modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    overflow: auto;
    background-color: rgba(0, 0, 0, 0.7);
}

.fg-equipment-modal-content {
    background-color: #1a1a1a;
    margin: 5% auto;
    padding: 20px;
    border: 2px solid #444;
    width: 80%;
    max-width: 700px;
    border-radius: 5px;
    box-shadow: 0 0 20px rgba(0, 0, 0, 0.5);
    color: #ccc;
    position: relative;
}

.fg-equipment-modal-close {
    position: absolute;
    top: 10px;
    right: 15px;
    color: #aaa;
    font-size: 24px;
    font-weight: bold;
    cursor: pointer;
}

.fg-equipment-modal-close:hover {
    color: #fff;
}

#fg-equipment-modal-title {
    margin-top: 0;
    border-bottom: 1px solid #444;
    padding-bottom: 10px;
    color: #f8f8f8;
}

.fg-modal-buttons {
    display: flex;
    gap: 10px;
    justify-content: flex-end;
    margin-top: 20px;
}

@media (max-width: 768px) {
    .fg-equipment-container {
        flex-direction: column;
    }

    .fg-equipment-image-container,
    .fg-equipment-details-panel {
        width: 100%;
        min-width: 0;
    }

    .fg-equipment-image-container {
        margin-bottom: 20px;
    }

    .fg-modal-buttons {
        flex-direction: column;
    }

    .fg-equipment-modal-content {
        width: 95%;
        margin: 10% auto;
    }
}

.fg-coord-valid-ids {
    margin-top: 15px;
    padding: 10px;
    background-color: #222;
    border-radius: 4px;
}

.fg-coord-valid-ids p {
    margin-top: 0;
    margin-bottom: 5px;
    font-weight: bold;
    color: #aaa;
}

.fg-coord-id-list {
    font-family: monospace;
    line-height: 1.5;
    word-wrap: break-word;
}

.fg-coord-id-item {
    display: inline-block;
    background-color: #333;
    padding: 2px 5px;
    margin: 2px;
    border-radius: 3px;
    color: #ccc;
}

/* Equipped items container */
.fg-equipment-equipped-items-container {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 5;
    pointer-events: none;
}

/* Equipped item styling */
.fg-equipment-item {
    position: absolute;
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 6;
    pointer-events: none;
    transition: all 0.3s ease;
}

.fg-equipment-item-image {
    max-width: 85%;
    max-height: 85%;
    object-fit: contain;
    filter: drop-shadow(0 0 5px rgba(255, 255, 255, 0.5));
}

/* Item thumbnail in details panel */
.fg-item-thumbnail {
    width: 60px;
    height: 60px;
    object-fit: contain;
    border-radius: 5px;
    background-color: #2a2a2a;
    padding: 5px;
    margin-right: 15px;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.3);
}

.fg-item-header {
    display: flex;
    align-items: center;
    margin-bottom: 15px;
}

.fg-item-description {
    margin-bottom: 15px;
    font-style: italic;
    color: #aaa;
    line-height: 1.4;
}

.fg-item-stats {
    margin: 15px 0;
    padding: 10px;
    background-color: #222;
    border-radius: 4px;
}

.fg-item-stat {
    margin: 5px 0;
    display: flex;
    justify-content: space-between;
    border-bottom: 1px dotted #444;
    padding-bottom: 5px;
}

.fg-stat-name {
    color: #aaa;
}

.fg-stat-value {
    color: #4c8;
    font-weight: bold;
}

/* Item selection grid in modal */
.fg-item-selection-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
    gap: 15px;
    margin-bottom: 20px;
}

.fg-item-selection-card {
    background-color: #2a2a2a;
    border: 2px solid #444;
    border-radius: 5px;
    padding: 10px;
    text-align: center;
    cursor: pointer;
    transition: all 0.2s ease;
}

.fg-item-selection-card:hover {
    background-color: #3a3a3a;
    border-color: #666;
    transform: translateY(-5px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
}

.fg-item-selection-image {
    width: 80px;
    height: 80px;
    object-fit: contain;
    margin-bottom: 10px;
}

.fg-item-selection-name {
    font-size: 14px;
    font-weight: bold;
    color: #ddd;
}

@media (max-width: 768px) {
    .fg-item-selection-grid {
        grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
    }

    .fg-item-selection-image {
        width: 60px;
        height: 60px;
    }

    .fg-item-selection-name {
        font-size: 12px;
    }
}

/* Weapon Upgrade UI Styles */
.fg-weapon-upgrade-container {
    padding: 15px 0;
    color: #e0e0e0;
}

.fg-weapon-header {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
    background-color: rgba(0, 0, 0, 0.2);
    border-radius: 5px;
    padding: 10px;
}

.fg-weapon-image {
    width: 80px;
    height: 80px;
    object-fit: contain;
    margin-right: 15px;
    border-radius: 5px;
    background-color: rgba(0, 0, 0, 0.3);
    padding: 5px;
    border: 1px solid #444;
}

.fg-weapon-title h4 {
    margin: 0 0 5px 0;
    font-size: 18px;
    color: #fff;
}

.fg-weapon-type {
    color: #aaa;
    font-size: 14px;
}

.fg-upgrade-section {
    margin-bottom: 20px;
    padding: 12px;
    border-radius: 5px;
    background-color: rgba(0, 0, 0, 0.2);
    border: 1px solid #333;
}

.fg-upgrade-section h5 {
    margin-top: 0;
    margin-bottom: 10px;
    padding-bottom: 5px;
    border-bottom: 1px solid #444;
    color: #fff;
}

/* Base Stats Section */
.fg-base-stats {
    background-color: rgba(50, 50, 50, 0.3);
}

.fg-stat-item {
    display: flex;
    justify-content: space-between;
    padding: 5px 0;
}

/* Grade Upgrade Section - White/Light Gray like in-game */
.fg-grade-upgrade {
    background-color: rgba(200, 200, 200, 0.15);
    border-color: rgba(200, 200, 200, 0.3);
}

.fg-slider-container {
    display: flex;
    align-items: center;
    gap: 10px;
}

.fg-grade-slider, .fg-extreme-slider, .fg-divine-slider {
    width: calc(100% - 50px);
    cursor: pointer;
}

.fg-grade-value, .fg-extreme-value, .fg-divine-value {
    width: 40px;
    text-align: center;
    background-color: rgba(0, 0, 0, 0.3);
    border-radius: 3px;
    padding: 2px 0;
}

/* Epic Option Section - Dark Purple like in-game */
.fg-epic-option {
    background-color: rgba(75, 45, 120, 0.25);
    border-color: rgba(75, 45, 120, 0.5);
}

.fg-epic-option-row {
    display: flex;
    justify-content: space-between;
    margin-bottom: 10px;
}

.fg-epic-option-select {
    width: 70%;
    background-color: #2a2a2a;
    color: #fff;
    border: 1px solid #444;
    padding: 5px;
    border-radius: 3px;
}

.fg-epic-option-value {
    padding: 5px 10px;
    background-color: rgba(75, 45, 120, 0.4);
    border-radius: 3px;
    color: #fff;
    font-weight: bold;
}

.fg-epic-level-buttons {
    display: flex;
    gap: 5px;
}

.fg-epic-level-btn {
    flex: 1;
    padding: 5px;
    background-color: #2a2a2a;
    border: 1px solid #444;
    color: #ccc;
    border-radius: 3px;
    cursor: pointer;
    text-align: center;
    font-size: 12px;
}

.fg-epic-level-btn.fg-active {
    background-color: rgba(75, 45, 120, 0.6);
    border-color: rgba(75, 45, 120, 0.8);
    color: #fff;
}

/* Option Slots Section - Cool Blue like in-game */
.fg-option-slots {
    background-color: rgba(30, 90, 180, 0.25);
    border-color: rgba(30, 90, 180, 0.5);
}

.fg-slot-container {
    margin-bottom: 10px;
}

.fg-slot-row {
    display: flex;
    align-items: center;
    margin-bottom: 5px;
    padding: 5px;
    border-radius: 3px;
}

.fg-slot-active {
    background-color: rgba(30, 90, 180, 0.2);
    border: 1px solid rgba(30, 90, 180, 0.4);
}

.fg-slot-inactive {
    background-color: rgba(0, 0, 0, 0.3);
    border: 1px solid #333;
    opacity: 0.6;
}

.fg-slot-number {
    width: 20px;
    height: 20px;
    background-color: rgba(13, 110, 253, 0.3);
    color: #fff;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 10px;
    font-size: 12px;
    font-weight: bold;
}

.fg-slot-option-select {
    flex: 1;
    background-color: #2a2a2a;
    color: #fff;
    border: 1px solid #444;
    padding: 5px;
    border-radius: 3px;
}

.fg-slot-buttons {
    display: flex;
    gap: 5px;
}

.fg-slot-count-btn {
    flex: 1;
    padding: 5px;
    background-color: #2a2a2a;
    border: 1px solid #444;
    color: #ccc;
    border-radius: 3px;
    cursor: pointer;
    text-align: center;
    font-size: 12px;
}

.fg-slot-count-btn.fg-active {
    background-color: rgba(30, 90, 180, 0.6);
    border-color: rgba(30, 90, 180, 0.8);
    color: #fff;
}

.fg-slot-count-btn[disabled] {
    opacity: 0.5;
    cursor: not-allowed;
}

/* Extreme Upgrade Section - Dark Blue like in-game */
.fg-extreme-upgrade {
    background-color: rgba(20, 60, 120, 0.25);
    border-color: rgba(20, 60, 120, 0.5);
}

.fg-extreme-bonuses {
    margin-top: 10px;
}

/* Divine Upgrade Section - Dark Gold like in-game */
.fg-divine-upgrade {
    background-color: rgba(180, 120, 30, 0.25);
    border-color: rgba(180, 120, 30, 0.5);
}

.fg-divine-bonuses {
    margin-top: 10px;
}

/* Total Stats Section */
.fg-total-stats {
    background-color: rgba(220, 53, 69, 0.2);
    border-color: rgba(220, 53, 69, 0.4);
}

.fg-total-stats-list {
    max-height: 200px;
    overflow-y: auto;
    margin-right: -5px;
    padding-right: 5px;
}

.fg-total-stat-item {
    display: flex;
    justify-content: space-between;
    padding: 3px 0;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.fg-total-stat-item:last-child {
    border-bottom: none;
}

/* Bonuses display */
.fg-bonus-item {
    display: flex;
    justify-content: space-between;
    padding: 3px 0;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.fg-bonus-item:last-child {
    border-bottom: none;
}

.fg-no-bonuses {
    color: #777;
    text-align: center;
    padding: 10px 0;
}

/* Weapon controls */
.fg-weapon-controls {
    display: flex;
    justify-content: space-between;
    gap: 10px;
    margin-top: 20px;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .fg-weapon-header {
        flex-direction: column;
        align-items: center;
        text-align: center;
    }

    .fg-weapon-image {
        margin-right: 0;
        margin-bottom: 10px;
    }

    .fg-upgrade-section {
        padding: 10px;
    }

    .fg-epic-option-row,
    .fg-slot-row {
        flex-direction: column;
        gap: 5px;
    }

    .fg-epic-option-select,
    .fg-slot-option-select {
        width: 100%;
    }

    .fg-epic-option-value {
        align-self: center;
        margin-top: 5px;
    }

    .fg-slot-number {
        margin-right: 0;
        margin-bottom: 5px;
    }

    .fg-weapon-controls {
        flex-direction: column;
    }
}

