/**
 * Force Wing System
 * Handles force wing progression with level-based stats and skill slots
 *
 * Features:
 * - Wing level progression (1-400) with linear stat scaling
 * - 12 skill slots with selectable stats
 * - Individual stat levels for each slot
 */

// Define the system globally to ensure it's always available
window.ForceWingSystem = {
    // Track system state
    isInitialized: false,

    // Cache DOM elements
    elements: {},

    // Wing level data
    wingLevel: 1,
    maxWingLevel: 400,

    // Slot data - 12 slots total
    slots: Array(12).fill(null).map((_, index) => ({
        id: index,
        selectedStat: null,
        statLevel: 1,
        maxStatLevel: 5 // Default, can be overridden per stat
    })),

    // Selection window manager
    selectionWindow: null,

    // Current selected slot for option popup
    currentSlot: null,

    /**
     * Initialize the system - called by BuildPlanner core
     */
    init: function() {
        // Prevent multiple initializations
        if (this.isInitialized) {
            return;
        }

        // Get the system panel
        const panel = document.getElementById('fg-force-wing-system');
        if (!panel) {
            return;
        }

        // Cache DOM elements
        this.elements.panel = panel;

        // Initialize UI
        this.initUI();

        // Initialize selection window
        this.initSelectionWindow();

        // Load data from store if available
        this.loadFromStore();

        // Setup event listeners
        this.setupEventListeners();

        // Update stats
        this.updateStats();

        // Mark as initialized
        this.isInitialized = true;
    },

    /**
     * Initialize the user interface
     */
    initUI: function() {
        // Cache important elements
        this.elements.levelInput = this.elements.panel.querySelector('#fg-force-wing-level');
        this.elements.levelStats = this.elements.panel.querySelector('.fg-force-wing-level-stats');
        this.elements.skillTree = this.elements.panel.querySelector('.fg-force-wing-skill-tree');
        this.elements.uiImage = this.elements.panel.querySelector('.fg-force-wing-ui-image');
        this.elements.slotsContainer = this.elements.panel.querySelector('.fg-force-wing-slots-container');
        this.elements.summaryContent = this.elements.panel.querySelector('.fg-force-wing-summary-content');

        // Create slot elements
        this.createSlots();

        // Update level display
        this.updateLevelDisplay();
    },

    /**
     * Create the 12 skill slots positioned over the UI image
     */
    createSlots: function() {
        if (!this.elements.slotsContainer) return;

        // Clear existing slots
        this.elements.slotsContainer.innerHTML = '';

        // Manually mapped coordinates - organic V-formation from user selection
        const mappedCoordinates = [
            {x: 0.140992, y: 0.637473},
            {x: 0.138381, y: 0.781847},
            {x: 0.281984, y: 0.688429},
            {x: 0.279373, y: 0.824310},
            {x: 0.425587, y: 0.737261},
            {x: 0.420366, y: 0.873142},
            {x: 0.574413, y: 0.741507},
            {x: 0.574413, y: 0.877389},
            {x: 0.718016, y: 0.699045},
            {x: 0.720627, y: 0.834926},
            {x: 0.856397, y: 0.641720},
            {x: 0.859008, y: 0.781847}
        ];

        // Create 12 slots with mapped coordinates from user selection
        this.slots.forEach((slot, index) => {
            const slotElement = document.createElement('div');
            slotElement.className = 'fg-force-wing-slot';
            slotElement.setAttribute('data-slot-id', slot.id);
            slotElement.setAttribute('title', `Force Wing Slot ${slot.id + 1}`);

            // Position using normalized coordinates (0-1 range)
            const coords = mappedCoordinates[index];
            slotElement.style.left = `${coords.x * 100}%`;
            slotElement.style.top = `${coords.y * 100}%`;

            this.elements.slotsContainer.appendChild(slotElement);
        });

        // Cache slot elements
        this.elements.slotElements = this.elements.panel.querySelectorAll('.fg-force-wing-slot');
    },

    /**
     * Initialize selection window for slot stats
     */
    initSelectionWindow: function() {
        // Initialize the selection window manager
        this.selectionWindow = new SelectionWindowManager({
            id: 'fg-force-wing-stat-selector',
            title: 'Select Force Wing Stat',
            className: 'fg-force-wing-stat-selector',
            fixedPosition: true,
            onSelect: (data) => {
                if (this.currentSlot !== null) {
                    if (data.action === 'clear') {
                        this.selectSlotStat(this.currentSlot, null);
                    } else if (data.statId) {
                        this.selectSlotStat(this.currentSlot, data.statId);
                    }
                }
            },
            onClose: () => {
                this.currentSlot = null;
            }
        });
    },

    /**
     * Setup event listeners
     */
    setupEventListeners: function() {
        // Wing level input
        if (this.elements.levelInput) {
            this.elements.levelInput.addEventListener('input', (e) => {
                this.setWingLevel(parseInt(e.target.value) || 1);
            });
        }

        // Slot click handlers
        if (this.elements.slotElements) {
            this.elements.slotElements.forEach(slotElement => {
                slotElement.addEventListener('click', (e) => {
                    const slotId = parseInt(e.currentTarget.getAttribute('data-slot-id'));
                    this.selectSlot(slotId);
                });
            });
        }
    },

    /**
     * Set wing level and update stats
     */
    setWingLevel: function(level) {
        // Clamp level to valid range
        level = Math.max(1, Math.min(this.maxWingLevel, level));
        this.wingLevel = level;

        // Update UI
        this.updateLevelDisplay();

        // Update stats
        this.updateStats();

        // Save to store
        this.saveToStore();
    },

    /**
     * Update level display
     */
    updateLevelDisplay: function() {
        if (this.elements.levelInput) {
            this.elements.levelInput.value = this.wingLevel;
        }

        if (this.elements.levelStats) {
            this.elements.levelStats.textContent =
                `+${this.wingLevel} HP, +${this.wingLevel} All Attack, +${this.wingLevel} Defense`;
        }
    },

    /**
     * Select a slot for stat assignment
     */
    selectSlot: function(slotId) {
        this.currentSlot = slotId;
        this.showSlotStatSelection(slotId);
    },

    /**
     * Show stat selection window for a slot
     */
    showSlotStatSelection: function(slotId) {
        // Check if ForceWingData is available
        if (typeof ForceWingData === 'undefined') {
            console.error('ForceWingData not loaded');
            return;
        }

        // Get available stats for this slot
        const availableStats = ForceWingData.getSlotStats(slotId);
        if (!availableStats || availableStats.length === 0) {
            console.log(`No stats available for slot ${slotId} yet`);
            return;
        }

        // Create options for the selection window
        const options = [];

        availableStats.forEach(statConfig => {
            // Check if StatsConfig is available
            if (typeof StatsConfig === 'undefined') {
                console.warn('StatsConfig not available');
                return;
            }

            // Use getStatInfo method to properly handle PvP/PvE variants
            const statInfo = StatsConfig.getStatInfo(statConfig.statId);
            if (!statInfo) {
                console.warn(`Stat ${statConfig.statId} not found in StatsConfig`);
                return;
            }

            // Create stat icon HTML using StatsConfig method with error handling
            let iconHtml = '';
            if (typeof StatsConfig !== 'undefined') {
                try {
                    const iconUrl = StatsConfig.getStatIconUrl(statConfig.statId);
                    iconHtml = `<img src="${iconUrl}" alt="${statInfo.name}" class="fg-selection-option-icon" onerror="this.onerror=null; this.style.display='none';">`;
                } catch (e) {
                    console.warn(`Error getting icon for stat ${statConfig.statId}:`, e);
                }
            }

            // Get current level 1 value for display
            const level1Value = ForceWingData.getStatValue(slotId, statConfig.statId, 1);
            const suffix = statInfo.isPercentage ? '%' : '';

            options.push({
                html: `
                    <div class="fg-selection-option-with-icon">
                        ${iconHtml}
                        <div class="fg-selection-option-details">
                            <span class="fg-selection-option-name">${statInfo.name}</span>
                            <span class="fg-selection-option-value">+${level1Value}${suffix} (Level 1)</span>
                            <span class="fg-selection-option-max">Max Level: ${statConfig.maxLevel}</span>
                        </div>
                    </div>
                `,
                data: {
                    statId: statConfig.statId
                }
            });
        });

        // Add clear option if slot has a selection
        const currentSlot = this.slots[slotId];
        if (currentSlot && currentSlot.selectedStat) {
            options.push({
                html: `
                    <div class="fg-selection-option-clear">
                        <span class="fg-selection-option-name">Clear Selection</span>
                    </div>
                `,
                className: 'fg-selection-clear-option',
                data: {
                    action: 'clear'
                }
            });
        }

        // Show the selection window
        this.selectionWindow.show({
            title: `Select Stat for Force Wing Slot ${slotId + 1}`,
            options: options
        });
    },

    /**
     * Select a stat for a slot
     */
    selectSlotStat: function(slotId, statId) {
        if (!statId) {
            // Clear the slot
            this.clearSlot(slotId);
        } else {
            // Set the stat for the slot
            this.setSlotStat(slotId, statId, 1); // Start at level 1
        }

        // Hide the selection window
        this.selectionWindow.hide();
        this.currentSlot = null;
    },

    /**
     * Set a stat for a slot
     */
    setSlotStat: function(slotId, statId, level) {
        const slot = this.slots[slotId];
        if (!slot) return;

        // Validate the stat is available for this slot
        const availableStats = ForceWingData.getSlotStats(slotId);
        const statConfig = availableStats.find(stat => stat.statId === statId);
        if (!statConfig) {
            console.error(`Stat ${statId} not available for slot ${slotId}`);
            return;
        }

        // Clamp level to valid range
        level = Math.max(1, Math.min(statConfig.maxLevel, level));

        // Update slot data
        slot.selectedStat = statId;
        slot.statLevel = level;
        slot.maxStatLevel = statConfig.maxLevel;

        // Update UI
        this.updateSlotDisplay(slotId);

        // Update stats
        this.updateStats();

        // Save to store
        this.saveToStore();
    },

    /**
     * Clear a slot
     */
    clearSlot: function(slotId) {
        const slot = this.slots[slotId];
        if (!slot) return;

        slot.selectedStat = null;
        slot.statLevel = 1;
        slot.maxStatLevel = 5;

        // Update UI
        this.updateSlotDisplay(slotId);

        // Update stats
        this.updateStats();

        // Save to store
        this.saveToStore();
    },

    /**
     * Update slot display
     */
    updateSlotDisplay: function(slotId) {
        const slotElement = this.elements.panel.querySelector(`[data-slot-id="${slotId}"]`);
        if (!slotElement) return;

        const slot = this.slots[slotId];

        if (slot.selectedStat) {
            // Show filled slot
            slotElement.classList.add('filled');
            slotElement.classList.remove('empty');

            // Create stat display content
            this.createSlotContent(slotElement, slot);

            // Update tooltip with stat name and level
            const statInfo = typeof StatsConfig !== 'undefined' ? StatsConfig.getStatInfo(slot.selectedStat) : null;
            const statName = statInfo ? statInfo.name : slot.selectedStat;
            slotElement.title = `${statName} Level ${slot.statLevel}`;
        } else {
            // Show empty slot
            slotElement.classList.remove('filled');
            slotElement.classList.add('empty');
            slotElement.innerHTML = ''; // Clear any content
            slotElement.title = `Force Wing Slot ${slotId + 1}`;
        }
    },

    /**
     * Create content for a filled slot
     */
    createSlotContent: function(slotElement, slot) {
        // Get stat info
        const statInfo = typeof StatsConfig !== 'undefined' ? StatsConfig.getStatInfo(slot.selectedStat) : null;
        if (!statInfo) {
            slotElement.innerHTML = `<div class="fg-force-wing-slot-level">${slot.statLevel}</div>`;
            return;
        }

        // Create icon HTML
        let iconHtml = '';
        try {
            const iconUrl = StatsConfig.getStatIconUrl(slot.selectedStat);
            iconHtml = `<img src="${iconUrl}" alt="${statInfo.name}" class="fg-force-wing-slot-icon" onerror="this.style.display='none';">`;
        } catch (e) {
            // Fallback to just showing level
        }

        // Set slot content with icon container and level indicator (following stellar system pattern)
        slotElement.innerHTML = `
            <div class="fg-force-wing-slot-content">
                <div class="fg-force-wing-slot-icon-container">
                    ${iconHtml}
                    <div class="fg-force-wing-slot-level">${slot.statLevel}</div>
                </div>
            </div>
        `;
    },

    /**
     * Calculate total stats from wing level and slots
     */
    calculateStats: function() {
        const stats = {};

        // Wing level base stats
        stats.hp = this.wingLevel;
        stats.allAttackUp = this.wingLevel;
        stats.defense = this.wingLevel;

        // Add slot stats
        this.slots.forEach((slot, slotId) => {
            if (slot.selectedStat && typeof ForceWingData !== 'undefined') {
                const statValue = ForceWingData.getStatValue(slotId, slot.selectedStat, slot.statLevel);
                if (statValue > 0) {
                    // Add to existing stat or create new one
                    if (stats[slot.selectedStat]) {
                        stats[slot.selectedStat] += statValue;
                    } else {
                        stats[slot.selectedStat] = statValue;
                    }
                }
            }
        });

        return stats;
    },

    /**
     * Update stats in the main build planner
     */
    updateStats: function() {
        const stats = this.calculateStats();

        // Update stats summary display
        this.updateStatsSummary(stats);

        // Send to BuildPlanner
        if (typeof BuildPlanner !== 'undefined' && BuildPlanner.updateStats) {
            BuildPlanner.updateStats('force-wing', stats);
        }
    },

    /**
     * Update the stats summary display
     */
    updateStatsSummary: function(stats) {
        if (!this.elements.summaryContent) return;

        // Use StatIntegrationService for consistent stat display
        if (typeof StatIntegrationService !== 'undefined') {
            this.elements.summaryContent.innerHTML = StatIntegrationService.createStatSummaryHTML(stats);
        } else {
            // Simple fallback if service isn't available
            this.elements.summaryContent.innerHTML = '<p class="no-stats">Stats service not available.</p>';
        }
    },

    /**
     * Get essential data for saving
     */
    getEssentialData: function() {
        return {
            wingLevel: this.wingLevel,
            slots: this.slots.map(slot => ({
                id: slot.id,
                selectedStat: slot.selectedStat,
                statLevel: slot.statLevel
            }))
        };
    },

    /**
     * Save to store
     */
    saveToStore: function() {
        if (typeof BuildSaverStore !== 'undefined' && BuildSaverStore.saveData) {
            const essentialData = this.getEssentialData();
            BuildSaverStore.saveData('force-wing', essentialData);
            return true;
        }
        return false;
    },

    /**
     * Load data from store
     */
    loadFromStore: function() {
        if (typeof BuildSaverStore === 'undefined') {
            return false;
        }

        const storedData = BuildSaverStore.getSystemData('force-wing');
        if (!storedData) {
            return false;
        }

        return this.loadFromData(storedData);
    },

    /**
     * Load data from provided data object
     */
    loadFromData: function(data) {
        if (!data) {
            return false;
        }

        // Load wing level
        if (data.wingLevel) {
            this.wingLevel = data.wingLevel;
            this.updateLevelDisplay();
        }

        // Load slot data
        if (data.slots && Array.isArray(data.slots)) {
            data.slots.forEach(savedSlot => {
                const slot = this.slots.find(s => s.id === savedSlot.id);
                if (slot) {
                    slot.selectedStat = savedSlot.selectedStat;
                    slot.statLevel = savedSlot.statLevel || 1;
                    // Update the slot display
                    this.updateSlotDisplay(slot.id);
                }
            });
        }

        // Update stats
        this.updateStats();

        return true;
    }
};

// System will be initialized by BuildPlanner when needed
