/* Achievement System Styles */

:root {
  /* Base colors from pet system */
  --fg-dark-bg: #1c1e22;
  --fg-border: #3c3f44;
  --fg-darker-bg: rgba(0, 0, 0, 0.2);
  --fg-text: #d0d0d0;

  /* Accent colors */
  --fg-accent: #66bb6a;
  --fg-highlight: #4c8e50;
  --fg-header: #ff3333;
  --fg-hover-bg: rgba(27, 94, 32, 0.2);

  /* Achievement specific colors */
  --fg-progress-bg: #ffa500;
  --fg-progress-fill: linear-gradient(90deg, #ffa500, #ffb84d);
  --fg-category-active: #4a4a4a;
}

/* Base container */
.fg-achievement-system-container {
  padding: 20px;
  max-width: 100%;
  background-color: var(--fg-dark-bg);
  border-radius: 5px;
  border: 1px solid var(--fg-border);
  box-sizing: border-box;
  min-height: 600px;
  display: flex;
}

/* Layout structure */
.fg-achievement-layout {
  display: flex;
  width: 100%;
  height: 100%;
}

.fg-achievement-sidebar {
  width: 200px;
  background: rgba(28, 30, 34, 0.5);
  border-right: 1px solid var(--fg-border);
  padding: 15px 0;
}

.fg-achievement-main-content {
  flex: 1;
  padding: 15px;
  overflow-y: auto;
  max-height: 600px; /* Set max height for scrolling */
}

/* Category sidebar */
.fg-achievement-category-item {
  padding: 12px 20px;
  cursor: pointer;
  transition: background 0.3s;
  border-bottom: 1px solid rgba(60, 63, 68, 0.3);
  color: var(--fg-text);
  font-size: 0.9rem;
}

.fg-achievement-category-item:hover {
  background: var(--fg-hover-bg);
}

.fg-achievement-category-item.active {
  background: var(--fg-category-active);
  border-left: 3px solid var(--fg-accent);
  color: #fff;
}

.fg-achievement-category-count {
  float: right;
  color: #888;
  font-size: 0.8rem;
}

/* Achievement cards - more compact */
.fg-achievement-card {
  background: rgba(28, 30, 34, 0.5);
  border: 1px solid var(--fg-border);
  border-radius: 6px;
  margin-bottom: 10px;
  padding: 12px;
  transition: border-color 0.3s;
}

.fg-achievement-card:hover {
  border-color: var(--fg-accent);
}

.fg-achievement-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
  padding-bottom: 6px;
  border-bottom: 1px dashed rgba(255, 51, 51, 0.3);
}

.fg-achievement-name {
  font-size: 16px;
  font-weight: bold;
  color: var(--fg-header);
  text-shadow: 0 1px 1px rgba(0, 0, 0, 0.5);
}

.fg-achievement-type {
  background: var(--fg-darker-bg);
  border: 1px solid var(--fg-border);
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 11px;
  text-transform: uppercase;
  color: var(--fg-text);
}

/* Progress bars - more compact */
.fg-achievement-progress-container {
  margin-bottom: 8px;
}

.fg-achievement-progress-bar {
  width: 100%;
  height: 14px;
  background: var(--fg-darker-bg);
  border: 1px solid var(--fg-border);
  border-radius: 3px;
  overflow: hidden;
  position: relative;
}

.fg-achievement-progress-fill {
  height: 100%;
  background: var(--fg-progress-fill);
  transition: width 0.3s ease;
  position: relative;
}

.fg-achievement-progress-text {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-size: 11px;
  font-weight: bold;
  color: #000;
  z-index: 2;
  text-shadow: 0 1px 1px rgba(255, 255, 255, 0.3);
}

/* Milestone selector - more compact */
.fg-achievement-milestone-selector {
  margin-bottom: 8px;
}

.fg-achievement-milestone-selector label {
  display: block;
  margin-bottom: 3px;
  font-weight: bold;
  color: var(--fg-text);
  font-size: 0.85rem;
}

.fg-achievement-milestone-selector select {
  width: 100%;
  padding: 6px;
  background: var(--fg-darker-bg);
  color: var(--fg-text);
  border: 1px solid var(--fg-border);
  border-radius: 3px;
  font-size: 0.85rem;
}

.fg-achievement-milestone-selector select:focus {
  border-color: var(--fg-accent);
  outline: none;
}

/* Checkbox for single achievements */
.fg-achievement-checkbox-container {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}

.fg-achievement-checkbox-container input[type="checkbox"] {
  margin-right: 10px;
  transform: scale(1.2);
  accent-color: var(--fg-accent);
}

.fg-achievement-checkbox-container label {
  color: var(--fg-text);
  font-size: 0.9rem;
}

/* Stats display */
.fg-achievement-stats-display {
  background: var(--fg-darker-bg);
  padding: 15px;
  border-radius: 4px;
  border: 1px solid var(--fg-border);
}

.fg-achievement-stats-title {
  font-weight: bold;
  margin-bottom: 10px;
  color: #e0e0e0;
  font-size: 0.9rem;
  border-bottom: 1px solid var(--fg-border);
  padding-bottom: 5px;
}

.fg-achievement-stat-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 5px;
  padding: 4px 0;
}

.fg-achievement-stat-name {
  color: var(--fg-text);
  font-size: 0.85rem;
}

.fg-achievement-stat-value {
  color: var(--fg-accent);
  font-weight: bold;
  font-size: 0.85rem;
}

/* Empty state */
.fg-achievement-empty-state {
  text-align: center;
  color: #888;
  padding: 40px;
  font-style: italic;
}

/* Category panels */
.fg-achievement-category-panel {
  display: none;
}

.fg-achievement-category-panel.active {
  display: block;
}

/* Stats summary container */
.fg-achievement-stats-summary {
  background: var(--fg-darker-bg);
  border: 1px solid var(--fg-border);
  border-radius: 5px;
  padding: 20px;
  margin-top: 20px;
}

.fg-achievement-stats-summary .fg-achievement-stats-title {
  color: #f8d64e;
  font-weight: bold;
  margin-bottom: 15px;
  text-align: center;
  border-bottom: 1px solid var(--fg-border);
  padding-bottom: 8px;
}

.fg-achievement-summary-content {
  /* StatIntegrationService will handle the styling */
}
