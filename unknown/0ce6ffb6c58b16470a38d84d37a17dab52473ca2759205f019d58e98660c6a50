/**
 * Stats Configuration
 * Central configuration for stats shared across all systems
 */

const StatsConfig = {
    // Base path for stat icons (relative to plugin)
    iconBasePath: 'assets/images/stat icons/',

    // Base stats that all characters have by default
    baseStats: {
        critRate: 5,        // Base critical rate is 5%
        maxCritRate: 50,
        critDamage: 20     // Base max critical rate is 50%
    },

    // Comprehensive stat definitions
    stats: {
        //======================================
        // OFFENSIVE STATS
        //======================================

        // Base attack stats
        attack: {
            name: "Attack",
            icon: "attack_icon.png",
            category: "offensive",
            isPercentage: false,
            description: "Increases physical damage",
            variants: ["pvp", "pve"]
        },

        magicAttack: {
            name: "Magic Attack",
            icon: "mattack_icon.png",
            category: "offensive",
            isPercentage: false,
            description: "Increases magic damage",
            variants: ["pvp", "pve"]
        },

        attackRate: {
            name: "Attack Rate",
            icon: "attack_rate_icon.png",
            category: "offensive",
            isPercentage: false,
            description: "Increases attack speed",
            variants: ["pvp", "pve"]
        },

        critRate: {
            name: "Critical Rate",
            icon: "critical_rate_icon.png",
            category: "offensive",
            isPercentage: true,
            description: "Increases chance of landing critical hits",
            variants: ["pvp", "pve"]
        },

        maxCritRate: {
            name: "Max Crit. Rate",
            icon: "max_crit_rate_icon.png",
            category: "offensive",
            isPercentage: true,
            description: "Maximum critical rate cap",
            variants: ["pvp", "pve"]
        },

        critDamage: {
            name: "Critical DMG",
            icon: "critical_damage_icon.png",
            category: "offensive",
            isPercentage: true,
            description: "Increases critical hit damage",
            variants: ["pvp", "pve"]
        },

        swordSkillAmp: {
            name: "Sword Skill Amp.",
            icon: "sword_amp_icon.png",
            category: "offensive",
            isPercentage: true,
            description: "Increases sword skill damage",
            variants: ["pvp", "pve"]
        },

        magicSkillAmp: {
            name: "Magic Skill Amp.",
            icon: "magic_amp_icon.png",
            category: "offensive",
            isPercentage: true,
            description: "Increases magic skill damage",
            variants: ["pvp", "pve"]
        },

        accuracy: {
            name: "Accuracy",
            icon: "accuracy_icon.png",
            category: "offensive",
            isPercentage: false,
            description: "Reduces chance of attacks being evaded",
            variants: ["pvp", "pve"]
        },

        penetration: {
            name: "Penetration",
            icon: "penetration_icon.png",
            category: "offensive",
            isPercentage: false,
            description: "Bypasses a portion of enemy defense",
            variants: ["pvp", "pve"]
        },

        addDamage: {
            name: "Add. Damage",
            icon: "add_dmg_icon.png",
            category: "offensive",
            isPercentage: false,
            description: "Flat damage added to attacks",
            variants: ["pvp", "pve"]
        },

        minDamage: {
            name: "Min Damage",
            icon: "min_dmg_icon.png",
            category: "offensive",
            isPercentage: true,
            description: "Increases minimum damage",
            variants: ["pvp", "pve"]
        },

        ignoreEvasion: {
            name: "Ignore Evasion",
            icon: "ignore_evasion_icon.png",
            category: "offensive",
            isPercentage: false,
            description: "Reduces chance of being evaded",
            variants: ["pvp", "pve"]
        },

        finalDamageIncreased: {
            name: "Final DMG Increased",
            icon: "final_dmg_increased_icon.png",
            category: "offensive",
            isPercentage: true,
            description: "Increases final damage",
            variants: ["pvp", "pve"]
        },
        ignoreDamageReduce: {
            name: "Ignore DMG Reduction",
            icon: "ignore_damage_reduction_icon.png",
            category: "offensive",
            isPercentage: false,
            description: "Bypasses enemy damage reduction",
            variants: ["pvp", "pve"]
        },
        // Ignore resist offensive stats
        ignoreResistCritRate: {
            name: "Ignore Resist Critical Rate",
            icon: "ignore_resist_crit_rate.png",
            category: "offensive",
            isPercentage: true,
            description: "Bypasses enemy critical rate resistance",
            variants: ["pvp", "pve"]
        },
        ignoreResistCritDmg: {
            name: "Ignore Resist Critical DMG",
            icon: "ignore_resist_crit_dmg_icon.png",
            category: "offensive",
            isPercentage: true,
            description: "Bypasses enemy critical damage resistance",
            variants: ["pvp", "pve"]
        },
        cancelIgnoreDamageReduce: {
            name: "Cancel Ignore Damage Reduction",
            icon: "cancel_ignore_damage_reduction_icon.png",
            category: "offensive",
            isPercentage: false,
            description: "Reduces enemy damage reduction",
            variants: ["pvp", "pve"]
        },

        ignoreResistSkillAmp: {
            name: "Ignore Resist Skill Amp",
            icon: "ignore_resist_skill_amp_icon.png",
            category: "offensive",
            isPercentage: true,
            description: "Bypasses enemy skill amplification resistance",
            variants: ["pvp", "pve"]
        },
        normalDamageUp: {
            name: "Normal DMG Up",
            icon: "normal_dmg_icon.png",
            category: "offensive",
            isPercentage: true,
            description: "Increases normal attack damage",
            variants: ["pvp", "pve"]
        },
        cancelIgnorePenetration: {
            name: "Cancel Ignore Penetration",
            icon: "cancel_ignore_penetration_icon.png",
            category: "offensive",
            isPercentage: false,
            description: "Reduces enemy penetration",
            variants: ["pvp", "pve"]
        },
        //======================================
        // DEFENSIVE STATS
        //======================================

        hp: {
            name: "HP",
            icon: "hp_icon.png",
            category: "defensive",
            isPercentage: false,
            description: "Total health points"
        },

        defense: {
            name: "Defense",
            icon: "defense_icon.png",
            category: "defensive",
            isPercentage: false,
            description: "Reduces damage taken",
            variants: ["pvp", "pve"]
        },

        defenseRate: {
            name: "Defense Rate",
            icon: "defense_rate_icon.png",
            category: "defensive",
            isPercentage: false,
            description: "Increases damage reduction",
            variants: ["pvp", "pve"]
        },

        evasion: {
            name: "Evasion",
            icon: "evasion_icon.png",
            category: "defensive",
            isPercentage: false,
            description: "Chance to evade attacks",
            variants: ["pvp", "pve"]
        },

        resistCritRate: {
            name: "Resist Critical Rate",
            icon: "resist_critical_rate_icon.png",
            category: "defensive",
            isPercentage: true,
            description: "Reduces chance of receiving critical hits"
        },

        resistCritDmg: {
            name: "Resist Critical DMG",
            icon: "resist_crit_dmg_icon.png",
            category: "defensive",
            isPercentage: true,
            description: "Reduces critical damage taken"
        },

        resistSkillAmp: {
            name: "Resist Skill Amp",
            icon: "resist_skill_amp_icon.png",
            category: "defensive",
            isPercentage: true,
            description: "Reduces skill damage taken",
            variants: ["pvp", "pve"]
        },

        ignorePenetration: {
            name: "Ignore Penetration",
            icon: "ignore_penetration_icon.png",
            category: "defensive",
            isPercentage: false,
            description: "Reduces enemy penetration",
            variants: ["pvp", "pve"]
        },

        ignoreAccuracy: {
            name: "Ignore Accuracy",
            icon: "ignore_accuracy_icon.png",
            category: "defensive",
            isPercentage: false,
            description: "Increases chance to evade despite enemy accuracy"
        },

        damageReduce: {
            name: "DMG Reduction",
            icon: "dmg_reduction_icon.png",
            category: "defensive",
            isPercentage: false,
            description: "Reduces all damage taken",
            variants: ["pvp", "pve"]
        },

        resistSuppression: {
            name: "Resist Suppression",
            icon: "resist_suppression_icon.png",
            category: "defensive",
            isPercentage: true,
            description: "Reduces chance of being suppressed"
        },

        resistSilence: {
            name: "Resist Silence",
            icon: "resist_silence_icon.png",
            category: "defensive",
            isPercentage: true,
            description: "Reduces chance of being silenced"
        },

        //======================================
        // UTILITY STATS
        //======================================

        mp: {
            name: "MP",
            icon: "mana_icon.png",
            category: "utility",
            isPercentage: false,
            description: "Total mana points"
        },

        hpAbsorb: {
            name: "HP Absorb",
            icon: "hp_absorb_icon.png",
            category: "utility",
            isPercentage: true,
            description: "Amount of HP absorbed per hit"
        },

        maxHpSteal: {
            name: "HP Absorb Up",
            icon: "max_hp_absorb_icon.png",
            category: "utility",
            isPercentage: true,
            description: "Increases HP absorption"
        },

        mpAbsorb: {
            name: "MP Absorb",
            icon: "mp_absorb_icon.png",
            category: "utility",
            isPercentage: true,
            description: "Amount of MP absorbed per hit"
        },

        maxMpSteal: {
            name: "MP Absorb Up",
            icon: "max_mp_absorb_icon.png",
            category: "utility",
            isPercentage: true,
            description: "Increases MP absorption"
        },

        hpAutoHeal: {
            name: "HP Auto Heal",
            icon: "hp_auto_heal_icon.png",
            category: "utility",
            isPercentage: false,
            description: "HP regenerated over time"
        },

        mpAutoHeal: {
            name: "MP Auto Heal",
            icon: "mp_auto_heal_icon.png",
            category: "utility",
            isPercentage: false,
            description: "MP regenerated over time"
        },

        exp: {
            name: "EXP",
            icon: "exp_icon.png",
            category: "utility",
            isPercentage: true,
            description: "Increases experience gain"
        },

        skillExp: {
            name: "Skill EXP",
            icon: "skill_exp_icon.png",
            category: "utility",
            isPercentage: true,
            description: "Increases skill experience gain"
        },

        partyExp: {
            name: "Party EXP",
            icon: "party_exp_icon.png",
            category: "utility",
            isPercentage: true,
            description: "Increases party experience gain"
        },

        petExp: {
            name: "Pet EXP",
            icon: "skill_exp_icon.png",
            category: "utility",
            isPercentage: true,
            description: "Increases pet experience gain"
        },

        alzDropAmount: {
            name: "Alz Drop Amount",
            icon: "alz_drop_amount_icon.png",
            category: "utility",
            isPercentage: true,
            description: "Increases Alz drops"
        },

        alzDropRate: {
            name: "Alz Drop Rate",
            icon: "alz_drop_rate_icon.png",
            category: "utility",
            isPercentage: true,
            description: "Increases chance of Alz drops"
        },

        alzBombChance: {
            name: "Alz Bomb Chance",
            icon: "alz_bomb_chance_icon.png",
            category: "utility",
            isPercentage: true,
            description: "Increases chance of getting big Alz drops"
        },

        '2SlotDropRate': {
            name: "2-slot Drop Rate",
            icon: "2_slot_item_drop_icon.png",
            category: "utility",
            isPercentage: true,
            description: "Increases 2-slot item drop rate"
        },

        resistUnableToMove: {
            name: "Resist Unable to Move",
            icon: "resist_unable_move_icon.png",
            category: "utility",
            isPercentage: true,
            description: "Reduces chance of movement-impairing effects"
        },

        resistDown: {
            name: "Resist Down",
            icon: "resist_down_icon.png",
            category: "utility",
            isPercentage: true,
            description: "Reduces chance of knockdown"
        },

        resistKnockback: {
            name: "Resist Knockback",
            icon: "resist_knock_back_icon.png",
            category: "utility",
            isPercentage: true,
            description: "Reduces chance of being knocked back"
        },

        resistStun: {
            name: "Resist Stun",
            icon: "stun_resist_icon.png",
            category: "utility",
            isPercentage: true,
            description: "Reduces chance of being stunned"
        },

        ignoreResistKnockback: {
            name: "Ignore Resist Knockback",
            icon: "ignore_resist_knochback_icon.png",
            category: "utility",
            isPercentage: true,
            description: "Bypasses enemy knockback resistance"
        },

        ignoreResistDown: {
            name: "Ignore Resist Down",
            icon: "ignore_resist_down_icon.png",
            category: "utility",
            isPercentage: true,
            description: "Bypasses enemy knockdown resistance"
        },

        ignoreResistStun: {
            name: "Ignore Resist Stun",
            icon: "ignore_resist_stun_icon.png",
            category: "utility",
            isPercentage: true,
            description: "Bypasses enemy stun resistance"
        },

        auraDurationIncrease: {
            name: "Aura Duration Increase",
            icon: "aura_mode_increase_icon.png",
            category: "utility",
            isPercentage: false,
            description: "Extends aura skill duration"
        },

        // Default icon for stats without specific icon
        default: {
            name: "Unknown Stat",
            icon: "default_stat_icon.png",
            category: "utility",
            isPercentage: false,
            description: "Unknown stat type"
        },

        //SPECIAL STATS; DERIVED STATS; THESE STATS SIMPLY INCREASE OTHER STATS

        // all attack up increases magic attack, attack
        allAttackUp: {
            name: "All Attack Up",
            icon: "all_atk_icon.png",
            category: "offensive",
            isPercentage: false,
            description: "Increases all attack types",
            variants: ["pvp", "pve"]
        },

        // ALL SKILL AMP INCREASES MAGIC AMP; SWORD AMP
        allSkillAmp: {
            name: "All Skill Amp.",
            icon: "all_amp_icon.png",
            category: "offensive",
            isPercentage: true,
            description: "Increases all skill damage",
            variants: ["pvp", "pve"]
        },

        // STR INCREASES ATTACK, DMG REDUCTION, RESIST DOWN, IGNORE PENETRATION
        str: {
            name: "STR",
            icon: "str_icon.png",
            category: "offensive",
            isPercentage: false,
            description: "Strength stat"
        },

        // int increases magic attack, resist critical rate, resist critical damage, resist skill, reist knockback, resist stun
        int: {
            name: "INT",
            icon: "int_icon.png",
            category: "offensive",
            isPercentage: false,
            description: "Intelligence stat"
        },

        //dex increases attack rate defense rate and evasion, resist unmovable, resist down
        dex: {
            name: "DEX",
            icon: "dex_icon.png",
            category: "offensive",
            isPercentage: false,
            description: "Dexterity stat"
        }
    },

    // Get base stats that all characters have by default
    getBaseStats: function() {
        return { ...this.baseStats }; // Return a copy to prevent modification
    },

    // Initialize the config
    init: function() {
        // Try to get iconBasePath from WordPress data
        if (typeof forceguidesPlannerData !== 'undefined') {
            if (forceguidesPlannerData.pluginUrl) {
                this.pluginUrl = forceguidesPlannerData.pluginUrl;
            }
        }
    },

    // Get plugin URL
    getPluginUrl: function() {
        return this.pluginUrl || '';
    },

    // Updated method to get icon URL for a specific stat that supports variants
    getStatIconUrl: function(statId) {
        const stat = this.getStatInfo(statId);
        if (!stat) {
            return this.getPluginUrl() + this.iconBasePath + this.stats.default.icon;
        }
        return this.getPluginUrl() + this.iconBasePath + stat.icon;
    },

    // Updated helper to format stat values
    formatStatValue: function(statId, value) {
        const stat = this.getStatInfo(statId);
        if (!stat) return value;

        return stat.isPercentage ? value + '%' : value;
    },

    // Get all stats for a category
    getStatsByCategory: function(category) {
        const result = [];

        // First add all base stats in this category
        for (const statId in this.stats) {
            if (this.stats[statId].category === category) {
                result.push(statId);

                // Then add any variants
                if (this.stats[statId].variants) {
                    this.stats[statId].variants.forEach(variant => {
                        result.push(variant + statId.charAt(0).toUpperCase() + statId.slice(1));
                    });
                }
            }
        }

        return result;
    },

    // Updated method to get stat information that supports variants
    getStatInfo: function(statId) {
        // Check if this is a variant (like "pvpAttack")
        if (statId.startsWith('pvp') || statId.startsWith('pve')) {
            const prefix = statId.substring(0, 3);
            const baseStatId = statId.substring(3, 4).toLowerCase() + statId.substring(4);

            if (this.stats[baseStatId] &&
                this.stats[baseStatId].variants &&
                this.stats[baseStatId].variants.includes(prefix)) {

                // Return a new object with variant-specific properties
                return {
                    name: `${prefix.toUpperCase()} ${this.stats[baseStatId].name}`,
                    icon: `${prefix}_${this.stats[baseStatId].icon}`,
                    category: this.stats[baseStatId].category,
                    isPercentage: this.stats[baseStatId].isPercentage,
                    description: `${this.stats[baseStatId].description} against ${prefix === 'pvp' ? 'players' : 'monsters'}`
                };
            }
        }

        // Return base stat or default
        return this.stats[statId] || this.stats.default;
    }
};

// Initialize StatsConfig when document is ready
document.addEventListener('DOMContentLoaded', function() {
    StatsConfig.init();
});