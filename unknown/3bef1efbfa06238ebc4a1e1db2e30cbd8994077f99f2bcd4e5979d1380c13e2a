/**
 * Build Sharer
 * Provides functionality to share builds via URL
 */

// Initialize the build sharer namespace
window.BuildSharer = {
    // Initialize the build sharer
    init: function() {
        // Create and append the share button to the UI
        this.createShareButton();

        // Setup event listeners
        this.setupEventListeners();

        // Check for shared builds in URL on page load
        this.checkForSharedBuild();
    },

    // Create the share button UI
    createShareButton: function() {
        // Create the share build button
        const shareButton = document.createElement('div');
        shareButton.className = 'fg-system-button fg-share-build-button';
        shareButton.id = 'fg-share-build-btn';
        shareButton.innerHTML = `
            <span class="icon share-icon">
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="24" height="24" fill="white">
                    <path d="M18 16.08c-.76 0-1.44.3-1.96.77L8.91 12.7c.05-.23.09-.46.09-.7s-.04-.47-.09-.7l7.05-4.11c.54.5 1.25.81 2.04.81 1.66 0 3-1.34 3-3s-1.34-3-3-3-3 1.34-3 3c0 .24.04.47.09.7L8.04 9.81C7.5 9.31 6.79 9 6 9c-1.66 0-3 1.34-3 3s1.34 3 3 3c.79 0 1.5-.31 2.04-.81l7.12 4.16c-.05.21-.08.43-.08.65 0 1.61 1.31 2.92 2.92 2.92 1.61 0 2.92-1.31 2.92-2.92s-1.31-2.92-2.92-2.92z"/>
                </svg>
            </span>
            <span>Share Build</span>
        `;

        // Add CSS styles for the share button
        const style = document.createElement('style');
        style.textContent = `
            .fg-share-build-button {
                background-color: #4CAF50 !important;
                margin-top: 10px !important;
            }

            .fg-share-build-button:hover {
                background-color: #45a049 !important;
            }

            .fg-share-dialog {
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0, 0, 0, 0.7);
                display: flex;
                align-items: center;
                justify-content: center;
                z-index: 9999;
            }

            .fg-share-dialog-content {
                background: #2a2a36;
                padding: 20px;
                border-radius: 8px;
                max-width: 500px;
                width: 90%;
            }

            .fg-share-url-container {
                display: flex;
                margin: 15px 0;
            }

            .fg-share-url {
                flex: 1;
                padding: 8px;
                border: 1px solid #444;
                background: #1a1a24;
                color: #fff;
                border-radius: 4px 0 0 4px;
            }

            .fg-copy-button {
                padding: 8px 15px;
                background: #ff5500;
                color: white;
                border: none;
                border-radius: 0 4px 4px 0;
                cursor: pointer;
            }

            .fg-close-button {
                padding: 8px 15px;
                background: #444;
                color: white;
                border: none;
                border-radius: 4px;
                cursor: pointer;
                float: right;
            }


        `;
        document.head.appendChild(style);

        // Append to the system buttons container
        const buttonsContainer = document.querySelector('.fg-system-buttons');
        if (buttonsContainer) {
            buttonsContainer.appendChild(shareButton);
        }
    },

    // Set up event listeners
    setupEventListeners: function() {
        // Share button click handler
        const shareButton = document.getElementById('fg-share-build-btn');
        if (shareButton) {
            shareButton.addEventListener('click', () => {
                // Make sure at least one system is initialized before sharing
                this.ensureSystemsInitialized();
                this.createShareableLink();
            });
        }
    },

    // Ensure at least one system is initialized
    ensureSystemsInitialized: function() {
        const systemsToCheck = ['HonorMedalSystem', 'StellarSystem', 'PetSystem', 'CostumesSystem', 'EssenceRunesSystem', 'AchievementSystem'];

        // Check if any system is already initialized
        const anyInitialized = systemsToCheck.some(systemName => {
            const system = window[systemName];
            return system && system.isInitialized;
        });

        if (!anyInitialized) {
            // Try to initialize the first visible system tab
            const systemTabs = document.querySelectorAll('.fg-system-button:not(.fg-share-build-button)');
            if (systemTabs.length > 0) {
                // Simulate a click on the first system tab
                systemTabs[0].click();
            }
        }
    },

    // Create a shareable link for the current build
    createShareableLink: function() {
        try {
            // Capture only the specified systems' data
            const buildData = this.captureSpecificSystems();

            // Debug: Log the captured data
            console.log('Captured build data:', buildData);

            // Check if we have any systems data
            if (!buildData.systems || Object.keys(buildData.systems).length === 0) {
                this.showNotification('No system data found. Make sure you have configured at least one system.', true);
                return null;
            }

            // Encode the build data
            const encodedData = this.encodeBuildData(buildData);
            if (!encodedData) {
                throw new Error('Failed to encode build data');
            }

            // Create the URL - use the current page's full URL as the base
            let baseUrl;
            let shareUrl;

            // Check if we have WordPress data available with the build planner page URL
            if (window.forceguidesPlannerData && forceguidesPlannerData.buildPlannerPageUrl) {
                baseUrl = forceguidesPlannerData.buildPlannerPageUrl;
                // Make sure we don't add a query string to an existing query string
                if (baseUrl.includes('?')) {
                    shareUrl = baseUrl + '&build=' + encodeURIComponent(encodedData);
                } else {
                    shareUrl = baseUrl + '?build=' + encodeURIComponent(encodedData);
                }
            } else {
                // Fallback to current URL
                baseUrl = window.location.href.split('?')[0]; // Remove any existing query parameters
                shareUrl = baseUrl + '?build=' + encodeURIComponent(encodedData);
            }

            // Show a dialog with the link
            this.showShareDialog(shareUrl);

            return shareUrl;
        } catch (error) {
            this.showNotification('Failed to create shareable link', true);
            return null;
        }
    },

    // Capture data from specific systems only
    captureSpecificSystems: function() {
        const buildData = {
            timestamp: Date.now(),
            version: '1.0',
            systems: {}
        };

        // Define which systems to include
        const systemsToInclude = ['HonorMedalSystem', 'StellarSystem', 'PetSystem', 'CostumesSystem', 'EssenceRunesSystem'];

        // Try to get essential data from each system
        systemsToInclude.forEach(systemName => {
            // Get the system from the window
            const system = window[systemName];

            // Check if system exists and is initialized
            if (!system || !system.isInitialized) {
                return;
            }

            // Get system ID from name (convert PetSystem -> pet, EssenceRunesSystem -> essence-runes)
            const systemId = systemName.replace('System', '')
                .replace(/([a-z])([A-Z])/g, '$1-$2')
                .toLowerCase();

            if (typeof system.getEssentialData === 'function') {
                try {
                    // Force a save to ensure we have the latest data
                    if (typeof system.saveToStore === 'function') {
                        system.saveToStore();
                    }

                    // Get the essential data
                    const data = system.getEssentialData();
                    buildData.systems[systemId] = data;
                } catch (error) {
                    console.error(`Error getting data from ${systemName}:`, error);
                }
            }
        });

        return buildData;
    },

    // Encode build data to a compact string
    encodeBuildData: function(buildData) {
        try {
            // Check if we have the custom compression available
            if (window.BuildCompression && typeof BuildCompression.encodeBuildData === 'function') {
                // Use custom compression for Honor System
                const customEncoded = BuildCompression.encodeBuildData(buildData);
                if (customEncoded) {
                    return customEncoded;
                }
            }

            // Fallback to standard compression
            const jsonData = JSON.stringify(buildData);
            return LZString.compressToBase64(jsonData);
        } catch (error) {
            console.error('Error encoding build data:', error);
            return null;
        }
    },

    // Decode build data from a string
    decodeBuildData: function(encodedData) {
        try {
            // First, ensure the data is properly decoded from URL encoding if needed
            try {
                // Try to decode if it's URL encoded
                if (encodedData.indexOf('%') !== -1) {
                    encodedData = decodeURIComponent(encodedData);
                }
            } catch (e) {
                // If decoding fails, use the original string
            }

            // Check if it's our custom format (starts with 'h')
            if (window.BuildCompression && typeof BuildCompression.decodeBuildData === 'function' &&
                (encodedData.startsWith('h') || encodedData.length < 100)) {
                // Try custom decompression first
                const customDecoded = BuildCompression.decodeBuildData(encodedData);
                if (customDecoded) {
                    return customDecoded;
                }
            }

            // Fallback to standard decompression
            const jsonData = LZString.decompressFromBase64(encodedData);

            if (!jsonData) {
                throw new Error('Decompression failed');
            }

            return JSON.parse(jsonData);
        } catch (error) {
            console.error('Error decoding build data:', error);
            throw error;
        }
    },

    // Show a dialog with the shareable link
    showShareDialog: function(shareUrl) {
        // Create dialog element
        const dialog = document.createElement('div');
        dialog.className = 'fg-share-dialog';
        dialog.innerHTML = `
            <div class="fg-share-dialog-content">
                <h3>Share This Build</h3>
                <p>Copy this link to share your build with others:</p>
                <div class="fg-share-url-container">
                    <input type="text" value="${shareUrl}" readonly class="fg-share-url" />
                    <button class="fg-copy-button">Copy</button>
                </div>
                <button class="fg-close-button">Close</button>
            </div>
        `;

        // Add to DOM
        document.body.appendChild(dialog);

        // Set up event listeners
        const copyButton = dialog.querySelector('.fg-copy-button');
        const closeButton = dialog.querySelector('.fg-close-button');
        const urlInput = dialog.querySelector('.fg-share-url');

        copyButton.addEventListener('click', () => {
            urlInput.select();
            document.execCommand('copy');
            copyButton.textContent = 'Copied!';
            setTimeout(() => {
                copyButton.textContent = 'Copy';
            }, 2000);
        });

        closeButton.addEventListener('click', () => {
            dialog.remove();
        });
    },

    // Check for shared builds in URL on page load
    checkForSharedBuild: function() {
        const urlParams = new URLSearchParams(window.location.search);
        const buildParam = urlParams.get('build');

        if (buildParam) {
            try {
                // Decode the build data
                const buildData = this.decodeBuildData(buildParam);

                // Make sure at least one system is initialized before loading
                this.ensureSystemsInitialized();

                // Wait a short time for systems to initialize
                setTimeout(() => {
                    // Import the build
                    this.loadSharedBuild(buildData);
                }, 500);

                return true;
            } catch (error) {
                this.showNotification('Failed to load shared build', true);
            }
        }

        return false;
    },

    // Load a shared build
    loadSharedBuild: function(buildData) {
        // Validate build data
        if (!buildData || !buildData.systems) {
            this.showNotification('Invalid build data format', true);
            return false;
        }

        // Debug: Log the build data we're loading
        console.log('Loading build data:', buildData);

        // Track which systems were loaded
        const loadedSystems = [];

        // Load data into each system
        for (const systemId in buildData.systems) {
            const systemData = buildData.systems[systemId];

            // Convert system ID to system name (pet -> PetSystem, essence-runes -> EssenceRunesSystem)
            const systemName = systemId
                .replace(/-([a-z])/g, (match, letter) => letter.toUpperCase())
                .replace(/^([a-z])/, (match, letter) => letter.toUpperCase()) + 'System';

            // Get the system from the window
            const system = window[systemName];

            // Check if system exists, is initialized, and has a loadFromData method
            if (system && system.isInitialized && typeof system.loadFromData === 'function') {
                try {
                    const result = system.loadFromData(systemData);
                    if (result) {
                        loadedSystems.push(systemId);
                    }
                } catch (error) {
                    console.error(`Error loading data into ${systemName}:`, error);
                }
            }
        }

        // Update stats
        if (BuildPlanner && typeof BuildPlanner.calculateTotalStats === 'function') {
            BuildPlanner.calculateTotalStats();
        }

        // Show success notification with details
        if (loadedSystems.length > 0) {
            this.showNotification(`Build loaded successfully: ${loadedSystems.join(', ')}`);
            return true;
        } else {
            this.showNotification('No systems were loaded. Try clicking on a system tab first.', true);
            return false;
        }
    },

    // Show a notification message
    showNotification: function(message, isError = false) {
        // Remove any existing notifications
        const existingNotification = document.querySelector('.fg-share-notification');
        if (existingNotification) {
            existingNotification.remove();
        }

        // Create notification element
        const notification = document.createElement('div');
        notification.className = 'fg-share-notification';
        notification.innerHTML = message;

        if (isError) {
            notification.style.backgroundColor = '#8B0000';
        }

        // Add styles for the notification
        notification.style.position = 'fixed';
        notification.style.top = '20px';
        notification.style.right = '20px';
        notification.style.backgroundColor = isError ? '#8B0000' : '#2a2a36';
        notification.style.color = '#fff';
        notification.style.padding = '12px 16px';
        notification.style.borderRadius = '4px';
        notification.style.boxShadow = '0 4px 12px rgba(0,0,0,0.2)';
        notification.style.zIndex = '9999';
        notification.style.transform = 'translateY(-100px)';
        notification.style.opacity = '0';
        notification.style.transition = 'all 0.3s ease';

        // Add to DOM
        document.body.appendChild(notification);

        // Show notification with animation
        setTimeout(() => {
            notification.style.transform = 'translateY(0)';
            notification.style.opacity = '1';
        }, 10);

        // Remove after delay
        setTimeout(() => {
            notification.style.transform = 'translateY(-100px)';
            notification.style.opacity = '0';
            setTimeout(() => {
                notification.remove();
            }, 300);
        }, 3000);
    }
};

// Initialize when document is ready
document.addEventListener('DOMContentLoaded', () => {
    // Only initialize if we're on the build planner page
    if (document.querySelector('.fg-build-planner-container')) {
        // Initialize the build sharer
        BuildSharer.init();
    }
});
