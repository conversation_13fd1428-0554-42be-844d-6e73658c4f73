/**
 * Selection Window Manager Styles
 * Provides styling for the reusable selection window component
 * used across different systems in the Force Guides Build Planner.
 */

/* Base window styles */
.fg-selection-window {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999;
    opacity: 0;
    visibility: hidden;
    transition: opacity 0.3s ease, visibility 0.3s ease;
    pointer-events: none;
}

.fg-selection-window.active {
    opacity: 1;
    visibility: visible;
    pointer-events: auto;
}

/* Window content */
.fg-selection-window-content {
    background: #1c1e22;
    border-radius: 8px;
    width: 90%;
    max-width: 600px;
    max-height: 80vh;
    overflow: hidden;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.5);
    display: flex;
    flex-direction: column;
    border: 1px solid #3c3f44;
    position: relative;
}

/* Fixed position window (centered) */
.fg-selection-window-fixed .fg-selection-window-content {
    position: relative;
    transform: none;
}

/* Relative position window (positioned relative to target) */
.fg-selection-window-relative .fg-selection-window-content {
    position: absolute;
    transform: translate(-50%, -50%);
}

/* Header */
.fg-selection-window-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 20px;
    border-bottom: 1px solid #3c3f44;
    background: linear-gradient(to bottom, #2a2d31, #1c1e22);
}

.fg-selection-window-title {
    margin: 0;
    padding: 0;
    font-size: 1.2rem;
    color: #e0e0e0;
    font-weight: 600;
}

.fg-selection-window-close {
    background: none;
    border: none;
    color: #aaa;
    font-size: 1.5rem;
    cursor: pointer;
    padding: 0;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: background-color 0.2s, color 0.2s;
}

.fg-selection-window-close:hover {
    background-color: rgba(255, 255, 255, 0.1);
    color: #fff;
}

/* Body */
.fg-selection-window-body {
    padding: 20px;
    overflow-y: auto;
    max-height: calc(80vh - 60px); /* Subtract header height */
}

/* Options container */
.fg-selection-window-options {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
    gap: 15px;
}

/* Option item */
.fg-selection-option {
    background-color: #2a2d31;
    border: 1px solid #3c3f44;
    border-radius: 6px;
    padding: 12px;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.fg-selection-option:hover {
    background-color: #33363a;
    border-color: #4a4d52;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.fg-selection-option.disabled {
    opacity: 0.5;
    cursor: not-allowed;
    pointer-events: none;
}

/* Option with icon */
.fg-selection-option-with-icon {
    display: flex;
    align-items: center;
    gap: 10px;
}

.fg-selection-option-icon {
    width: 24px;
    height: 24px;
    object-fit: contain;
}

/* Color preview for Stellar system */
.fg-selection-option-color-preview {
    width: 20px;
    height: 20px;
    border-radius: 50%;
    margin-left: auto;
}

/* Level indicator for Stellar system */
.fg-selection-option-level {
    display: inline-block;
    min-width: 20px;
    text-align: center;
    font-weight: 600;
    color: #aaa;
    margin-right: 5px;
}

.fg-selection-option-name {
    font-weight: 500;
    color: #d0d0d0;
    font-size: 0.9rem;
}

.fg-selection-option-value {
    color: #66bb6a;
    font-weight: 600;
    font-size: 0.9rem;
}

/* Search input */
.fg-selection-window-search {
    margin-bottom: 15px;
    position: relative;
}

.fg-selection-window-search input {
    width: 100%;
    padding: 10px 15px;
    border-radius: 6px;
    border: 1px solid #3c3f44;
    background-color: #252830;
    color: #e0e0e0;
    font-size: 0.9rem;
}

.fg-selection-window-search input:focus {
    outline: none;
    border-color: #4a90e2;
    box-shadow: 0 0 0 2px rgba(74, 144, 226, 0.2);
}

/* Category headers */
.fg-selection-category-header {
    font-size: 1rem;
    font-weight: 600;
    color: #e0e0e0;
    margin: 15px 0 10px;
    padding-bottom: 5px;
    border-bottom: 1px solid #3c3f44;
}

/* Empty state */
.fg-selection-empty-state {
    text-align: center;
    padding: 30px;
    color: #aaa;
    font-style: italic;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .fg-selection-window-content {
        width: 95%;
        max-height: 90vh;
    }

    .fg-selection-window-options {
        grid-template-columns: repeat(auto-fill, minmax(140px, 1fr));
        gap: 10px;
    }

    .fg-selection-window-header {
        padding: 12px 15px;
    }

    .fg-selection-window-body {
        padding: 15px;
    }
}
