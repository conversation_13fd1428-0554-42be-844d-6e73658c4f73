/**
 * Essence Runes System Styles
 * UI styles for the essence runes system - mimics the game's rune system
 */

.essence-runes-container {
    width: 100%;
    max-width: 320px;
    margin: 0 auto;
    padding: 15px;
    background-color: #1c1e22;
    border-radius: 5px;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.7);
    color: #f0f0f0;
    border: 1px solid rgba(60, 63, 68, 0.8);
}

.essence-runes-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    padding-bottom: 10px;
    border-bottom: 1px solid #3c3f44;
}

.essence-runes-header h2 {
    margin: 0;
    padding: 0;
    color: #e0e0e0;
    font-size: 1.3rem;
    text-align: center;
}

.essence-runes-info {
    position: absolute;
    right: 10px;
    color: #aaa;
    font-size: 0.8rem;
}

/* Slots scroll area */
.essence-runes-slots-scroll {
    flex: 1;
    overflow-y: auto;
    padding-right: 4px;
    margin-bottom: 10px;
    border-bottom: 1px solid #333;
}

/* Slots layout - ONE PER ROW like in the game image */
.essence-runes-slots-container {
    display: flex;
    flex-direction: column;
    gap: 3px;
}

/* Individual rune slot styles - matching game appearance */
.essence-rune-slot {
    position: relative;
    width: 100%;
    height: 46px;
    border-radius: 2px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: flex-start;
    transition: all 0.2s ease;
    background-color: rgba(10, 10, 15, 0.8);
    border: 1px solid #3c3f44;
    overflow: hidden;
    padding: 0;
}

.essence-rune-slot:hover {
    background-color: #272a2e;
    border-color: #4f5a68;
}

.essence-rune-slot.empty {
    background: linear-gradient(135deg, #1a1a1a 0%, #222222 100%);
    border: 1px solid #333333;
}

.essence-rune-slot.filled {
    background-color: #272a2e;
    border: 1px solid #444444;
}

.essence-rune-slot-level {
    position: absolute;
    left: 3px;
    top: 3px;
    font-size: 0.75em;
    color: #888888;
    z-index: 5;
}

/* Rune icon container */
.essence-rune-icon {
    width: 32px;
    height: 32px;
    margin-left: 0;
    margin-right: 10px;
    border-radius: 2px;
    background-color: rgba(0, 0, 0, 0.4);
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
}

.essence-rune-icon img {
    width: 100%;
    height: 100%;
    object-fit: contain;
}

.essence-rune-icon-inner {
    width: 28px;
    height: 28px;
    border-radius: 2px;
    background-size: cover;
}

/* Rune content styles */
.essence-rune-content {
    width: 100%;
    display: flex;
    align-items: center;
    margin-left: 0;
    padding-left: 16px;
}

.essence-rune-name {
    font-size: 0.85rem;
    font-weight: 500;
    line-height: 1.2;
    flex-grow: 1;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    color: #d0d0d0;
}

.essence-rune-level {
    font-size: 0.85rem;
    color: #ffffff;
    margin-left: 5px;
    margin-right: 10px;
    white-space: nowrap;
}

/* Level up button in the row */
.essence-rune-level-up {
    width: 22px;
    height: 22px;
    border-radius: 2px;
    border: 1px solid #444444;
    background-color: rgba(15, 15, 15, 0.3);
    color: #66bb6a;
    font-size: 14px;
    line-height: 1;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
    margin-right: 8px;
}

.essence-rune-level-up:hover {
    background-color: rgba(40, 40, 40, 0.5);
    color: #a5d6a7;
}

/* Level control buttons in details */
.essence-rune-level-controls {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    margin-top: 10px;
}

.essence-rune-level-controls button {
    width: 30px;
    height: 30px;
    border-radius: 4px;
    border: 1px solid #3c3f44;
    background-color: #272a2e;
    color: #e0e0e0;
    font-size: 16px;
    line-height: 1;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
}

.essence-rune-level-controls button:hover {
    background-color: #4f5a68;
}

.essence-rune-level-controls span {
    font-size: 0.8rem;
    color: #ccc;
}

/* Rune selector modal - styled like pet system */
.essence-rune-selector {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999;
}

.essence-rune-selector-content {
    background: #1c1e22;
    border-radius: 5px;
    width: 90%;
    max-width: 650px;
    max-height: 80vh;
    overflow: hidden;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.5);
    display: flex;
    flex-direction: column;
    border: 1px solid #3c3f44;
}

.essence-rune-selector-header {
    padding: 10px 15px;
    border-bottom: 1px solid #3c3f44;
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: #272a2e;
}

.essence-rune-selector-header h3 {
    margin: 0;
    color: #e0e0e0;
    font-size: 1rem;
}

.essence-rune-selector-list {
    padding: 15px;
    overflow-y: auto;
    max-height: calc(80vh - 100px);
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 8px;
}

.essence-rune-selector-item {
    background: rgba(0, 0, 0, 0.15);
    border-radius: 3px;
    padding: 8px 10px;
    cursor: pointer;
    transition: background-color 0.2s;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border: 1px solid #3c3f44;
}

.essence-rune-selector-item:hover {
    background: rgba(40, 40, 40, 0.2);
    border-color: #5a5a5a;
}

.essence-rune-selector-item.disabled {
    opacity: 0.5;
    cursor: not-allowed;
    background-color: rgba(0, 0, 0, 0.4);
}

.essence-rune-selector-item span {
    display: flex;
    align-items: center;
    font-weight: 500;
    color: #d0d0d0;
    font-size: 0.85rem;
}

.essence-rune-selector-item img {
    width: 20px;
    height: 20px;
    margin-right: 8px;
    vertical-align: middle;
    border-radius: 3px;
    background-color: rgba(0, 0, 0, 0.15);
    padding: 1px;
    border: 1px solid #444444;
}

.essence-rune-selector-item .select-button {
    color: #66bb6a;
    font-weight: 600;
    font-size: 0.85rem;
    background-color: rgba(15, 15, 15, 0.3);
    border: 1px solid #444444;
    padding: 3px 10px;
    border-radius: 3px;
    transition: all 0.2s ease;
}

.essence-rune-selector-item:hover .select-button {
    background-color: rgba(40, 40, 40, 0.5);
    color: #a5d6a7;
}

.essence-rune-selector-footer {
    padding: 10px 15px;
    border-top: 1px solid #3c3f44;
    display: flex;
    justify-content: center;
    background: #272a2e;
}

.essence-rune-selector-close {
    background: rgba(0, 0, 0, 0.2);
    border: 1px solid #3c3f44;
    color: #e0e0e0;
    border-radius: 4px;
    cursor: pointer;
    padding: 8px 20px;
    font-size: 0.9rem;
    transition: background-color 0.2s;
}

.essence-rune-selector-close:hover {
    background-color: #4f5a68;
}

/* Add row button */
.essence-runes-add-row {
    text-align: center;
    padding: 8px 0;
    margin-top: 10px;
    background-color: rgba(0, 0, 0, 0.15);
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.2s ease;
    border: 1px solid #3c3f44;
}

.essence-runes-add-row:hover {
    background-color: rgba(40, 40, 40, 0.2);
    border-color: #555555;
}

.essence-runes-add-row-icon {
    font-size: 18px;
    font-weight: bold;
    color: #66bb6a;
}

/* Slots counter */
.essence-runes-slots-counter {
    text-align: center;
    color: #aaa;
    margin: 5px 0 10px;
    font-size: 0.75rem;
}

/* Details section */
.essence-runes-details,
.essence-runes-stats,
.essence-runes-resources {
    width: 100%;
    box-sizing: border-box;
    margin-bottom: 15px;
    background-color: rgba(0, 0, 0, 0.15);
    border-radius: 5px;
    padding: 15px;
    border: 1px solid #3c3f44;
}

.essence-runes-details h3,
.essence-runes-stats h3,
.essence-runes-resources h3 {
    color: #e0e0e0;
    margin-top: 0;
    margin-bottom: 12px;
    border-bottom: 1px solid #3c3f44;
    padding-bottom: 8px;
    font-size: 1rem;
    text-align: center;
}

.essence-runes-resources h3 {
    color: #f8d64e;
}

.essence-rune-details-empty {
    color: #707070;
    font-style: italic;
    padding: 8px 0;
    text-align: center;
    font-size: 0.9rem;
}

.essence-rune-description {
    color: #d0d0d0;
    font-size: 0.75rem;
    margin-top: 5px;
    line-height: 1.4;
}

/* Stat items styling */
.essence-rune-stat-item {
    display: flex;
    justify-content: space-between;
    padding: 6px 10px;
    background: rgba(0, 0, 0, 0.2);
    border-radius: 3px;
    margin-bottom: 5px;
    border: 1px solid #333333;
}

.essence-rune-stat-item:hover {
    background: rgba(0, 0, 0, 0.3);
}

.essence-rune-stat-name {
    color: #d0d0d0;
    font-size: 0.85rem;
    font-weight: 600;
    max-width: 70%;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    display: flex;
    align-items: center;
}

.essence-rune-stat-icon {
    width: 20px;
    height: 20px;
    margin-right: 8px;
    border-radius: 2px;
    background-color: rgba(0, 0, 0, 0.4);
    padding: 2px;
    border: 1px solid #444444;
}

.essence-rune-stat-value {
    color: #66bb6a;
    font-weight: 600;
    font-size: 0.85rem;
}

/* Resource section styles */
.essence-runes-resources h4 {
    font-size: 0.85rem;
    margin-top: 15px;
    margin-bottom: 10px;
    color: #f0f0f0;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    padding-bottom: 6px;
    text-align: left;
}

.essence-rune-resource-item {
    display: flex;
    justify-content: space-between;
    padding: 6px 0;
    border-bottom: 1px dotted #3c3f44;
}

.essence-rune-resource-name {
    color: #d0d0d0;
    font-size: 0.85rem;
}

.essence-rune-resource-value {
    color: #a9e34b;
    font-weight: bold;
    font-size: 0.85rem;
}

/* Remove button */
.essence-rune-remove {
    color: #ff6b6b;
    font-size: 20px;
    font-weight: bold;
    cursor: pointer;
    margin-right: 4px;
    width: 22px;
    height: 22px;
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 1;
    transition: all 0.2s ease;
    background-color: rgba(244, 67, 54, 0.3);
    border-radius: 2px;
    border: 1px solid rgba(244, 67, 54, 0.5);
}

.essence-rune-remove:hover {
    background-color: rgba(244, 67, 54, 0.5);
}

/* Two-column layout for main container */
.essence-runes-main-layout {
    display: flex;
    gap: 15px;
    align-items: stretch;
    height: auto;
    justify-content: center;
}

.essence-runes-left-panel,
.essence-runes-right-panel {
    flex: 1 1 320px;
    display: flex;
    flex-direction: column;
    max-width: 320px;
}

.essence-runes-left-panel .essence-runes-container,
.essence-runes-right-panel .essence-runes-container {
    height: 100%;
    min-height: 750px;
    max-height: 750px;
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

/* Make slots container scrollable but with a fixed height */
.essence-runes-slots-scroll {
    flex: 1;
    overflow-y: auto;
    padding-right: 4px;
    margin-bottom: 10px;
    border-bottom: 1px solid #333;
}

/* Make right panel container scrollable */
.essence-runes-right-panel .essence-runes-container {
    overflow-y: auto;
    padding-right: 10px;
}

/* Adjust heights for right panel sections */
.essence-runes-details,
.essence-runes-stats,
.essence-runes-resources {
    width: 100%;
    box-sizing: border-box;
    margin-bottom: 15px;
}

/* Fix content areas - remove separate scrollbars */
.essence-runes-details-content,
.essence-runes-stats-content,
.essence-runes-resources-content {
    width: 100%;
    box-sizing: border-box;
    max-height: none;
    overflow-y: visible;
}

/* Media queries for responsiveness */
@media screen and (max-width: 768px) {
    .essence-runes-container {
        padding: 10px;
    }
}

@media screen and (max-width: 480px) {
    .essence-runes-slots-scroll {
        height: 300px;
    }
}

/* Location information styles */
.essence-rune-location {
    margin: 10px 0;
    font-size: 0.85rem;
    line-height: 1.4;
    color: #b0b0b0;
    padding: 5px;
    background-color: rgba(0, 0, 0, 0.2);
    border-radius: 3px;
}

.essence-rune-location strong {
    color: #e0e0e0;
}

/* Rune details styling */
.essence-rune-details-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 10px;
    background-color: rgba(0, 0, 0, 0.2);
    padding: 8px;
    border-radius: 3px;
}

.essence-rune-details-name {
    display: flex;
    align-items: center;
    font-weight: 600;
    font-size: 0.9rem;
}

.essence-rune-details-value {
    font-weight: 600;
    font-size: 1rem;
}

/* Empty slot styling - center text and darken it */
.essence-rune-slot.empty .essence-rune-content {
    justify-content: center;
    color: #909090;
    font-size: 0.9rem;
}

/* Old button styling removed - now using centralized quick-fill-buttons.css */

/* Mobile responsive adjustments */
@media screen and (max-width: 480px) {
    .essence-runes-header {
        flex-direction: column;
        gap: 8px;
    }

    /* Quick fill buttons are now handled by centralized CSS */
}