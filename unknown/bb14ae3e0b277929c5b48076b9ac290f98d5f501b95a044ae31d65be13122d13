/**
 * Essence Runes System
 * Handles essence rune progression and stats
 */

// Ensure EssenceRuneData is loaded
(function() {
    // Check if EssenceRuneData is already loaded
    if (typeof window.EssenceRuneData !== 'undefined') {
        return;
    }

    // Function to load a script dynamically
    function loadScript(url) {
        return new Promise((resolve, reject) => {
            const script = document.createElement('script');
            script.src = url;
            script.onload = resolve;
            script.onerror = reject;
            document.head.appendChild(script);
        });
    }

    // Determine the base URL based on the current script
    const scripts = document.getElementsByTagName('script');
    const currentScript = scripts[scripts.length - 1];
    const currentPath = currentScript.src;

    // Use WordPress plugin URL from global variable instead of attempting to calculate
    const basePath = window.forceguidesPlannerData ? forceguidesPlannerData.pluginUrl : '';

    // Load the essence-rune-data.js file
    loadScript(basePath + 'js/essence-rune-system/essence-rune-data.js');
})();

// Define the system globally to ensure it's always available
window.EssenceRunesSystem = {
    // Track system state
    isInitialized: false,

    // Flag to indicate data has been loaded from storage
    dataLoaded: false,

    // Cache DOM elements
    elements: {},

    // Selection window manager
    selectionWindow: null,

    // System specific data
    data: {
        // Total number of slots
        totalSlots: 16,

        // Maximum slots possible
        maxSlots: 60,

        // Track selected runes
        selectedRunes: Array(60).fill(null),

        // Current slot being edited
        currentSlot: null,

        // Define runes with their variants for better organization
        // Use the runeDefinitions from the separate data file
        get runeDefinitions() {
            // Get rune definitions from the data file
            if (window.EssenceRuneData && window.EssenceRuneData.runeDefinitions) {
                return window.EssenceRuneData.runeDefinitions;
            }

            // Return a minimal object with default structure if data file is not loaded
            return {
                hp: {
                    baseStatType: 'hp',
                    iconId: 'hp',
                    variants: [
                        {
                            id: 'hp',
                            name: 'HP I (Default)',
                            tier: 1,
                            maxLevel: 20,
                            valuePerLevel: [50, 100, 150, 200, 250, 300, 350, 400, 450, 500],
                            apCost: [3, 4, 5, 7, 9, 12, 15, 19, 23, 28],
                            location: "Default location",
                            materials: []
                        }
                    ]
                }
            };
        },

        // Example runes for the UI demonstration (initial setup)
        exampleRunes: [
            { slot: 0, type: 'maxCritRate', value: 3 },
            { slot: 1, type: 'critDmg', value: 5 },
            { slot: 2, type: 'ignoreResistCritDmg', value: 5 },
            { slot: 3, type: 'cancelIgnorePenetration', value: 9 },
            { slot: 4, type: 'ignoreResistSkillAmp', value: 5 },
            { slot: 5, type: 'ignoreEvasion', value: 10 },
            { slot: 6, type: 'normalDmgUp', value: 10 },
            { slot: 7, type: 'penetration', value: 10 },
            { slot: 8, type: 'penetration2', value: 25 },
            { slot: 9, type: 'ignorePenetration2', value: 45 },
            { slot: 10, type: 'dmgReduction2', value: 36 },
            { slot: 11, type: 'int2', value: 12 },
            { slot: 12, type: 'str2', value: 12 },
            { slot: 13, type: 'defenseRate2', value: 150 }
        ]
    },

    /**
     * Initialize the system - this is called by the BuildPlanner core
     * when this system becomes active
     */
    init: function() {
        // Prevent multiple initializations
        if (this.isInitialized) {
            return;
        }

        // Check if data file is loaded
        if (!window.EssenceRuneData || !window.EssenceRuneData.runeDefinitions) {
            setTimeout(() => this.init(), 500);
            return;
        }

        // Get the system panel
        const panel = document.getElementById('fg-essence-runes-system');
        if (!panel) {
            return;
        }

        // Cache DOM elements for better performance
        this.elements.panel = panel;

        // Initialize UI
        this.initUI();

        // Setup event listeners
        this.setupEventListeners();

        // Wait for BuildPlanner to be fully loaded before updating stats
        this.waitForBuildPlanner(10);

        // Mark as initialized
        this.isInitialized = true;
    },

    /**
     * Wait for BuildPlanner to be fully loaded before updating stats
     * Will retry up to maxAttempts times, with increased delay between attempts
     */
    waitForBuildPlanner: function(maxAttempts) {
        const self = this;
        let attempts = 0;

        function attemptToRegister() {
            attempts++;

            // Check if both BuildPlanner and EssenceRuneData are available
            if (typeof BuildPlanner !== 'undefined' && BuildPlanner.updateStats &&
                typeof window.EssenceRuneData !== 'undefined') {
                // BuildPlanner and data are available, update stats
                self.updateStats();
                return true;
            } else if (attempts < maxAttempts) {
                // Either BuildPlanner or EssenceRuneData not ready, try again after a delay
                setTimeout(attemptToRegister, attempts * 200);
                return false;
            } else {
                // Maximum attempts reached
                return false;
            }
        }

        // Start the attempt process
        attemptToRegister();
    },

    /**
     * Refresh the entire system UI and data - useful after loading
     */
    refreshSystem: function() {
        // First ensure we have the proper data structure
        this.ensureDataStructure();

        // Refresh all UI elements
        this.refreshUI();

        // Hide rune selector if visible
        if (this.elements.runeSelector) {
            this.elements.runeSelector.style.display = 'none';
        }

        // Update stats in the main planner
        this.updateStats();
    },

    /**
     * Ensure the proper data structure exists
     */
    ensureDataStructure: function() {
        // Initialize data structure if needed
        if (!this.data) {
            this.data = {};
        }

        // Initialize selectedRunes array if it doesn't exist
        if (!this.data.selectedRunes || !Array.isArray(this.data.selectedRunes)) {
            this.data.selectedRunes = [];
        }

        // Ensure the array has enough slots
        if (this.data.selectedRunes.length < this.data.totalSlots) {
            const missingSlots = this.data.totalSlots - this.data.selectedRunes.length;
            const nullSlots = Array(missingSlots).fill(null);
            this.data.selectedRunes = this.data.selectedRunes.concat(nullSlots);
        }

        // Ensure proper settings for each rune
        for (let i = 0; i < this.data.selectedRunes.length; i++) {
            const rune = this.data.selectedRunes[i];
            if (rune) {
                // Make sure required properties exist
                if (!rune.type) rune.type = 'unknown';
                if (!rune.id) rune.id = 'unknown';
                if (!rune.level) rune.level = 1;
                if (!rune.maxLevel) {
                    // Find max level from runeDefinitions
                    let maxLevelFound = false;

                    // Check if this rune is in our runeDefinitions structure
                    for (const [baseStatType, definition] of Object.entries(this.data.runeDefinitions)) {
                        const variant = definition.variants.find(v => v.id === rune.type);
                        if (variant) {
                            rune.maxLevel = variant.maxLevel || 10;
                            maxLevelFound = true;
                            break;
                        }
                    }

                    // Default max level if not found in runeDefinitions
                    if (!maxLevelFound) {
                        rune.maxLevel = 10;
                    }
                }
            }
        }
    },

    /**
     * Force store the current state
     */
    ensureAndSave: function() {
        // Ensure data structure is valid
        this.ensureDataStructure();

        // Save data to the central store
        if (typeof BuildSaverStore !== 'undefined' && BuildSaverStore.saveData) {
            const essentialData = this.getEssentialData();
            BuildSaverStore.saveData('essence-runes', essentialData);
            return true;
        }

        return false;
    },

    /**
     * Get the essential data for saving
     * Returns just the minimum data needed to restore the system state
     */
    getEssentialData: function() {
        // Ensure data structure is valid before providing it
        this.ensureDataStructure();

        // Create a clean copy of selected runes without any circular references
        const cleanSelectedRunes = this.data.selectedRunes.map(rune => {
            if (!rune) return null;
            return {
                id: rune.id || null,
                type: rune.type || null,
                level: rune.level || 1,
                maxLevel: rune.maxLevel || 10
            };
        });

        // Return only the essential data needed to restore state
        return {
            selectedRunes: cleanSelectedRunes,
            totalSlots: this.data.totalSlots || 16
        };
    },

    /**
     * Force a save of the current data to the store
     */
    saveToStore: function() {
        // Use the central BuildSaverStore to save data
        if (typeof BuildSaverStore !== 'undefined' && BuildSaverStore.saveData) {
            const essentialData = this.getEssentialData();
            BuildSaverStore.saveData('essence-runes', essentialData);
            return true;
        }

        return false;
    },

    /**
     * Initialize the UI for the system
     */
    initUI: function() {
        // Create the main UI structure
        const html = `
            <div class="essence-runes-main-layout">
                <div class="essence-runes-left-panel">
                    <div class="essence-runes-container">
                        <div class="essence-runes-header">
                            <h2>Essence Runes System</h2>
                        </div>

                        <div class="essence-runes-slots-scroll">
                            <div class="essence-runes-slots-container">
                                <!-- Slots will be generated here -->
                            </div>
                        </div>

                        <div class="essence-runes-slots-counter">
                            Essence Slot (In Use/Total): <span class="essence-runes-slots-count">0/${this.data.totalSlots}</span>
                        </div>

                        <div class="essence-runes-add-row">
                            <div class="essence-runes-add-row-icon">+</div>
                            <div>Add Slot</div>
                        </div>
                    </div>
                </div>

                <div class="essence-runes-right-panel">
                    <div class="essence-runes-container">
                        <div class="essence-runes-details">
                            <h3>Rune Details</h3>
                            <div class="essence-runes-details-content">
                                <div class="essence-rune-details-empty">Select a rune to view details</div>
                            </div>
                        </div>

                        <div class="essence-runes-stats">
                            <h3>Stats Summary</h3>
                            <div class="essence-runes-stats-content">
                                <div class="essence-rune-details-empty">No runes equipped yet</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;

        // Add the HTML to the panel
        this.elements.panel.innerHTML = html;

        // Cache more DOM elements
        this.elements.slotsContainer = this.elements.panel.querySelector('.essence-runes-slots-container');
        this.elements.slotsCount = this.elements.panel.querySelector('.essence-runes-slots-count');
        this.elements.addRowButton = this.elements.panel.querySelector('.essence-runes-add-row');
        this.elements.detailsContent = this.elements.panel.querySelector('.essence-runes-details-content');
        this.elements.statsContent = this.elements.panel.querySelector('.essence-runes-stats-content');

        // Initialize the selection window manager
        this.selectionWindow = new SelectionWindowManager({
            id: 'fg-essence-rune-selector',
            title: 'Select Essence Rune',
            className: 'fg-essence-rune-selector',
            fixedPosition: true,
            onSelect: (data) => {
                if (data.type) {
                    this.selectRune(data.type);
                }
            },
            onClose: () => {
                this.data.currentSlot = null;
            }
        });

        // Initialize Quick Fill Button Manager
        if (typeof QuickFillButtonConfigs !== 'undefined') {
            this.quickFillManager = QuickFillButtonConfigs.initializeSystem('essence-runes', this.elements.panel.querySelector('.essence-runes-container'));
        }

        // Initialize the selectedRunes array if it doesn't exist
        if (!this.data.selectedRunes || !Array.isArray(this.data.selectedRunes)) {
            this.data.selectedRunes = Array(this.data.maxSlots).fill(null);
        }

        // Check if there is saved data in the central store
        this.loadFromStore();

        // Generate slots based on the totalSlots
        this.generateSlots();

        // Update the stats right away
        this.updateStats();

        // Update UI elements
        this.updateSlotsCounter();
        this.updateStatsDisplay();
    },

    /**
     * Load data from the central BuildSaverStore
     */
    loadFromStore: function() {
        // Check if BuildSaverStore exists and has data
        if (window.BuildSaverStore && BuildSaverStore.dataLoaded) {
            // Get data for this system with consistent ID format
            const systemData = BuildSaverStore.getSystemData('essence-runes');

            if (systemData && systemData.selectedRunes) {
                // Use the common loadFromData method
                return this.loadFromData(systemData);
            }
        }

        return false;
    },

    /**
     * Load data directly from provided data object
     * Used by both loadFromStore and the build sharing feature
     */
    loadFromData: function(data) {
        if (data && data.selectedRunes) {
            // Use the saved data
            this.data.selectedRunes = JSON.parse(JSON.stringify(data.selectedRunes));

            // Restore totalSlots if it was saved
            if (data.totalSlots && typeof data.totalSlots === 'number') {
                this.data.totalSlots = data.totalSlots;
            }

            // Trigger an update to the stats to ensure DPS calculator gets updated
            setTimeout(() => {
                this.updateStats();
                // Tell BuildPlanner to update the stats summary UI if available
                if (typeof BuildPlanner !== 'undefined' && BuildPlanner.updateStatsSummaryUI) {
                    BuildPlanner.updateStatsSummaryUI(BuildPlanner.totalStats);
                }
            }, 300);

            return true;
        }

        return false;
    },

    /**
     * Find a rune definition by its ID
     * @param {string} runeId - The ID of the rune to find
     * @returns {Object} An object containing {variant, definition, baseStatType, iconId} or null if not found
     */
    findRuneDefinition: function(runeId) {
        if (!runeId) return null;

        for (const [baseStatType, definition] of Object.entries(this.data.runeDefinitions)) {
            const variant = definition.variants.find(v => v.id === runeId);
            if (variant) {
                return {
                    variant,
                    definition,
                    baseStatType,
                    iconId: definition.iconId || baseStatType
                };
            }
        }

        return null;
    },

    /**
     * Refresh all UI elements
     */
    refreshUI: function() {
        this.refreshRuneSlots();
        this.updateSlotsCounter();
        this.updateStatsDisplay();
    },

    /**
     * Get HTML for a rune's content
     */
    getRuneContentHTML: function(rune, slotIndex) {
        if (!rune) {
            return '<div class="essence-rune-content">Click to add rune</div>';
        }

        // Find rune definition using the helper method
        const runeDefInfo = this.findRuneDefinition(rune.type);

        // If rune is not found, return basic content
        if (!runeDefInfo) {
            return '<div class="essence-rune-content">Unknown rune</div>';
        }

        const { variant: runeVariant, definition: baseDefinition, iconId, baseStatType } = runeDefInfo;

        // Set up rune type info
        const runeType = {
            id: runeVariant.id,
            name: runeVariant.name,
            color: runeVariant.color || '#4caf50',
            isPercent: StatsConfig.getStatInfo(baseDefinition.baseStatType || iconId).isPercentage
        };

        // Format value based on rune progression data
        let valueText = '';

        // Check if we have progression data for this rune via runeDefinitions
        if (runeVariant && runeVariant.valuePerLevel) {
            const levelIndex = Math.min(rune.level - 1, runeVariant.valuePerLevel.length - 1);
            const value = runeVariant.valuePerLevel[levelIndex];
            // Use StatsConfig for consistent stat formatting
            valueText = StatsConfig.formatStatValue(baseStatType, value);
        } else {
            // For runes without specific progression data
            valueText = `Lv. ${rune.level}`;
        }

        // Determine if + button should be disabled (at max level)
        const isMaxLevel = rune.level >= runeVariant.maxLevel;
        const levelUpButtonClass = isMaxLevel ? 'essence-rune-level-up disabled' : 'essence-rune-level-up';

        // Get icon for the rune - use StatsConfig if available
        let iconHtml = '';
        if (typeof StatsConfig !== 'undefined') {
            try {
                // Use the base stat icon ID if available, otherwise use the rune type
                const iconUrl = StatsConfig.getStatIconUrl(iconId || rune.type);
                iconHtml = `
                    <div class="essence-rune-icon">
                        <img src="${iconUrl}" alt="${runeType.name}" onerror="handleRuneIconError(this, '${runeType.color}')">
                    </div>
                `;
            } catch (e) {
                // Fallback to color block if icon loading fails
                iconHtml = `
                    <div class="essence-rune-icon">
                        <div class="essence-rune-icon-inner" style="background-color: ${runeType.color}"></div>
                    </div>
                `;
            }
        } else {
            // Fallback if StatsConfig is not available
            iconHtml = `
                <div class="essence-rune-icon">
                    <div class="essence-rune-icon-inner" style="background-color: ${runeType.color}"></div>
                </div>
            `;
        }

        // Format the name and level for essence runes display - improved to match game UI
        return `
            <div class="essence-rune-content">
                ${iconHtml}
                <div class="essence-rune-name" style="color: ${runeType.color}">
                    ${runeType.name}
                </div>
                <div class="essence-rune-level">Lv. ${rune.level}</div>
                <div class="${levelUpButtonClass}" data-slot="${slotIndex}" title="Level up rune">+</div>
                <div class="essence-rune-remove" data-slot="${slotIndex}" title="Remove rune">×</div>
            </div>
        `;
    },

    /**
     * Update the stats display in the UI
     */
    updateStatsDisplay: function() {
        // Generate the stats HTML
        const statsHtml = this.generateStatsHTML();

        // Update the stats content area
        if (this.elements.statsContent) {
            this.elements.statsContent.innerHTML = statsHtml;
        }
    },

    /**
     * Handle removing a rune from a slot
     */
    removeRune: function(slotIndex) {
        // Check if valid slot
        if (slotIndex < 0 || slotIndex >= this.data.maxSlots) return;

        // Clear the slot
        this.data.selectedRunes[slotIndex] = null;

        // Reset current slot if it was this one
        if (this.data.currentSlot === slotIndex) {
            this.data.currentSlot = null;
            // Show empty details
            this.showEmptyDetails();
        }

        // Update UI and stats
        this.refreshUI();

        // Explicitly update stats in the build planner
        this.updateStats();

        // Save the changes
        this.saveToStore();
    },

    /**
     * Setup event listeners for the system
     */
    setupEventListeners: function() {
        const self = this;

        // Define global handler for rune icon errors
        if (typeof window.handleRuneIconError !== 'function') {
            window.handleRuneIconError = function(img, color) {
                img.onerror = null;
                img.style.display = 'none';
                img.parentNode.innerHTML = '<div class="essence-rune-icon-inner" style="background-color: ' + color + '"></div>';
            };
        }

        // Event delegation for slots container (for dynamic elements)
        this.elements.slotsContainer.addEventListener('click', function(e) {
            // Check if clicked on a slot
            const slotElement = e.target.closest('.essence-rune-slot');
            if (slotElement) {
                const slotIndex = parseInt(slotElement.dataset.slot, 10);

                // Check if the remove button was clicked
                if (e.target.classList.contains('essence-rune-remove')) {
                    self.removeRune(slotIndex);
                    return;
                }

                // Check if the level up button was clicked
                if (e.target.classList.contains('essence-rune-level-up') && !e.target.classList.contains('disabled')) {
                    self.levelUpRune(slotIndex);
                    return;
                }

                // Otherwise, show the rune selector or details
                if (!self.data.selectedRunes[slotIndex]) {
                    self.showRuneSelector(slotIndex);
                } else {
                    self.showRuneDetails(slotIndex);
                }
            }
        });

        // Add row button click
        this.elements.addRowButton.addEventListener('click', function() {
            self.addSlot();
        });

        // No need for rune selector event listeners as they're handled by SelectionWindowManager

        // Level control buttons in details panel
        this.elements.detailsContent.addEventListener('click', function(e) {
            if (e.target.classList.contains('level-down-button')) {
                const slotIndex = parseInt(e.target.dataset.slot, 10);
                self.levelDownRune(slotIndex);
            } else if (e.target.classList.contains('level-up-button')) {
                const slotIndex = parseInt(e.target.dataset.slot, 10);
                self.levelUpRune(slotIndex);
            }
        });

        // Note: Max All Runes and Reset All button events are now handled by QuickFillButtonManager
    },

    /**
     * Get the count of used slots
     */
    getUsedSlots: function() {
        return this.data.selectedRunes.filter(rune => rune !== null).length;
    },

    /**
     * Generate HTML for all rune slots
     */
    generateSlotsHTML: function() {
        let html = '';

        for (let i = 0; i < this.data.totalSlots; i++) {
            const rune = this.data.selectedRunes[i];

            let slotClass = 'essence-rune-slot';
            if (rune) {
                slotClass += ' filled';
            } else {
                slotClass += ' empty';
            }

            html += `
                <div class="${slotClass}" data-slot="${i}">
                    <div class="essence-rune-slot-level">${i + 1}</div>
                    ${this.getRuneContentHTML(rune, i)}
                </div>
            `;
        }

        return html;
    },

    /**
     * Generate HTML for the stats summary
     */
    generateStatsHTML: function() {
        // Calculate the total value for each base stat type
        const baseStatTotals = {};
        const baseStatInfo = {}; // Store info for each base stat
        const equippedCount = this.data.selectedRunes.filter(rune => rune !== null).length;

        // If no runes equipped, show empty message
        if (equippedCount === 0) {
            return '<div class="essence-rune-stats-empty">No Essence Runes equipped yet</div>';
        }

        // Calculate totals grouped by base stat type
        this.data.selectedRunes.forEach(rune => {
            if (!rune) return;

            // Find rune definition
            const runeDefInfo = this.findRuneDefinition(rune.type);
            if (!runeDefInfo) return;

            const { variant, definition, baseStatType } = runeDefInfo;

            // Calculate value based on rune level
            const levelIndex = Math.min(rune.level - 1, variant.valuePerLevel.length - 1);
            const value = variant.valuePerLevel[levelIndex];

            // Store info about this base stat if not already stored
            if (!baseStatInfo[baseStatType]) {
                baseStatInfo[baseStatType] = {
                    baseStatType: baseStatType,
                    iconId: definition.iconId,
                    isPercent: StatsConfig.getStatInfo(baseStatType).isPercentage
                };
            }

            // Initialize the total for this base stat type if not already
            if (!baseStatTotals[baseStatType]) {
                baseStatTotals[baseStatType] = 0;
            }

            // Add the value to the total for this base stat type
            baseStatTotals[baseStatType] += value;
        });

        // Generate the HTML for each base stat
        let html = '';

        // Sort the base stat types alphabetically
        const sortedBaseStatTypes = Object.keys(baseStatTotals).sort((a, b) => {
            const nameA = StatsConfig.getStatInfo(a).name || a;
            const nameB = StatsConfig.getStatInfo(b).name || b;
            return nameA.localeCompare(nameB);
        });

        // Add each base stat to the HTML
        sortedBaseStatTypes.forEach(baseStatType => {
            const statInfo = baseStatInfo[baseStatType];
            const statValue = baseStatTotals[baseStatType];

            // Get icon HTML
            let iconHtml = '';
            if (typeof StatsConfig !== 'undefined') {
                try {
                    const iconUrl = StatsConfig.getStatIconUrl(statInfo.iconId || baseStatType);
                    iconHtml = `<img src="${iconUrl}" alt="${baseStatType}" class="essence-rune-stat-icon" onerror="this.style.display='none';">`;
                } catch (e) {
                    // Ignore icon error
                }
            }

            // Format the stat display using StatsConfig
            const displayValue = StatsConfig.formatStatValue(baseStatType, statValue);
            const displayName = StatsConfig.getStatInfo(baseStatType).name || this.formatStatName(baseStatType);

            html += `
                <div class="essence-rune-stat-item">
                    <div class="essence-rune-stat-name">${iconHtml}${displayName}</div>
                    <div class="essence-rune-stat-value">${displayValue}</div>
                </div>
            `;
        });

        return html;
    },

    /**
     * Format a stat name from camelCase to Title Case
     */
    formatStatName: function(statId) {
        // First convert camelCase to space-separated words
        const spacedName = statId.replace(/([A-Z])/g, ' $1');

        // Now capitalize first letter of each word
        return spacedName
            // First letter of whole string
            .replace(/^[a-z]/, (match) => match.toUpperCase())
            // First letter after space
            .replace(/ [a-z]/g, (match) => match.toUpperCase());
    },

    /**
     * Show the rune selector for a specific slot
     */
    showRuneSelector: function(slotIndex) {
        // Set the current slot being edited
        this.data.currentSlot = slotIndex;

        // Check if EssenceRuneData is properly loaded
        if (!window.EssenceRuneData || !window.EssenceRuneData.runeDefinitions) {
            // Show error using the selection window
            this.selectionWindow.show({
                title: 'Error Loading Runes',
                options: [{
                    html: '<div class="fg-selection-empty-state">Error loading rune data. Please refresh the page.</div>',
                    data: {}
                }]
            });
            return;
        }

        // Get currently selected rune IDs to prevent duplicates
        const selectedRuneIds = this.data.selectedRunes
            .filter(rune => rune !== null)
            .map(rune => rune.type);

        // Collect all rune variants from definitions
        const allRuneTypes = [];

        // Process each base rune type and its variants
        Object.entries(window.EssenceRuneData.runeDefinitions).forEach(([baseStatType, definition]) => {
            if (!definition.variants || !Array.isArray(definition.variants)) return;

            definition.variants.forEach(variant => {
                allRuneTypes.push({
                    id: variant.id,
                    name: variant.name || baseStatType,
                    color: variant.color || '#4caf50',
                    tier: variant.tier || 1,
                    isPercent: StatsConfig.getStatInfo(baseStatType).isPercentage,
                    baseStatType: definition.baseStatType || baseStatType,
                    iconId: definition.iconId || baseStatType
                });
            });
        });

        // Sort runes: first by tier, then by name
        allRuneTypes.sort((a, b) => {
            if (a.tier !== b.tier) return a.tier - b.tier;
            return a.name.localeCompare(b.name);
        });

        // Prepare options for the selection window
        const options = allRuneTypes.map(rune => {
            // Check if this rune is already selected
            const isSelected = selectedRuneIds.includes(rune.id);

            // Get icon for the rune using StatsConfig
            let iconHtml = '';
            if (typeof StatsConfig !== 'undefined') {
                try {
                    const iconUrl = StatsConfig.getStatIconUrl(rune.iconId);
                    iconHtml = `<img src="${iconUrl}" alt="${rune.name}" class="fg-selection-option-icon" onerror="handleRuneIconError(this, '${rune.color}')">`;
                } catch (e) {
                    iconHtml = `<div class="fg-selection-option-icon-placeholder" style="background-color: ${rune.color}; width: 20px; height: 20px; display: inline-block; margin-right: 8px;"></div>`;
                }
            } else {
                iconHtml = `<div class="fg-selection-option-icon-placeholder" style="background-color: ${rune.color}; width: 20px; height: 20px; display: inline-block; margin-right: 8px;"></div>`;
            }

            return {
                html: `
                    <div class="fg-selection-option-with-icon">
                        ${iconHtml}
                        <span class="fg-selection-option-name" style="color: ${rune.color}">${rune.name}</span>
                    </div>
                    <span class="fg-selection-option-action">${isSelected ? 'Selected' : 'Select'}</span>
                `,
                className: isSelected ? 'disabled' : '',
                data: {
                    type: rune.id,
                    disabled: isSelected
                }
            };
        });

        // Show the selection window
        this.selectionWindow.show({
            title: 'Select Essence Rune',
            options: options
        });
    },

    /**
     * Hide the rune selector
     */
    hideRuneSelector: function() {
        this.selectionWindow.hide();
        this.data.currentSlot = null;
    },

    /**
     * Select a rune type and add it to the current slot
     */
    selectRune: function(runeType) {
        // If no current slot, return
        if (this.data.currentSlot === null) return;

        // Find rune definition using our helper
        const runeDefInfo = this.findRuneDefinition(runeType);
        if (!runeDefInfo) {
            console.warn(`Rune type ${runeType} not found in runeDefinitions. Add it to essence-rune-data.js.`);
            return;
        }

        // Add the rune to the selected slot
        this.data.selectedRunes[this.data.currentSlot] = {
            type: runeType,
            id: runeType,
            level: 1,
            maxLevel: runeDefInfo.variant.maxLevel
        };

        // Update the UI
        this.refreshRuneSlots();
        this.hideRuneSelector();

        // Update the stats display
        this.updateStatsDisplay();

        // Explicitly update stats in the build planner when a new rune is added
        this.updateStats();

        // Save the changes
        this.saveToStore();
    },

    /**
     * Update UI after a rune change
     */
    updateUIAfterChange: function(slotIndex) {
        // Update all UI elements at once
        this.refreshUI();

        // Show updated details for the selected rune
        this.showRuneDetails(slotIndex);

        // Update stats in the build planner
        this.updateStats();
    },

    /**
     * Calculate stats for the selected runes
     */
    calculateStats: function() {
        const stats = {};

        // Process each equipped rune
        this.data.selectedRunes.forEach(rune => {
            if (!rune) return; // Skip empty slots

            // Find the rune definition using our helper
            const runeDefInfo = this.findRuneDefinition(rune.type);
            if (!runeDefInfo) return; // Skip invalid runes

            const { variant, definition, baseStatType } = runeDefInfo;

            // Get the value based on current level
            const levelIndex = Math.min(rune.level - 1, variant.valuePerLevel.length - 1);
            const statValue = variant.valuePerLevel[levelIndex];

            // Add to the appropriate stat (using the base stat type from the definition)
            const statId = definition.baseStatType || baseStatType;

            if (stats[statId]) {
                stats[statId] += statValue;
            } else {
                stats[statId] = statValue;
            }
        });

        return stats;
    },

    /**
     * Level up a rune
     */
    levelUpRune: function(slotIndex) {
        const rune = this.data.selectedRunes[slotIndex];
        if (!rune) return;

        // Find rune definition to get max level
        const runeDefInfo = this.findRuneDefinition(rune.type);

        // Set default max level if not found
        let maxLevel = 10;

        if (runeDefInfo) {
            maxLevel = runeDefInfo.variant.maxLevel;
        } else if (rune.maxLevel) {
            // If not found in runeDefinitions, use maxLevel property if available
            maxLevel = rune.maxLevel;
        }

        // Increase level if not at max
        if (rune.level < maxLevel) {
            rune.level++;

            // Update the UI
            this.updateUIAfterChange(slotIndex);

            // Save the changes
            this.saveToStore();
        }
    },

    /**
     * Level down a rune
     */
    levelDownRune: function(slotIndex) {
        const rune = this.data.selectedRunes[slotIndex];
        if (!rune) return;

        // Decrease level if not at minimum
        if (rune.level > 1) {
            rune.level--;

            // Update the UI
            this.updateUIAfterChange(slotIndex);

            // Show details for the updated rune
            this.showRuneDetails(slotIndex);

            // Save the changes
            this.saveToStore();
        }
    },

    /**
     * Show details for a specific slot
     */
    showRuneDetails: function(slotIndex) {
        const rune = this.data.selectedRunes[slotIndex];

        if (!this.elements.detailsContent) {
            return;
        }

        if (!rune) {
            this.elements.detailsContent.innerHTML = `
                <h3>Slot ${slotIndex + 1} Details</h3>
                <div class="essence-rune-details-empty">
                    No rune equipped in this slot
                </div>
            `;
            return;
        }

        // Find rune definition using our helper method
        const runeDefInfo = this.findRuneDefinition(rune.type);

        // If rune definition not found, show error message
        if (!runeDefInfo) {
            console.warn(`Rune ${rune.type} not found in runeDefinitions. Add it to essence-rune-data.js.`);
            this.elements.detailsContent.innerHTML = `
                <div class="essence-rune-details-header">
                    <div class="essence-rune-details-name" style="color: #ff0000">
                        ${rune.type} (Missing Definition)
                    </div>
                </div>
                <div class="essence-rune-location">
                    <strong>Location:</strong> Missing definition - update essence-rune-data.js
                </div>
            `;
            return;
        }

        const { variant: runeData, definition: baseDefinition, baseStatType, iconId } = runeDefInfo;

        // Get base stat name from StatsConfig
        const baseStatName = StatsConfig.getStatInfo(baseStatType).name || runeData.name.replace(/ I+$/, '');

        // Get rune stats based on level
        const levelIndex = Math.min(rune.level - 1, runeData.valuePerLevel.length - 1);
        const value = runeData.valuePerLevel[levelIndex];
        // Use StatsConfig for consistent stat formatting
        const statValue = StatsConfig.formatStatValue(baseStatType, value);
        const location = runeData.location || 'Unknown';

        // Get icon HTML
        let iconHtml = '';
        if (typeof StatsConfig !== 'undefined') {
            try {
                const iconUrl = StatsConfig.getStatIconUrl(iconId);
                iconHtml = `<img src="${iconUrl}" alt="${runeData.name}" class="essence-rune-stat-icon">`;
            } catch (e) {
                // Use color block instead
                iconHtml = `<div class="essence-rune-icon-inner" style="background-color: ${runeData.color || '#4caf50'}"></div>`;
            }
        }

        // Build the details HTML
        const detailsHTML = `
            <div class="essence-rune-details-header">
                <div class="essence-rune-details-name" style="color: ${runeData.color || '#4caf50'}">
                    ${iconHtml} ${runeData.name}
                </div>
                <div class="essence-rune-details-value">
                    ${baseStatName} +${statValue}
                </div>
            </div>
            <div class="essence-rune-location">
                <strong>Location:</strong> ${location}
            </div>
            <div class="essence-rune-level-controls">
                <button class="${rune.level <= 1 ? 'level-down-button disabled' : 'level-down-button'}" data-slot="${slotIndex}">-</button>
                <span>Level: ${rune.level}</span>
                <button class="${rune.level >= runeData.maxLevel ? 'level-up-button disabled' : 'level-up-button'}" data-slot="${slotIndex}">+</button>
            </div>
        `;

        this.elements.detailsContent.innerHTML = detailsHTML;
    },

    /**
     * Get description for a rune type and level
     */
    getRuneDescription: function(typeId, level) {
        // First check in runeDefinitions
        let foundInDefinitions = false;
        let value = null;

        // Look in runeDefinitions first
        for (const [baseStatType, definition] of Object.entries(this.data.runeDefinitions)) {
            const variant = definition.variants.find(v => v.id === typeId);
            if (variant && variant.valuePerLevel) {
                foundInDefinitions = true;
                const levelIndex = Math.min(level - 1, variant.valuePerLevel.length - 1);
                value = variant.valuePerLevel[levelIndex];
                break;
            }
        }

        // If found in runeDefinitions, return appropriate description
        if (foundInDefinitions && value !== null) {
            switch (typeId) {
                case 'exp':
                    return `Increases experience gain by ${value}%. This affects all experience earned in-game.`;
                case 'skillExp':
                    return `Increases skill experience gain by ${value}%. This affects all skill experience earned in-game.`;
                case 'partyExp':
                    return `Increases party experience gain by ${value}%. This affects experience earned when in a party.`;
                case 'petExp':
                    return `Increases pet experience gain by ${value}%. This affects all pet experience earned in-game.`;
                case 'alzDropAmount':
                    return `Increases Alz drop amount by ${value}%. This affects the amount of Alz dropped from monsters.`;
                case 'alzDropRate':
                    return `Increases Alz drop rate by ${value}%. This affects the chance of Alz dropping from monsters.`;
                case 'alzBombChance':
                    return `Increases Alz Bomb chance by ${value}%. This affects the chance of getting a large Alz drop.`;
            }
        }

        // Placeholder descriptions for other runes that aren't specifically handled above
        const runeType = this.getRuneType(typeId);
        return `${runeType.name} at level ${level}`;
    },

    /**
     * Update stats in BuildPlanner
     */
    updateStats: function() {
        // Calculate stats from all runes
        const stats = this.calculateStats();

        // Update the stats in the build planner
        if (typeof BuildPlanner !== 'undefined' && BuildPlanner.updateStats) {
            BuildPlanner.updateStats('essence-runes', stats);
        }

        // Update the stats display in our system UI
        this.updateStatsDisplay();
    },

    /**
     * Add a new slot
     */
    addSlot: function() {
        // Check if we've reached the maximum slots
        if (this.data.totalSlots >= this.data.maxSlots) {
            console.warn('Maximum essence rune slots reached');
            return;
        }

        // Add a single new slot
        this.data.totalSlots++;

        // Update the UI
        this.elements.slotsContainer.innerHTML = this.generateSlotsHTML();
        this.elements.slotsCount.textContent = `${this.getUsedSlots()}/${this.data.totalSlots}`;

        // Hide the add slot button if we've reached the maximum
        if (this.data.totalSlots >= this.data.maxSlots) {
            this.elements.addRowButton.style.display = 'none';
        }
    },

    /**
     * Update the slots counter in the UI
     */
    updateSlotsCounter: function() {
        this.elements.slotsCount.textContent = `${this.getUsedSlots()}/${this.data.totalSlots}`;
    },

    /**
     * Generate slots based on the totalSlots
     */
    generateSlots: function() {
        // Check if EssenceRuneData is available
        if (!window.EssenceRuneData || !window.EssenceRuneData.runeDefinitions) {
            setTimeout(() => this.generateSlots(), 500);
            return;
        }

        // Generate HTML for the slots
        const html = this.generateSlotsHTML();

        // Update the slots container
        if (this.elements.slotsContainer) {
            this.elements.slotsContainer.innerHTML = html;
        }
    },

    /**
     * Refresh the UI for all rune slots
     */
    refreshRuneSlots: function() {
        // Generate slots HTML
        const html = this.generateSlotsHTML();

        // Update the slots container
        if (this.elements.slotsContainer) {
            this.elements.slotsContainer.innerHTML = html;
        } else {
            console.error('Slots container element not found');
        }
    },

    /**
     * Show empty details
     */
    showEmptyDetails: function() {
        this.elements.detailsContent.innerHTML = `
            <h3>Slot Details</h3>
            <div class="essence-rune-details-empty">
                No rune equipped in this slot
            </div>
        `;
    },

    /**
     * Get rune type information by ID
     */
    getRuneType: function(typeId) {
        // Use our findRuneDefinition helper method
        const runeDefInfo = this.findRuneDefinition(typeId);

        if (runeDefInfo) {
            const { variant, baseStatType } = runeDefInfo;
            return {
                id: variant.id,
                name: variant.name,
                isPercent: StatsConfig.getStatInfo(baseStatType).isPercentage,
                color: variant.color || '#4caf50'
            };
        }

        // If not found, return error object
        console.warn(`Rune type ${typeId} not found. Add it to essence-rune-data.js.`);
        return {
            id: typeId,
            name: `Unknown Type: ${typeId}`,
            isPercent: false,
            color: '#ff0000' // Red to indicate error
        };
    },

    /**
     * Max out all available runes
     * - Add slots up to the number of available runes
     * - Equip all available runes
     * - Level all runes to their max level
     */
    maxAllRunes: function() {
        // Get all available rune types
        const availableRunes = [];

        // Get runes from runeDefinitions (for variants)
        for (const [baseStatType, definition] of Object.entries(this.data.runeDefinitions)) {
            definition.variants.forEach(variant => {
                availableRunes.push(variant.id);
            });
        }

        // Get currently equipped runes
        const equippedRunes = this.data.selectedRunes
            .filter(rune => rune !== null)
            .map(rune => rune.type);

        // Filter out runes that are already equipped
        const runesToEquip = availableRunes.filter(runeId => !equippedRunes.includes(runeId));

        // First, add slots if needed
        const totalRunesAfterEquipping = equippedRunes.length + runesToEquip.length;
        const slotsNeeded = Math.min(totalRunesAfterEquipping, this.data.maxSlots);

        // Add slots until we have enough
        while (this.data.totalSlots < slotsNeeded) {
            this.addSlot();
        }

        // Now equip all available runes
        let currentSlot = 0;
        for (const runeId of runesToEquip) {
            // Find the next empty slot
            while (currentSlot < this.data.totalSlots && this.data.selectedRunes[currentSlot] !== null) {
                currentSlot++;
            }

            // If we've run out of slots, break
            if (currentSlot >= this.data.totalSlots) {
                break;
            }

            // Add the rune to this slot
            let runeData = null;
            let maxLevel = 10; // Default max level

            // Look for this rune in our runeDefinitions structure
            for (const [baseStatType, definition] of Object.entries(this.data.runeDefinitions)) {
                const variant = definition.variants.find(v => v.id === runeId);
                if (variant) {
                    runeData = variant;
                    maxLevel = variant.maxLevel;
                    break;
                }
            }

            // Add rune to the slot
            this.data.selectedRunes[currentSlot] = {
                type: runeId,
                id: runeId,
                level: maxLevel, // Set to max level immediately
                maxLevel: maxLevel
            };

            // Move to the next slot
            currentSlot++;
        }

        // Now max out all existing runes
        for (let i = 0; i < this.data.totalSlots; i++) {
            const rune = this.data.selectedRunes[i];
            if (rune) {
                // Find max level for this rune
                let maxLevel = 10; // Default max level

                // Check in runeDefinitions
                for (const [baseStatType, definition] of Object.entries(this.data.runeDefinitions)) {
                    const variant = definition.variants.find(v => v.id === rune.type);
                    if (variant) {
                        maxLevel = variant.maxLevel;
                        break;
                    }
                }

                // Set to max level
                rune.level = maxLevel;
            }
        }

        // Update UI and stats
        this.refreshRuneSlots();
        this.updateSlotsCounter();
        this.updateStatsDisplay();
        this.updateStats();

        // Save the changes
        this.saveToStore();
    },

    /**
     * Reset all runes and slots to default
     * - Remove all equipped runes
     * - Reset slot count to default (16)
     */
    resetAllRunes: function() {
        // Ask for confirmation
        if (!confirm('Are you sure you want to remove all runes and reset to default slots? This cannot be undone.')) {
            return;
        }

        // Reset all runes
        this.data.selectedRunes = Array(this.data.maxSlots).fill(null);

        // Reset total slots to default
        this.data.totalSlots = 16; // Default slot count

        // Update UI and stats
        this.refreshRuneSlots();
        this.updateSlotsCounter();
        this.updateStatsDisplay();
        this.updateStats();

        // Save the changes
        this.saveToStore();
    },

    /**
     * Get details of a specific rune for the details panel
     */
    getRuneDetails: function(rune) {
        if (!rune) return null;

        let runeData = null;
        let progressionData = null;
        let iconId = null;
        let baseStatType = null;

        // First check if this rune is in our runeDefinitions structure
        for (const [bStatType, definition] of Object.entries(this.data.runeDefinitions)) {
            const variant = definition.variants.find(v => v.id === rune.type);
            if (variant) {
                runeData = variant;
                iconId = definition.iconId; // Use the icon ID from the definition
                baseStatType = bStatType; // Store the base stat type
                break;
            }
        }

        // If we have no rune data, return placeholder info
        if (!runeData) {
            return {
                name: 'Unknown Rune',
                currentLevel: rune.level,
                maxLevel: rune.maxLevel || 10,
                currentValue: 0,
                nextValue: 0,
                apCost: 0,
                materials: [],
                statType: 'unknown',
                iconUrl: null
            };
        }

        // Calculate current and next values
        const levelIndex = Math.min(rune.level - 1, runeData.valuePerLevel.length - 1);
        const currentValue = runeData.valuePerLevel[levelIndex];

        // Calculate next level value (if not at max)
        const nextLevelIndex = Math.min(rune.level, runeData.valuePerLevel.length - 1);
        const nextValue = runeData.valuePerLevel[nextLevelIndex];

        // Calculate AP cost for next level (if not at max)
        const apCostIndex = Math.min(rune.level, runeData.apCost.length - 1);
        const apCost = runeData.apCost[apCostIndex];

        // Get materials for next level (if not at max)
        let materials = [];
        if (rune.level < runeData.maxLevel && runeData.materials && runeData.materials.length > 0) {
            const materialIndex = Math.min(rune.level + 1, runeData.materials.length - 1);
            const materialData = runeData.materials[materialIndex];

            if (materialData && materialData.name && materialData.quantity > 0) {
                materials.push({
                    name: materialData.name,
                    quantity: materialData.quantity
                });
            }
        }

        // Get icon URL from StatsConfig if available
        let iconUrl = null;
        if (typeof StatsConfig !== 'undefined' && StatsConfig.getStatIconUrl && iconId) {
            iconUrl = StatsConfig.getStatIconUrl(iconId);
        }

        // Get stat type for formatting
        let isPercentage = false;
        if (typeof StatsConfig !== 'undefined' && StatsConfig.isStatPercentage && baseStatType) {
            isPercentage = StatsConfig.isStatPercentage(baseStatType);
        }

        // Return compiled details
        return {
            name: runeData.name,
            currentLevel: rune.level,
            maxLevel: runeData.maxLevel,
            currentValue: currentValue,
            nextValue: nextValue,
            apCost: apCost,
            materials: materials,
            statType: baseStatType,
            iconUrl: iconUrl,
            isPercentage: isPercentage,
            location: runeData.location || 'Unknown'
        };
    },

    /**
     * Get all available rune types for the summary dropdown
     */
    getAllRuneTypes: function() {
        const runeTypes = [];

        // Add all types from runeDefinitions
        if (this.data.runeDefinitions) {
            for (const [baseStatType, definition] of Object.entries(this.data.runeDefinitions)) {
                definition.variants.forEach(variant => {
                    runeTypes.push({
                        id: variant.id,
                        name: variant.name,
                        baseStatType: baseStatType
                    });
                });
            }
        }

        // Sort alphabetically by name
        runeTypes.sort((a, b) => a.name.localeCompare(b.name));

        return runeTypes;
    }
};

// Safety check to ensure the system is available when needed
document.addEventListener('DOMContentLoaded', function() {
    // System is now loaded
});

