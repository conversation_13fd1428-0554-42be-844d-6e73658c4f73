/**
 * Equipment Data for Build Planner
 * Contains definitions for all equipment items, their stats, and upgrade paths
 */

window.EquipmentData = {
    /**
     * Equipment metadata - defines upgrade paths and capabilities by item type
     */
    itemTypes: {
        weapon: {
            upgradePaths: ["base", "extreme", "divine", "slots", "epic"],
            maxBaseLevel: 20,
            canHaveEpicOption: true,
            hasSlots: true
        },
        armor: {
            upgradePaths: ["base", "extreme", "divine", "slots", "epic"],
            maxBaseLevel: 20,
            canHaveEpicOption: true,
            hasSlots: true
        },
        helmet: {
            upgradePaths: ["base", "extreme", "divine", "slots", "epic"],
            maxBaseLevel: 20,
            canHaveEpicOption: true,
            hasSlots: true
        },
        gloves: {
            upgradePaths: ["base", "extreme", "divine", "slots", "epic"],
            maxBaseLevel: 20,
            canHaveEpicOption: true,
            hasSlots: true
        },
        boots: {
            upgradePaths: ["base", "extreme", "divine", "slots", "epic"],
            maxBaseLevel: 20,
            canHaveEpicOption: true,
            hasSlots: true
        },
        ring: {
            upgradePaths: [],
            canHaveEpicOption: false,
            hasSlots: false
        },
        earring: {
            upgradePaths: ["chaos"],
            maxChaosLevel: 15,
            canHaveEpicOption: false,
            hasSlots: false
        },
        bracelet: {
            upgradePaths: ["chaos"],
            maxChaosLevel: 15,
            canHaveEpicOption: false,
            hasSlots: false
        },
        amulet: {
            upgradePaths: ["chaos"],
            maxChaosLevel: 15,
            canHaveEpicOption: false,
            hasSlots: false
        },
        arcana: {
            upgradePaths: ["base"],
            maxBaseLevel: 20,
            canHaveEpicOption: false,
            hasSlots: false
        },
        belt: {
            upgradePaths: ["base"],
            maxBaseLevel: 20,
            canHaveEpicOption: false,
            hasSlots: false
        },
        carnelian: {
            upgradePaths: ["base"],
            maxBaseLevel: 20,
            canHaveEpicOption: false,
            hasSlots: false
        },
        talisman: {
            upgradePaths: ["base"],
            maxBaseLevel: 20,
            canHaveEpicOption: false,
            hasSlots: false
        },
        epaulet: {
            upgradePaths: [],
            canHaveEpicOption: false,
            hasSlots: false
        },
        bike: {
            upgradePaths: ["base", "extreme", "divine", "slots", "epic"],
            maxBaseLevel: 20,
            canHaveEpicOption: true,
            hasSlots: true
        },
        effector: {
            upgradePaths: [],
            canHaveEpicOption: false,
            hasSlots: false
        }
    },

    /**
     * Equipment items organized by type
     */
    items: {
        // Weapons are now defined in weapons-data.js
        weapons: [],
        
        // Each item type will be defined in separate files
        armor: [],
        helmets: [],
        gloves: [],
        boots: [],
        rings: [],
        earrings: [],
        bracelets: [],
        amulets: [],
        arcanas: [],
        belts: [],
        carnelians: [],
        talismans: [],
        epaulets: [],
        bikes: [],
        effectors: []
    },

    /**
     * Initialization function that will be called by the equipment system
     * after all equipment data modules are loaded
     */
    init: function() {
        // Import weapons data when it becomes available
        if (window.WeaponsData) {
            this.items.weapons = WeaponsData.weapons;
            
            // Import weapon upgrade data
            this.gradeUpgrades = WeaponsData.gradeUpgrades;
            this.extremeUpgrades = WeaponsData.extremeUpgrades;
            this.divineUpgrades = WeaponsData.divineUpgrades;
            this.epicOptions = WeaponsData.epicOptions;
            this.slotOptions = WeaponsData.slotOptions;
        }
        
        // Import belts data when it becomes available
        if (window.BeltsData) {
            this.items.belts = BeltsData.belts;
        }
        
        // Other item type data modules will be loaded similarly
    },

    /**
     * Base upgrade data for different item materials
     * Note: Weapon-specific upgrade data is now in WeaponsData
     */
    baseUpgrades: {
        // For armor items
        plate: [
            { level: 0, defenseMod: 1.00, magicDefenseMod: 1.00, hpMod: 1.00 },
            { level: 1, defenseMod: 1.03, magicDefenseMod: 1.02, hpMod: 1.01 },
            // ... additional levels would be defined similarly
            { level: 20, defenseMod: 1.60, magicDefenseMod: 1.40, hpMod: 1.30 }
        ],
        
        // For accessories and other items that use simple multipliers
        generic: [
            { level: 0, statMod: 1.00 },
            { level: 1, statMod: 1.02 },
            { level: 2, statMod: 1.04 },
            // ... additional levels would be defined similarly
            { level: 20, statMod: 1.40 }
        ]
    },
    
    /**
     * Chaos upgrade levels for accessories (0-15)
     */
    chaosUpgrades: {
        // Different tiers of chaos upgrades
        gold: [
            // Level 0 - No bonuses
            [],
            // Level 1
            [
                { stat: 'allAttackUp', value: 10 }
            ],
            // Levels 2-15 would follow with increasing values
            // Level 15
            [
                { stat: 'allAttackUp', value: 150 },
                { stat: 'critRate', value: 10 },
                { stat: 'critDamage', value: 20 }
            ]
        ],
        
        platinum: [
            // Level 0 - No bonuses
            [],
            // Level 1 with better stats than gold
            [
                { stat: 'allAttackUp', value: 15 }
            ],
            // Levels 2-15 would follow with increasing values
            // Level 15
            [
                { stat: 'allAttackUp', value: 200 },
                { stat: 'critRate', value: 15 },
                { stat: 'critDamage', value: 25 }
            ]
        ]
    }
}; 