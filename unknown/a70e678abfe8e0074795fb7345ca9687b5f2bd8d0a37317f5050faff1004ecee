/**
 * Quick Fill Button Manager CSS
 * Shared styling for quick fill buttons across all systems
 */

/* Container styling */
.fg-quick-fill-container {
    display: flex;
    gap: 8px;
    align-items: center;
    margin-bottom: 15px;
}

/* Position-specific container styling */
.fg-quick-fill-container.fg-quick-fill-top {
    margin-bottom: 15px;
    margin-top: 0;
}

.fg-quick-fill-container.fg-quick-fill-header {
    margin-bottom: 0;
    margin-left: auto; /* Push to right side of header by default */
}

.fg-quick-fill-container.fg-quick-fill-custom {
    margin-bottom: 10px;
}

/* Alignment options */
.fg-quick-fill-container.fg-quick-fill-align-left {
    justify-content: flex-start;
}

.fg-quick-fill-container.fg-quick-fill-align-center {
    justify-content: center;
}

.fg-quick-fill-container.fg-quick-fill-align-right {
    justify-content: flex-end;
}

/* Base button styling - ALL QUICK FILL BUTTONS USE SAME GREEN DESIGN */
.fg-quick-fill-button {
    padding: 6px 12px;
    border: none;
    border-radius: 4px;
    font-size: 0.85rem;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.2s ease;
    white-space: nowrap;
    min-height: 32px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    /* Consistent green design for all quick fill buttons */
    background-color: #4e9a06;
    color: white;
    border: 1px solid #5fb107;
}

.fg-quick-fill-button:hover:not(:disabled) {
    background-color: #5fb107;
    box-shadow: 0 0 8px rgba(95, 177, 7, 0.4);
    transform: translateY(-1px);
}

.fg-quick-fill-button:active:not(:disabled) {
    transform: translateY(0);
    box-shadow: 0 0 4px rgba(95, 177, 7, 0.6);
}

.fg-quick-fill-button:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

.fg-quick-fill-button:disabled:hover {
    transform: none;
    box-shadow: none;
}

/* Danger button (for destructive actions like reset) */
.fg-quick-fill-button-danger {
    background-color: #c62828;
    color: white;
    border: 1px solid #d32f2f;
}

.fg-quick-fill-button-danger:hover:not(:disabled) {
    background-color: #d32f2f;
    box-shadow: 0 0 8px rgba(211, 47, 47, 0.5);
    transform: translateY(-1px);
}

.fg-quick-fill-button-danger:active:not(:disabled) {
    transform: translateY(0);
    box-shadow: 0 0 4px rgba(211, 47, 47, 0.7);
}

/* Responsive design */
@media screen and (max-width: 768px) {
    .fg-quick-fill-container {
        flex-wrap: wrap;
        gap: 6px;
    }

    .fg-quick-fill-button {
        padding: 5px 10px;
        font-size: 0.8rem;
        min-height: 28px;
    }
}

@media screen and (max-width: 480px) {
    .fg-quick-fill-container {
        width: 100%;
        justify-content: center;
    }

    .fg-quick-fill-container.fg-quick-fill-header {
        margin-left: 0;
        margin-top: 8px;
    }

    .fg-quick-fill-button {
        flex: 1;
        min-width: 80px;
        max-width: 120px;
    }
}

/* Integration with existing header styles */
.essence-runes-header .fg-quick-fill-container,
.karma-runes-header .fg-quick-fill-container {
    margin-bottom: 0;
}

/* Special handling for systems with existing button containers */
.essence-runes-buttons .fg-quick-fill-container {
    gap: 8px;
    margin-bottom: 0;
}

/* Focus styles for accessibility */
.fg-quick-fill-button:focus {
    outline: 2px solid #fff;
    outline-offset: 2px;
}

.fg-quick-fill-button:focus:not(:focus-visible) {
    outline: none;
}

/* Loading state (for future use) */
.fg-quick-fill-button.loading {
    position: relative;
    color: transparent;
}

.fg-quick-fill-button.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 16px;
    height: 16px;
    margin: -8px 0 0 -8px;
    border: 2px solid transparent;
    border-top: 2px solid currentColor;
    border-radius: 50%;
    animation: button-spin 1s linear infinite;
}

@keyframes button-spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}
