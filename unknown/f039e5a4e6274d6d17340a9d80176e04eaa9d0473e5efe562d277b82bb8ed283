/**
 * ItemType Data for Build Planner
 * Contains definitions for ItemType items and their upgrade data
 */

window.ItemTypeData = {
    /**
     * All available itemTypes in the game
     */
    itemTypes: [
        {
            id: 'item1_id',
            name: 'Item 1 Name',
            type: 'itemType',
            imagePath: forceguidesPlannerData.pluginUrl + 'assets/images/equipment/item1_image.png',
            description: 'Description of Item 1',
            baseStats: {
                // Base stats - use exact stat IDs from StatsConfig
                hp: 10
            },
            maxBaseLevel: 20,
            upgradeData: 'item1'
        },
        
        {
            id: 'item2_id',
            name: 'Item 2 Name',
            type: 'itemType',
            imagePath: forceguidesPlannerData.pluginUrl + 'assets/images/equipment/item2_image.png',
            description: 'Description of Item 2',
            baseStats: {
                // Base stats - use exact stat IDs from StatsConfig
                hp: 10
            },
            maxBaseLevel: 20,
            upgradeData: 'item2'
        }
    ],

    /**
     * Upgrade data for item1
     * Description of what this item does
     */
    item1: {
        upgrades: [
            // Level 0
            {
                // IMPORTANT: Use exact stat IDs from StatsConfig.js
                hp: 0,
                attack: 0,
                critDamage: 0,  // NOT critDmg - use full name from StatsConfig
                accuracy: 0,
                // Add other stats from StatsConfig as needed
            },
            // Level 1
            {
                hp: 20,
                attack: 5,
                critDamage: 0,
                accuracy: 0,
                // Add other stats from StatsConfig as needed
            },
            // Additional levels would be defined similarly...
            // Level 20
            {
                hp: 210,
                attack: 125,
                critDamage: 22,
                accuracy: 110,
                // Add other stats from StatsConfig as needed
            }
        ]
    },
    
    /**
     * Upgrade data for item2
     * Description of what this item does
     */
    item2: {
        upgrades: [
            // Level upgrade data similar to above
            // IMPORTANT: Make sure to use exact stat IDs from StatsConfig
        ]
    }
    
    // Additional items would be defined similarly...
};

// NOTE: Do NOT add items directly to EquipmentData here
// Items will be imported in equipment-data.js init() function 