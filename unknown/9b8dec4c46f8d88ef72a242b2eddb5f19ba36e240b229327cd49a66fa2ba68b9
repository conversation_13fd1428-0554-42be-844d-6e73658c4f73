/**
 * Karma Rune Data
 * Contains all definitions for karma runes
 */

// Export the rune definitions for use in the karma-runes-system.js
window.KarmaRuneData = {
    // Define runes with their variants for better organization
    runeDefinitions: {
        hp: {
            baseStatType: 'hp',
            iconId: 'hp', // Use this to resolve icon from StatsConfig
            variants: [
                {
                    id: 'karmaHp',
                    name: 'HP Karma Rune',
                    tier: 1,
                    maxLevel: 100,
                    // 10 HP per level, up to 1000 HP at level 100
                    valuePerLevel: Array.from({ length: 100 }, (_, i) => (i + 1) * 10),
                    apCost: Array.from({ length: 100 }, (_, i) => Math.floor(3 + i * 1.5)),
                    location: "Karma Rune Dungeon, Karma Shop",
                    materials: []
                }
            ]
        },
        pveAllAttackUp: {
            baseStatType: 'allAttackUp',
            iconId: 'allAttack',
            variants: [
                {
                    id: 'pveAllAttackUp',
                    name: 'PvE All Attack Up',
                    tier: 1,
                    maxLevel: 15,
                    valuePerLevel: [3, 6, 9, 12, 15, 18, 21, 24, 27, 30, 33, 36, 39, 42, 45],
                    apCost: [500, 500, 500, 500, 500, 600, 600, 600, 600, 600, 700, 700, 700, 700, 700],
                    runeCount: [1, 2, 3, 4, 5, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18],
                    location: "Karma Rune Dungeon, Karma Shop",
                    materials: []
                }
            ]
        },
        pvpAllAttackUp: {
            baseStatType: 'allAttackUp',
            iconId: 'allAttack',
            variants: [
                {
                    id: 'pvpAllAttackUp',
                    name: 'PvP All Attack Up',
                    tier: 1,
                    maxLevel: 15,
                    valuePerLevel: [3, 6, 9, 12, 15, 18, 21, 24, 27, 30, 33, 36, 39, 42, 45],
                    apCost: [500, 500, 500, 500, 500, 600, 600, 600, 600, 600, 700, 700, 700, 700, 700],
                    runeCount: [1, 2, 3, 4, 5, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18],
                    location: "Karma Rune Dungeon, Karma Shop",
                    materials: []
                }
            ]
        },
        pvpResistSkillAmp: {
            baseStatType: 'resistSkillAmp',
            iconId: 'resistSkillAmp',
            variants: [
                {
                    id: 'pvpResistSkillAmp',
                    name: 'PvP Resist Skill Amp.',
                    tier: 1,
                    maxLevel: 5,
                    valuePerLevel: [1, 2, 3, 4, 5],
                    apCost: [700, 700, 700, 1200, 2200],
                    runeCount: [0, 5, 7, 9, 11],
                    location: "Karma Rune Dungeon, Karma Shop",
                    materials: []
                }
            ]
        },
        pveAttackRate: {
            baseStatType: 'attackRate',
            iconId: 'attackRate',
            variants: [
                {
                    id: 'pveAttackRate',
                    name: 'PvE Attack Rate',
                    tier: 1,
                    maxLevel: 20,
                    valuePerLevel: [20, 40, 60, 80, 100, 120, 140, 160, 180, 200, 220, 240, 260, 280, 300, 320, 340, 360, 380, 400],
                    apCost: [400, 400, 500, 500, 500, 500, 500, 500, 500, 500, 600, 600, 600, 600, 600, 700, 700, 700, 700, 700],
                    runeCount: [0, 0, 5, 5, 5, 10, 10, 10, 10, 10, 15, 15, 15, 15, 15, 20, 20, 20, 20, 20],
                    location: "Karma Rune Dungeon, Karma Shop",
                    materials: []
                }
            ]
        },
        pveDefenseRate: {
            baseStatType: 'defenseRate',
            iconId: 'defenseRate',
            variants: [
                {
                    id: 'pveDefenseRate',
                    name: 'PvE Defense Rate',
                    tier: 1,
                    maxLevel: 20,
                    valuePerLevel: [20, 40, 60, 80, 100, 120, 140, 160, 180, 200, 220, 240, 260, 280, 300, 320, 340, 360, 380, 400],
                    apCost: [400, 400, 500, 500, 500, 500, 500, 500, 500, 500, 600, 600, 600, 600, 600, 700, 700, 700, 700, 700],
                    runeCount: [0, 0, 5, 5, 5, 10, 10, 10, 10, 10, 15, 15, 15, 15, 15, 20, 20, 20, 20, 20],
                    location: "Karma Rune Dungeon, Karma Shop",
                    materials: []
                }
            ]
        },
        pvpAttackRate: {
            baseStatType: 'attackRate',
            iconId: 'attackRate',
            variants: [
                {
                    id: 'pvpAttackRate',
                    name: 'PvP Attack Rate',
                    tier: 1,
                    maxLevel: 20,
                    valuePerLevel: [20, 40, 60, 80, 100, 120, 140, 160, 180, 200, 220, 240, 260, 280, 300, 320, 340, 360, 380, 400],
                    apCost: [400, 400, 500, 500, 500, 500, 500, 500, 500, 500, 600, 600, 600, 600, 600, 700, 700, 700, 700, 700],
                    runeCount: [0, 0, 5, 5, 5, 10, 10, 10, 10, 10, 15, 15, 15, 15, 15, 20, 20, 20, 20, 20],
                    location: "Karma Rune Dungeon, Karma Shop",
                    materials: []
                }
            ]
        },
        pvpDefenseRate: {
            baseStatType: 'defenseRate',
            iconId: 'defenseRate',
            variants: [
                {
                    id: 'pvpDefenseRate',
                    name: 'PvP Defense Rate',
                    tier: 1,
                    maxLevel: 20,
                    valuePerLevel: [20, 40, 60, 80, 100, 120, 140, 160, 180, 200, 220, 240, 260, 280, 300, 320, 340, 360, 380, 400],
                    apCost: [400, 400, 500, 500, 500, 500, 500, 500, 500, 500, 600, 600, 600, 600, 600, 700, 700, 700, 700, 700],
                    runeCount: [0, 0, 5, 5, 5, 10, 10, 10, 10, 10, 15, 15, 15, 15, 15, 20, 20, 20, 20, 20],
                    location: "Karma Rune Dungeon, Karma Shop",
                    materials: []
                }
            ]
        }
    }
};