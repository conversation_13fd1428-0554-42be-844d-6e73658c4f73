/**
 * Stat Integration Service
 * Provides unified stat summary generation and validation
 * for consistent display across all progression systems
 */

window.StatIntegrationService = {
    // Create a standardized stat summary HTML for any system
    createStatSummaryHTML: function(stats) {
        if (!stats || Object.keys(stats).length === 0) {
            return '<p class="no-stats">No stats selected yet.</p>';
        }
        
        // Group stats by category
        const categories = {
            offensive: [],
            defensive: [],
            utility: []
        };
        
        // Sort stats into categories
        for (const statId in stats) {
            const value = stats[statId];
            if (value === 0) continue;
            
            // Get stat info from StatsConfig
            let statName = statId;
            let isPercentage = false;
            let category = 'utility';
            
            if (typeof StatsConfig !== 'undefined') {
                const statInfo = StatsConfig.getStatInfo(statId);
                if (statInfo) {
                    statName = statInfo.name;
                    isPercentage = statInfo.isPercentage;
                    category = statInfo.category || 'utility';
                }
            }
            
            // Add to appropriate category
            categories[category].push({
                id: statId,
                name: statName,
                value: value,
                isPercentage: isPercentage
            });
        }
        
        // Create HTML structure
        let html = '<div class="fg-stat-summary">';
        
        // Category titles
        const categoryNames = {
            offensive: 'Offensive',
            defensive: 'Defensive',
            utility: 'Utility'
        };
        
        // Add stats by category
        for (const category in categories) {
            const categoryStats = categories[category];
            if (categoryStats.length === 0) continue;
            
            html += `<div class="fg-stat-category">
                <h4 class="fg-stat-category-title">${categoryNames[category]}</h4>
                <div class="fg-stat-list">`;
            
            // Sort stats by name
            categoryStats.sort((a, b) => a.name.localeCompare(b.name));
            
            // Add each stat
            categoryStats.forEach(stat => {
                // Get icon HTML
                let iconHtml = '';
                if (typeof StatsConfig !== 'undefined') {
                    try {
                        const iconUrl = StatsConfig.getStatIconUrl(stat.id);
                        iconHtml = `<img src="${iconUrl}" alt="${stat.name}" class="fg-stat-icon" onerror="this.style.display='none';">`;
                    } catch (e) {
                        // If icon fails, continue without it
                    }
                }
                
                html += `<div class="fg-stat-item" data-stat-id="${stat.id}">
                    <div class="fg-stat-info">
                        ${iconHtml}
                        <span class="fg-stat-name">${stat.name}</span>
                    </div>
                    <span class="fg-stat-value">+${stat.value}${stat.isPercentage ? '%' : ''}</span>
                </div>`;
            });
            
            html += `</div></div>`;
        }
        
        html += '</div>';
        return html;
    },
    
    // Add uniform styling to the page
    addSummaryStyles: function() {
        // Check if styles already exist
        if (document.getElementById('fg-stat-summary-styles')) {
            return;
        }
        
        // Create style element
        const style = document.createElement('style');
        style.id = 'fg-stat-summary-styles';
        style.textContent = `
            /* Stat Summary Styles */
            .fg-stat-summary {
                display: flex;
                flex-direction: column;
                gap: 15px;
                width: 100%;
            }
            
            .fg-stat-category {
                background-color: rgba(20, 20, 30, 0.5);
                border-radius: 6px;
                padding: 10px;
                border-left: 3px solid rgba(100, 100, 220, 0.6);
            }
            
            .fg-stat-category-title {
                margin: 0 0 10px 0;
                font-size: 16px;
                color: #eee;
                border-bottom: 1px solid rgba(100, 100, 220, 0.3);
                padding-bottom: 5px;
            }
            
            .fg-stat-list {
                display: flex;
                flex-direction: column;
                gap: 5px;
            }
            
            .fg-stat-item {
                display: flex;
                justify-content: space-between;
                align-items: center;
                padding: 4px 8px;
                border-radius: 4px;
                background-color: rgba(40, 40, 50, 0.4);
            }
            
            .fg-stat-info {
                display: flex;
                align-items: center;
                gap: 8px;
            }
            
            .fg-stat-icon {
                width: 20px;
                height: 20px;
            }
            
            .fg-stat-name {
                color: #d0d0d0;
                font-size: 14px;
            }
            
            .fg-stat-value {
                color: #88ff88;
                font-weight: bold;
                font-size: 14px;
            }
            
            /* Category-specific colors */
            .fg-stat-category:nth-child(1) {
                border-left-color: rgba(220, 100, 100, 0.6);
            }
            
            .fg-stat-category:nth-child(1) .fg-stat-category-title {
                border-bottom-color: rgba(220, 100, 100, 0.3);
            }
            
            .fg-stat-category:nth-child(2) {
                border-left-color: rgba(100, 180, 100, 0.6);
            }
            
            .fg-stat-category:nth-child(2) .fg-stat-category-title {
                border-bottom-color: rgba(100, 180, 100, 0.3);
            }
            
            .fg-stat-category:nth-child(3) {
                border-left-color: rgba(100, 120, 220, 0.6);
            }
            
            .fg-stat-category:nth-child(3) .fg-stat-category-title {
                border-bottom-color: rgba(100, 120, 220, 0.3);
            }
            
            .no-stats {
                color: #aaa;
                font-style: italic;
                text-align: center;
                padding: 10px;
            }
        `;
        
        // Add to document
        document.head.appendChild(style);
    }
};

// Add styles when document is ready
document.addEventListener('DOMContentLoaded', function() {
    StatIntegrationService.addSummaryStyles();
}); 