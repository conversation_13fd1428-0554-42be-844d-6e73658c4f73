/**
 * Essence Rune Data
 * Contains all definitions for essence runes
 */

// Export the rune definitions for use in the essence-runes-system.js
window.EssenceRuneData = {
    // Define runes with their variants for better organization
    runeDefinitions: {
        hp: {
            baseStatType: 'hp',
            iconId: 'hp', // Use this to resolve icon from StatsConfig
            variants: [
                {
                    id: 'hp',
                    name: 'HP I',
                    tier: 1,
                    maxLevel: 20,
                    valuePerLevel: [50, 100, 150, 200, 250, 300, 350, 400, 450, 500, 550, 600, 650, 700, 750, 800, 850, 900, 950, 1000],
                    apCost: [3, 4, 5, 7, 9, 12, 15, 19, 23, 28, 33, 39, 45, 52, 59, 67, 76, 86, 97, 109],
                    location: "Lakeside, Tower of the Dead, Essence Rune Cube (DP)",
                    materials: [
                        { level: 1, name: null, quantity: 0 },
                        { level: 2, name: "Force Core (Highest)", quantity: 1 },
                        { level: 3, name: "Force Core (Highest)", quantity: 1 },
                        { level: 4, name: "Force Core (Highest)", quantity: 1 },
                        { level: 5, name: "Force Core (Highest)", quantity: 1 },
                        { level: 6, name: "Force Core (Highest)", quantity: 1 },
                        { level: 7, name: "Force Core (Highest)", quantity: 1 },
                        { level: 8, name: "Force Core (Highest)", quantity: 2 },
                        { level: 9, name: "Force Core (Highest)", quantity: 2 },
                        { level: 10, name: "Force Core (Highest)", quantity: 2 },
                        { level: 11, name: "Force Core (Highest)", quantity: 2 },
                        { level: 12, name: "Force Core (Highest)", quantity: 2 },
                        { level: 13, name: "Force Core (Highest)", quantity: 3 },
                        { level: 14, name: "Force Core (Highest)", quantity: 3 },
                        { level: 15, name: "Force Core (Highest)", quantity: 3 },
                        { level: 16, name: "Force Core (Highest)", quantity: 4 },
                        { level: 17, name: "Force Core (Highest)", quantity: 4 },
                        { level: 18, name: "Force Core (Highest)", quantity: 5 },
                        { level: 19, name: "Force Core (Highest)", quantity: 5 },
                        { level: 20, name: "Force Core (Highest)", quantity: 6 }
                    ]
                },
                {
                    id: 'hp2',
                    name: 'HP II',
                    tier: 2,
                    maxLevel: 20,
                    valuePerLevel: [100, 200, 300, 400, 500, 600, 700, 800, 900, 1000, 1100, 1200, 1300, 1400, 1500, 1600, 1700, 1800, 1900, 2000],
                    apCost: [20, 50, 80, 110, 140, 170, 200, 230, 260, 290, 320, 350, 380, 410, 440, 470, 500, 530, 560, 590],
                    location: "NPC Chloe's Request with material from Labyrinth",
                    materials: [
                        { level: 1, name: null, quantity: 0 },
                        { level: 2, name: "Divine Stone", quantity: 2 },
                        { level: 3, name: "Divine Stone", quantity: 2 },
                        { level: 4, name: "Divine Stone", quantity: 2 },
                        { level: 5, name: "Divine Stone", quantity: 2 },
                        { level: 6, name: "Divine Stone", quantity: 2 },
                        { level: 7, name: "Divine Stone", quantity: 2 },
                        { level: 8, name: "Divine Stone", quantity: 2 },
                        { level: 9, name: "Divine Stone", quantity: 2 },
                        { level: 10, name: "Divine Stone", quantity: 2 },
                        { level: 11, name: "Divine Stone", quantity: 4 },
                        { level: 12, name: "Divine Stone", quantity: 6 },
                        { level: 13, name: "Divine Stone", quantity: 8 },
                        { level: 14, name: "Divine Stone", quantity: 10 },
                        { level: 15, name: "Divine Stone", quantity: 14 },
                        { level: 16, name: "Divine Stone", quantity: 14 },
                        { level: 17, name: "Divine Stone", quantity: 14 },
                        { level: 18, name: "Divine Stone", quantity: 14 },
                        { level: 19, name: "Divine Stone", quantity: 14 },
                        { level: 20, name: "Divine Stone", quantity: 20 }
                    ]
                },
                {
                    id: 'hp3',
                    name: 'HP III',
                    tier: 3,
                    maxLevel: 30,
                    valuePerLevel: [10, 27, 44, 61, 78, 95, 112, 129, 146, 165, 182, 199, 216, 233, 250, 267, 284, 301, 318, 335, 352, 369, 386, 403, 420, 437, 454, 471, 488, 500],
                    apCost: [40, 65, 90, 115, 140, 165, 190, 215, 240, 265, 290, 315, 340, 365, 390, 415, 440, 465, 490, 515, 533, 551, 569, 587, 605, 623, 641, 659, 677, 695],
                    location: "Purifier in the Woods",
                    materials: [
                        { level: 1, name: null, quantity: 0 },
                        { level: 2, name: "Divine Stone", quantity: 2 },
                        { level: 3, name: "Divine Stone", quantity: 2 },
                        { level: 4, name: "Divine Stone", quantity: 2 },
                        { level: 5, name: "Divine Stone", quantity: 2 },
                        { level: 6, name: "Divine Stone", quantity: 4 },
                        { level: 7, name: "Divine Stone", quantity: 4 },
                        { level: 8, name: "Divine Stone", quantity: 4 },
                        { level: 9, name: "Divine Stone", quantity: 4 },
                        { level: 10, name: "Divine Stone", quantity: 4 },
                        { level: 11, name: "Divine Stone", quantity: 6 },
                        { level: 12, name: "Divine Stone", quantity: 6 },
                        { level: 13, name: "Divine Stone", quantity: 6 },
                        { level: 14, name: "Divine Stone", quantity: 6 },
                        { level: 15, name: "Divine Stone", quantity: 6 },
                        { level: 16, name: "Divine Stone", quantity: 8 },
                        { level: 17, name: "Divine Stone", quantity: 8 },
                        { level: 18, name: "Divine Stone", quantity: 8 },
                        { level: 19, name: "Divine Stone", quantity: 8 },
                        { level: 20, name: "Divine Stone", quantity: 8 },
                        { level: 21, name: "Divine Stone", quantity: 10 },
                        { level: 22, name: "Divine Stone", quantity: 10 },
                        { level: 23, name: "Divine Stone", quantity: 10 },
                        { level: 24, name: "Divine Stone", quantity: 10 },
                        { level: 25, name: "Divine Stone", quantity: 10 },
                        { level: 26, name: "Divine Stone", quantity: 12 },
                        { level: 27, name: "Divine Stone", quantity: 12 },
                        { level: 28, name: "Divine Stone", quantity: 12 },
                        { level: 29, name: "Divine Stone", quantity: 12 },
                        { level: 30, name: "Divine Stone", quantity: 12 }
                    ]
                }
            ]
        },
        defenseRate: {
            baseStatType: 'defenseRate',
            iconId: 'defenseRate', // Use this to resolve icon from StatsConfig
            variants: [
                {
                    id: 'defenseRate',
                    name: 'Defense Rate I',
                    tier: 1,
                    maxLevel: 20,
                    valuePerLevel: [15, 30, 45, 60, 75, 90, 105, 120, 135, 150, 165, 180, 195, 210, 225, 240, 255, 270, 285, 300],
                    apCost: [3, 4, 5, 7, 9, 12, 15, 19, 23, 28, 33, 39, 45, 52, 59, 67, 76, 86, 97, 109],
                    location: "Lakeside, Forbidden Island, Tower of the Dead B3F, Forbidden Island (Awakening), Essence Rune Cube (DP), Officer's Support Cube (WExp)",
                    materials: [
                        { level: 1, name: null, quantity: 0 },
                        { level: 2, name: "Force Core(Highest)", quantity: 1 },
                        { level: 3, name: "Force Core(Highest)", quantity: 1 },
                        { level: 4, name: "Force Core(Highest)", quantity: 1 },
                        { level: 5, name: "Force Core(Highest)", quantity: 1 },
                        { level: 6, name: "Force Core(Highest)", quantity: 1 },
                        { level: 7, name: "Force Core(Highest)", quantity: 1 },
                        { level: 8, name: "Force Core(Highest)", quantity: 2 },
                        { level: 9, name: "Force Core(Highest)", quantity: 2 },
                        { level: 10, name: "Force Core(Highest)", quantity: 2 },
                        { level: 11, name: "Force Core(Highest)", quantity: 2 },
                        { level: 12, name: "Force Core(Highest)", quantity: 2 },
                        { level: 13, name: "Force Core(Highest)", quantity: 3 },
                        { level: 14, name: "Force Core(Highest)", quantity: 3 },
                        { level: 15, name: "Force Core(Highest)", quantity: 3 },
                        { level: 16, name: "Force Core(Highest)", quantity: 4 },
                        { level: 17, name: "Force Core(Highest)", quantity: 4 },
                        { level: 18, name: "Force Core(Highest)", quantity: 5 },
                        { level: 19, name: "Force Core(Highest)", quantity: 5 },
                        { level: 20, name: "Force Core(Highest)", quantity: 6 }
                    ]
                },
                {
                    id: 'defenseRate2',
                    name: 'Defense Rate II',
                    tier: 2,
                    maxLevel: 20,
                    valuePerLevel: [15, 30, 45, 60, 75, 90, 105, 120, 135, 150, 165, 180, 195, 210, 225, 240, 255, 270, 285, 300],
                    apCost: [3, 4, 5, 7, 9, 12, 15, 19, 23, 28, 33, 39, 45, 52, 59, 67, 76, 86, 97, 109],
                    location: "Exchange Shop at Secret Dealer Hiroalev",
                    materials: [
                        { level: 1, name: null, quantity: 0 },
                        { level: 2, name: "Troglo's Golden Fruit", quantity: 5 },
                        { level: 3, name: "Troglo's Golden Fruit", quantity: 10 },
                        { level: 4, name: "Troglo's Golden Fruit", quantity: 15 },
                        { level: 5, name: "Troglo's Golden Fruit", quantity: 20 },
                        { level: 6, name: "Troglo's Golden Fruit", quantity: 25 },
                        { level: 7, name: "Troglo's Golden Fruit", quantity: 30 },
                        { level: 8, name: "Troglo's Golden Fruit", quantity: 35 },
                        { level: 9, name: "Troglo's Golden Fruit", quantity: 40 },
                        { level: 10, name: "Troglo's Golden Fruit", quantity: 45 },
                        { level: 11, name: "Troglo's Golden Fruit", quantity: 50 },
                        { level: 12, name: "Troglo's Golden Fruit", quantity: 55 },
                        { level: 13, name: "Troglo's Golden Fruit", quantity: 60 },
                        { level: 14, name: "Troglo's Golden Fruit", quantity: 65 },
                        { level: 15, name: "Troglo's Golden Fruit", quantity: 70 },
                        { level: 16, name: "Troglo's Golden Fruit", quantity: 75 },
                        { level: 17, name: "Troglo's Golden Fruit", quantity: 80 },
                        { level: 18, name: "Troglo's Golden Fruit", quantity: 85 },
                        { level: 19, name: "Troglo's Golden Fruit", quantity: 90 },
                        { level: 20, name: "Troglo's Golden Fruit", quantity: 95 }
                    ]
                }
            ]
        },
        attackRate: {
            baseStatType: 'attackRate',
            iconId: 'attackRate', // Use this to resolve icon from StatsConfig
            variants: [
                {
                    id: 'attackRate',
                    name: 'Attack Rate I',
                    tier: 1,
                    maxLevel: 20,
                    valuePerLevel: [15, 30, 45, 60, 75, 90, 105, 120, 135, 150, 165, 180, 195, 210, 225, 240, 255, 270, 285, 300],
                    apCost: [3, 4, 5, 7, 9, 12, 15, 19, 23, 28, 33, 39, 45, 52, 59, 67, 76, 86, 97, 109],
                    location: "Lakeside, Forbidden Island (Awakening), Essence Rune Cube (DP), Officer's Support Cube (WExp)",
                    materials: [
                        { level: 1, name: null, quantity: 0 },
                        { level: 2, name: "Force Core(Highest)", quantity: 1 },
                        { level: 3, name: "Force Core(Highest)", quantity: 1 },
                        { level: 4, name: "Force Core(Highest)", quantity: 1 },
                        { level: 5, name: "Force Core(Highest)", quantity: 1 },
                        { level: 6, name: "Force Core(Highest)", quantity: 1 },
                        { level: 7, name: "Force Core(Highest)", quantity: 1 },
                        { level: 8, name: "Force Core(Highest)", quantity: 2 },
                        { level: 9, name: "Force Core(Highest)", quantity: 2 },
                        { level: 10, name: "Force Core(Highest)", quantity: 2 },
                        { level: 11, name: "Force Core(Highest)", quantity: 2 },
                        { level: 12, name: "Force Core(Highest)", quantity: 2 },
                        { level: 13, name: "Force Core(Highest)", quantity: 3 },
                        { level: 14, name: "Force Core(Highest)", quantity: 3 },
                        { level: 15, name: "Force Core(Highest)", quantity: 3 },
                        { level: 16, name: "Force Core(Highest)", quantity: 4 },
                        { level: 17, name: "Force Core(Highest)", quantity: 4 },
                        { level: 18, name: "Force Core(Highest)", quantity: 5 },
                        { level: 19, name: "Force Core(Highest)", quantity: 5 },
                        { level: 20, name: "Force Core(Highest)", quantity: 6 }
                    ]
                },
                {
                    id: 'attackRate2',
                    name: 'Attack Rate II',
                    tier: 2,
                    maxLevel: 20,
                    valuePerLevel: [15, 30, 45, 60, 75, 90, 105, 120, 135, 150, 165, 180, 195, 210, 225, 240, 255, 270, 285, 300],
                    apCost: [3, 4, 5, 7, 9, 12, 15, 19, 23, 28, 33, 39, 45, 52, 59, 67, 76, 86, 97, 109],
                    location: "Exchange Shop at Secret Dealer Hiroalev",
                    materials: [
                        { level: 1, name: null, quantity: 0 },
                        { level: 2, name: "Troglo's Golden Fruit", quantity: 5 },
                        { level: 3, name: "Troglo's Golden Fruit", quantity: 10 },
                        { level: 4, name: "Troglo's Golden Fruit", quantity: 15 },
                        { level: 5, name: "Troglo's Golden Fruit", quantity: 20 },
                        { level: 6, name: "Troglo's Golden Fruit", quantity: 25 },
                        { level: 7, name: "Troglo's Golden Fruit", quantity: 30 },
                        { level: 8, name: "Troglo's Golden Fruit", quantity: 35 },
                        { level: 9, name: "Troglo's Golden Fruit", quantity: 40 },
                        { level: 10, name: "Troglo's Golden Fruit", quantity: 45 },
                        { level: 11, name: "Troglo's Golden Fruit", quantity: 50 },
                        { level: 12, name: "Troglo's Golden Fruit", quantity: 55 },
                        { level: 13, name: "Troglo's Golden Fruit", quantity: 60 },
                        { level: 14, name: "Troglo's Golden Fruit", quantity: 65 },
                        { level: 15, name: "Troglo's Golden Fruit", quantity: 70 },
                        { level: 16, name: "Troglo's Golden Fruit", quantity: 75 },
                        { level: 17, name: "Troglo's Golden Fruit", quantity: 80 },
                        { level: 18, name: "Troglo's Golden Fruit", quantity: 85 },
                        { level: 19, name: "Troglo's Golden Fruit", quantity: 90 },
                        { level: 20, name: "Troglo's Golden Fruit", quantity: 95 }
                    ]
                }
            ]
        },
        critDamage: {
            baseStatType: 'critDamage',
            iconId: 'critDamage', // Use this to resolve icon from StatsConfig
            variants: [
                {
                    id: 'critDmg',
                    name: 'Critical DMG I',
                    tier: 1,
                    maxLevel: 10,
                    valuePerLevel: [5, 10, 15, 20, 25, 30, 35, 40, 45, 50],
                    apCost: [5, 14, 27, 45, 67, 94, 125, 163, 209, 264],
                    location: "Pontus Ferrum, Porta Inferno, Tower of the Dead B4F, Forbidden Island (Awakening)",
                    materials: [
                        { level: 1, name: null, quantity: 0 },
                        { level: 2, name: "Force Core (Highest)", quantity: 1 },
                        { level: 3, name: "Force Core (Highest)", quantity: 2 },
                        { level: 4, name: "Force Core (Highest)", quantity: 4 },
                        { level: 5, name: "Force Core (Highest)", quantity: 5 },
                        { level: 6, name: "Force Core (Highest)", quantity: 6 },
                        { level: 7, name: "Force Core (Highest)", quantity: 7 },
                        { level: 8, name: "Force Core (Highest)", quantity: 8 },
                        { level: 9, name: "Force Core (Highest)", quantity: 9 },
                        { level: 10, name: "Force Core (Highest)", quantity: 10 }
                    ]
                },
                {
                    id: 'critDmg2',
                    name: 'Critical DMG II',
                    tier: 2,
                    maxLevel: 9,
                    valuePerLevel: [10, 20, 30, 40, 50, 60, 70, 80, 90],
                    apCost: [450, 475, 500, 525, 550, 575, 600, 625, 650],
                    location: "Secret Base SCA-76",
                    materials: [
                        { level: 1, name: null, quantity: 0 },
                        { level: 2, name: "Divine Stone", quantity: 5 },
                        { level: 3, name: "Divine Stone", quantity: 5 },
                        { level: 4, name: "Divine Stone", quantity: 5 },
                        { level: 5, name: "Divine Stone", quantity: 10 },
                        { level: 6, name: "Divine Stone", quantity: 10 },
                        { level: 7, name: "Divine Stone", quantity: 10 },
                        { level: 8, name: "Divine Stone", quantity: 15 },
                        { level: 9, name: "Divine Stone", quantity: 20 }
                    ]
                }
            ]
        },
        defense: {
            baseStatType: 'defense',
            iconId: 'defense',
            variants: [
                {
                    id: 'defense',
                    name: 'Defense I',
                    tier: 1,
                    maxLevel: 20,
                    valuePerLevel: [5, 10, 15, 20, 25, 30, 35, 40, 45, 50, 55, 60, 65, 70, 75, 80, 85, 90, 95, 100],
                    apCost: [3, 4, 5, 7, 9, 12, 15, 19, 23, 28, 33, 39, 45, 52, 59, 67, 76, 86, 97, 109],
                    location: "Lakeside, Forbidden Island, Tower of the Dead B3F",
                    materials: [
                        { level: 1, name: null, quantity: 0 },
                        { level: 2, name: "Force Core(Highest)", quantity: 1 },
                        { level: 3, name: "Force Core(Highest)", quantity: 1 },
                        { level: 4, name: "Force Core(Highest)", quantity: 1 },
                        { level: 5, name: "Force Core(Highest)", quantity: 1 },
                        { level: 6, name: "Force Core(Highest)", quantity: 1 },
                        { level: 7, name: "Force Core(Highest)", quantity: 1 },
                        { level: 8, name: "Force Core(Highest)", quantity: 2 },
                        { level: 9, name: "Force Core(Highest)", quantity: 2 },
                        { level: 10, name: "Force Core(Highest)", quantity: 2 },
                        { level: 11, name: "Force Core(Highest)", quantity: 2 },
                        { level: 12, name: "Force Core(Highest)", quantity: 2 },
                        { level: 13, name: "Force Core(Highest)", quantity: 3 },
                        { level: 14, name: "Force Core(Highest)", quantity: 3 },
                        { level: 15, name: "Force Core(Highest)", quantity: 3 },
                        { level: 16, name: "Force Core(Highest)", quantity: 4 },
                        { level: 17, name: "Force Core(Highest)", quantity: 4 },
                        { level: 18, name: "Force Core(Highest)", quantity: 5 },
                        { level: 19, name: "Force Core(Highest)", quantity: 5 },
                        { level: 20, name: "Force Core(Highest)", quantity: 6 }
                    ]
                },
                {
                    id: 'defense2',
                    name: 'Defense II',
                    tier: 2,
                    maxLevel: 20,
                    valuePerLevel: [3, 6, 9, 12, 15, 18, 21, 24, 27, 30, 33, 36, 39, 42, 45, 48, 51, 54, 57, 60],
                    apCost: [20, 50, 80, 110, 140, 170, 200, 230, 260, 290, 320, 350, 380, 410, 440, 470, 500, 530, 560, 590],
                    location: "Illussion Castle: Radiant Hall (Apocrypha)",
                    materials: [
                        { level: 1, name: null, quantity: 0 },
                        { level: 2, name: "Divine Stone", quantity: 1 },
                        { level: 3, name: "Divine Stone", quantity: 1 },
                        { level: 4, name: "Divine Stone", quantity: 1 },
                        { level: 5, name: "Divine Stone", quantity: 1 },
                        { level: 6, name: "Divine Stone", quantity: 1 },
                        { level: 7, name: "Divine Stone", quantity: 1 },
                        { level: 8, name: "Divine Stone", quantity: 1 },
                        { level: 9, name: "Divine Stone", quantity: 1 },
                        { level: 10, name: "Divine Stone", quantity: 1 },
                        { level: 11, name: "Divine Stone", quantity: 2 },
                        { level: 12, name: "Divine Stone", quantity: 3 },
                        { level: 13, name: "Divine Stone", quantity: 4 },
                        { level: 14, name: "Divine Stone", quantity: 5 },
                        { level: 15, name: "Divine Stone", quantity: 7 },
                        { level: 16, name: "Divine Stone", quantity: 7 },
                        { level: 17, name: "Divine Stone", quantity: 7 },
                        { level: 18, name: "Divine Stone", quantity: 7 },
                        { level: 19, name: "Divine Stone", quantity: 7 },
                        { level: 20, name: "Divine Stone", quantity: 10 }
                    ]
                }
            ]
        },
        exp: {
            baseStatType: 'exp',
            iconId: 'exp',
            variants: [
                {
                    id: 'exp',
                    name: 'EXP',
                    tier: 1,
                    maxLevel: 10,
                    valuePerLevel: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10], // Values in %
                    apCost: [1, 2, 3, 5, 7, 10, 13, 17, 21, 26],    // AP cost per level
                    location: 'Lakeside, Forbidden Island (Awakening), Essence Rune Cube (DP)',
                    materials: [
                        { level: 1, name: null, quantity: 0 },
                        { level: 2, name: "Force Core (High)", quantity: 1 }, 
                        { level: 3, name: "Force Core (High)", quantity: 1 }, 
                        { level: 4, name: "Force Core (High)", quantity: 1 }, 
                        { level: 5, name: "Force Core (High)", quantity: 1 }, 
                        { level: 6, name: "Force Core (High)", quantity: 2 }, 
                        { level: 7, name: "Force Core (High)", quantity: 2 }, 
                        { level: 8, name: "Force Core (High)", quantity: 2 }, 
                        { level: 9, name: "Force Core (High)", quantity: 2 }, 
                        { level: 10, name: "Force Core (High)", quantity: 3 }
                    ]
                }
                // Add EXP II variant here if it exists later
            ]
        },
        skillExp: {
            baseStatType: 'skillExp',
            iconId: 'skillExp',
            variants: [
                {
                    id: 'skillExp',
                    name: 'Skill EXP',
                    tier: 1,
                    maxLevel: 10,
                    valuePerLevel: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10], // Values in %
                    apCost: [1, 2, 3, 5, 7, 10, 13, 17, 21, 26],    // AP cost per level
                    location: 'Phoenix Nest, Forgotten Temple B2F, Forbidden Island',
                    materials: [
                        { level: 1, name: null, quantity: 0 }, 
                        { level: 2, name: "Force Core (High)", quantity: 1 }, 
                        { level: 3, name: "Force Core (High)", quantity: 1 }, 
                        { level: 4, name: "Force Core (High)", quantity: 1 }, 
                        { level: 5, name: "Force Core (High)", quantity: 1 }, 
                        { level: 6, name: "Force Core (High)", quantity: 2 }, 
                        { level: 7, name: "Force Core (High)", quantity: 2 }, 
                        { level: 8, name: "Force Core (High)", quantity: 2 }, 
                        { level: 9, name: "Force Core (High)", quantity: 2 }, 
                        { level: 10, name: "Force Core (High)", quantity: 3 }
                    ]
                }
            ]
        },
        partyExp: {
            baseStatType: 'partyExp',
            iconId: 'partyExp',
            variants: [
                {
                    id: 'partyExp',
                    name: 'Party EXP',
                    tier: 1,
                    maxLevel: 10,
                    valuePerLevel: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10], // Values in %
                    apCost: [1, 2, 3, 5, 7, 10, 13, 17, 21, 26],    // AP cost per level
                    location: 'Tower of the Dead B2F, Forgotten Temple B2F, Forbidden Island',
                    materials: [
                        { level: 1, name: null, quantity: 0 }, 
                        { level: 2, name: "Force Core (High)", quantity: 1 }, 
                        { level: 3, name: "Force Core (High)", quantity: 1 }, 
                        { level: 4, name: "Force Core (High)", quantity: 1 }, 
                        { level: 5, name: "Force Core (High)", quantity: 1 }, 
                        { level: 6, name: "Force Core (High)", quantity: 2 }, 
                        { level: 7, name: "Force Core (High)", quantity: 2 }, 
                        { level: 8, name: "Force Core (High)", quantity: 2 }, 
                        { level: 9, name: "Force Core (High)", quantity: 2 }, 
                        { level: 10, name: "Force Core (High)", quantity: 3 }
                    ]
                }
            ]
        },
        petExp: {
            baseStatType: 'petExp',
            iconId: 'petExp',
            variants: [
                {
                    id: 'petExp',
                    name: 'Pet EXP',
                    tier: 1,
                    maxLevel: 10,
                    valuePerLevel: [5, 10, 15, 20, 25, 30, 35, 40, 45, 50], // Values in %
                    apCost: [1, 2, 3, 5, 7, 10, 13, 17, 21, 26],    // AP cost per level
                    location: 'Volcanic Citadel, Forgotten Temple B2F, Forbidden Island',
                    materials: [
                        { level: 1, name: null, quantity: 0 }, 
                        { level: 2, name: "Force Core (High)", quantity: 1 }, 
                        { level: 3, name: "Force Core (High)", quantity: 1 }, 
                        { level: 4, name: "Force Core (High)", quantity: 1 }, 
                        { level: 5, name: "Force Core (High)", quantity: 1 }, 
                        { level: 6, name: "Force Core (High)", quantity: 2 }, 
                        { level: 7, name: "Force Core (High)", quantity: 2 }, 
                        { level: 8, name: "Force Core (High)", quantity: 2 }, 
                        { level: 9, name: "Force Core (High)", quantity: 2 }, 
                        { level: 10, name: "Force Core (High)", quantity: 3 }
                    ]
                }
            ]
        },
        alzDropAmount: {
            baseStatType: 'alzDropAmount',
            iconId: 'alzDropAmount',
            variants: [
                {
                    id: 'alzDropAmount',
                    name: 'Alz Drop Amount',
                    tier: 1,
                    maxLevel: 15,
                    valuePerLevel: [2, 4, 7, 9, 12, 14, 17, 19, 22, 24, 27, 29, 32, 34, 37], // Values in %
                    apCost: [2, 2, 3, 4, 6, 8, 10, 13, 16, 20, 24, 28, 33, 38, 44],          // AP cost per level
                    location: 'Pontus Ferrum, Porta Inferno, Arcane Trace',
                    materials: [
                        { level: 1, name: null, quantity: 0 }, 
                        { level: 2, name: "Force Core (High)", quantity: 1 }, 
                        { level: 3, name: "Force Core (High)", quantity: 1 }, 
                        { level: 4, name: "Force Core (High)", quantity: 1 }, 
                        { level: 5, name: "Force Core (High)", quantity: 1 }, 
                        { level: 6, name: "Force Core (High)", quantity: 1 }, 
                        { level: 7, name: "Force Core (High)", quantity: 1 }, 
                        { level: 8, name: "Force Core (High)", quantity: 2 }, 
                        { level: 9, name: "Force Core (High)", quantity: 2 }, 
                        { level: 10, name: "Force Core (High)", quantity: 2 }, 
                        { level: 11, name: "Force Core (High)", quantity: 2 }, 
                        { level: 12, name: "Force Core (High)", quantity: 2 }, 
                        { level: 13, name: "Force Core (High)", quantity: 3 }, 
                        { level: 14, name: "Force Core (High)", quantity: 3 }, 
                        { level: 15, name: "Force Core (High)", quantity: 3 }
                    ]
                }
            ]
        },
        alzDropRate: {
            baseStatType: 'alzDropRate',
            iconId: 'alzDropRate',
            variants: [
                {
                    id: 'alzDropRate',
                    name: 'Alz Drop Rate',
                    tier: 1,
                    maxLevel: 15,
                    valuePerLevel: [1, 2, 4, 5, 7, 8, 10, 11, 13, 14, 16, 17, 19, 20, 22], // Values in %
                    apCost: [2, 2, 3, 4, 6, 8, 10, 13, 16, 20, 24, 28, 33, 38, 44],        // AP cost per level
                    location: 'Unknown - Data missing from original runeProgression', // Add location if known
                    materials: [
                        { level: 1, name: null, quantity: 0 }, 
                        { level: 2, name: "Force Core (High)", quantity: 1 }, 
                        { level: 3, name: "Force Core (High)", quantity: 1 }, 
                        { level: 4, name: "Force Core (High)", quantity: 1 }, 
                        { level: 5, name: "Force Core (High)", quantity: 1 }, 
                        { level: 6, name: "Force Core (High)", quantity: 1 }, 
                        { level: 7, name: "Force Core (High)", quantity: 1 }, 
                        { level: 8, name: "Force Core (High)", quantity: 2 }, 
                        { level: 9, name: "Force Core (High)", quantity: 2 }, 
                        { level: 10, name: "Force Core (High)", quantity: 2 }, 
                        { level: 11, name: "Force Core (High)", quantity: 2 }, 
                        { level: 12, name: "Force Core (High)", quantity: 2 }, 
                        { level: 13, name: "Force Core (High)", quantity: 2 }, 
                        { level: 14, name: "Force Core (High)", quantity: 3 }, 
                        { level: 15, name: "Force Core (High)", quantity: 3 }
                    ]
                }
            ]
        },
        alzBombChance: {
            baseStatType: 'alzBombChance',
            iconId: 'alzBombChance',
            variants: [
                {
                    id: 'alzBombChance',
                    name: 'Alz Bomb Chance',
                    tier: 1,
                    maxLevel: 15,
                    valuePerLevel: [1, 2, 4, 5, 7, 8, 10, 11, 13, 15, 16, 17, 19, 20, 22], // Values in %
                    apCost: [2, 2, 3, 4, 6, 8, 10, 13, 16, 20, 24, 28, 33, 38, 44],        // AP cost per level
                    location: 'Unknown - Data missing from original runeProgression', // Add location if known
                    materials: [
                        { level: 1, name: null, quantity: 0 }, { level: 2, name: "Force Core (High)", quantity: 1 }, { level: 3, name: "Force Core (High)", quantity: 1 }, { level: 4, name: "Force Core (High)", quantity: 1 }, { level: 5, name: "Force Core (High)", quantity: 1 }, { level: 6, name: "Force Core (High)", quantity: 1 }, { level: 7, name: "Force Core (High)", quantity: 1 }, { level: 8, name: "Force Core (High)", quantity: 2 }, { level: 9, name: "Force Core (High)", quantity: 2 }, { level: 10, name: "Force Core (High)", quantity: 2 }, { level: 11, name: "Force Core (High)", quantity: 2 }, { level: 12, name: "Force Core (High)", quantity: 2 }, { level: 13, name: "Force Core (High)", quantity: 2 }, { level: 14, name: "Force Core (High)", quantity: 3 }, { level: 15, name: "Force Core (High)", quantity: 3 }
                    ]
                }
            ]
        },
        resistDown: { // Keep the key as downResist for backward compatibility
            baseStatType: 'resistDown', // But map it to the correct StatsConfig key
            iconId: 'resistDown', // And use the correct icon ID
            variants: [
                {
                    id: 'resistDown', // Keep the original ID for backward compatibility
                    name: 'Resist Down', // Update display name
                    tier: 1,
                    maxLevel: 10,
                    valuePerLevel: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10], // Values in %
                    apCost: [5, 14, 27, 45, 67, 94, 125, 163, 209, 264],        // AP cost per level
                    location: 'Unknown - Data missing from original runeProgression', // Add location if known
                    materials: [
                       { level: 1, name: null, quantity: 0 }, { level: 2, name: "Force Core (Highest)", quantity: 1 }, { level: 3, name: "Force Core (Highest)", quantity: 2 }, { level: 4, name: "Force Core (Highest)", quantity: 4 }, { level: 5, name: "Force Core (Highest)", quantity: 5 }, { level: 6, name: "Force Core (Highest)", quantity: 6 }, { level: 7, name: "Force Core (Highest)", quantity: 7 }, { level: 8, name: "Force Core (Highest)", quantity: 8 }, { level: 9, name: "Force Core (Highest)", quantity: 9 }, { level: 10, name: "Force Core (Highest)", quantity: 10 }
                    ]
                }
            ]
        },
        resistKnockBack: { // Keep the key for backward compatibility
            baseStatType: 'resistKnockback', // Map to correct StatsConfig key
            iconId: 'resistKnockback', // Use correct icon ID
            variants: [
                {
                    id: 'resistKnockBack', // Keep original ID for backward compatibility
                    name: 'Resist Knockback', // Update name
                    tier: 1,
                    maxLevel: 10,
                    valuePerLevel: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10], // Values in %
                    apCost: [5, 14, 27, 45, 67, 94, 125, 163, 209, 264],        // AP cost per level
                    location: 'Unknown - Data missing from original runeProgression', // Add location if known
                    materials: [
                        { level: 1, name: null, quantity: 0 }, { level: 2, name: "Force Core (Highest)", quantity: 1 }, { level: 3, name: "Force Core (Highest)", quantity: 2 }, { level: 4, name: "Force Core (Highest)", quantity: 4 }, { level: 5, name: "Force Core (Highest)", quantity: 5 }, { level: 6, name: "Force Core (Highest)", quantity: 6 }, { level: 7, name: "Force Core (Highest)", quantity: 7 }, { level: 8, name: "Force Core (Highest)", quantity: 8 }, { level: 9, name: "Force Core (Highest)", quantity: 9 }, { level: 10, name: "Force Core (Highest)", quantity: 10 }
                    ]
                }
            ]
        },
        ignorePenetration: { // Base stat type
            baseStatType: 'ignorePenetration',
            iconId: 'ignorePenetration',
            variants: [
                { // Data from ignorePenetration2 in runeProgression
                    id: 'ignorePenetration2',
                    name: 'Ignore Penetration II',
                    tier: 2,
                    maxLevel: 10,
                    valuePerLevel: [13, 21, 29, 37, 45, 53, 61, 69, 77, 85],
                    apCost: [450, 475, 500, 525, 550, 575, 600, 625, 650, 675],
                    location: "Celestia",
                    materials: [
                        { level: 1, name: null, quantity: 0 },
                        { level: 2, name: "Divine Stone", quantity: 5 },
                        { level: 3, name: "Divine Stone", quantity: 5 },
                        { level: 4, name: "Divine Stone", quantity: 5 },
                        { level: 5, name: "Divine Stone", quantity: 10 },
                        { level: 6, name: "Divine Stone", quantity: 10 },
                        { level: 7, name: "Divine Stone", quantity: 10 },
                        { level: 8, name: "Divine Stone", quantity: 15 },
                        { level: 9, name: "Divine Stone", quantity: 20 },
                        { level: 10, name: "Divine Stone", quantity: 20 }
                    ]
                }
            ]
        },
        penetration: {
            baseStatType: 'penetration',
            iconId: 'penetration',
            variants: [
                // Tier 1 data would go here if available
                { // Data from penetration2 in runeProgression
                    id: 'penetration2',
                    name: 'Penetration II',
                    tier: 2,
                    maxLevel: 9,
                    valuePerLevel: [5, 10, 15, 20, 25, 30, 35, 40, 45], // Assuming flat based on lack of '%'? Needs verification.
                    apCost: [450, 475, 500, 525, 550, 575, 600, 625, 650],
                    location: "Secret Base SCA-76",
                    materials: [
                         { level: 1, name: null, quantity: 0 }, { level: 2, name: "Divine Stone", quantity: 5 }, { level: 3, name: "Divine Stone", quantity: 5 }, { level: 4, name: "Divine Stone", quantity: 5 }, { level: 5, name: "Divine Stone", quantity: 10 }, { level: 6, name: "Divine Stone", quantity: 10 }, { level: 7, name: "Divine Stone", quantity: 10 }, { level: 8, name: "Divine Stone", quantity: 15 }, { level: 9, name: "Divine Stone", quantity: 20 }
                    ]
                }
            ]
        },
        int: { // Base stat type
            baseStatType: 'int', // Or 'intelligence' depending on StatsConfig
            iconId: 'int',
            variants: [
                // Tier 1 data would go here if available
                { // Data from int2 in runeProgression
                    id: 'int2',
                    name: 'INT II',
                    tier: 2,
                    maxLevel: 20,
                    valuePerLevel: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20],
                    apCost: [20, 50, 80, 110, 140, 170, 200, 230, 260, 290, 320, 350, 380, 410, 440, 470, 500, 530, 560, 590],
                    location: "NPC Chloe's Request with material from Labyrinth",
                    materials: [
                       { level: 1, name: null, quantity: 0 }, 
                       { level: 2, name: "Divine Stone", quantity: 2 }, 
                       { level: 3, name: "Divine Stone", quantity: 2 }, 
                       { level: 4, name: "Divine Stone", quantity: 2 }, 
                       { level: 5, name: "Divine Stone", quantity: 2 }, 
                       { level: 6, name: "Divine Stone", quantity: 2 }, 
                       { level: 7, name: "Divine Stone", quantity: 2 }, 
                       { level: 8, name: "Divine Stone", quantity: 2 }, 
                       { level: 9, name: "Divine Stone", quantity: 2 }, 
                       { level: 10, name: "Divine Stone", quantity: 2 }, 
                       { level: 11, name: "Divine Stone", quantity: 4 }, 
                       { level: 12, name: "Divine Stone", quantity: 6 }, 
                       { level: 13, name: "Divine Stone", quantity: 8 }, 
                       { level: 14, name: "Divine Stone", quantity: 10 }, 
                       { level: 15, name: "Divine Stone", quantity: 14 }, 
                       { level: 16, name: "Divine Stone", quantity: 14 }, 
                       { level: 17, name: "Divine Stone", quantity: 14 }, 
                       { level: 18, name: "Divine Stone", quantity: 14 }, 
                       { level: 19, name: "Divine Stone", quantity: 14 }, 
                       { level: 20, name: "Divine Stone", quantity: 20 }
                    ]
                },
                { // Data from int2 in runeProgression
                    id: 'int',
                    name: 'INT I',
                    tier: 1,
                    maxLevel: 20,
                    valuePerLevel: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20],
                    apCost: [20, 50, 80, 110, 140, 170, 200, 230, 260, 290, 320, 350, 380, 410, 440, 470, 500, 530, 560, 590],
                    location: "NPC Chloe's Request with material from Labyrinth",
                    materials: [
                       { level: 1, name: null, quantity: 0 }, 
                       { level: 2, name: "Divine Stone", quantity: 2 }, 
                       { level: 3, name: "Divine Stone", quantity: 2 }, 
                       { level: 4, name: "Divine Stone", quantity: 2 }, 
                       { level: 5, name: "Divine Stone", quantity: 2 }, 
                       { level: 6, name: "Divine Stone", quantity: 2 }, 
                       { level: 7, name: "Divine Stone", quantity: 2 }, 
                       { level: 8, name: "Divine Stone", quantity: 2 }, 
                       { level: 9, name: "Divine Stone", quantity: 2 }, 
                       { level: 10, name: "Divine Stone", quantity: 2 }, 
                       { level: 11, name: "Divine Stone", quantity: 4 }, 
                       { level: 12, name: "Divine Stone", quantity: 6 }, 
                       { level: 13, name: "Divine Stone", quantity: 8 }, 
                       { level: 14, name: "Divine Stone", quantity: 10 }, 
                       { level: 15, name: "Divine Stone", quantity: 14 }, 
                       { level: 16, name: "Divine Stone", quantity: 14 }, 
                       { level: 17, name: "Divine Stone", quantity: 14 }, 
                       { level: 18, name: "Divine Stone", quantity: 14 }, 
                       { level: 19, name: "Divine Stone", quantity: 14 }, 
                       { level: 20, name: "Divine Stone", quantity: 20 }
                    ]
                }

            ]
        },
        damageReduction: { // Keep the key for backward compatibility
            baseStatType: 'damageReduce', // Map to correct StatsConfig key
            iconId: 'damageReduce', // Use correct icon ID
            variants: [
                 // Tier 1 data would go here if available
                { // Data from dmgReduction2 in runeProgression
                    id: 'dmgReduction2',
                    name: 'DMG Reduction II',
                    tier: 2,
                    maxLevel: 5,
                    valuePerLevel: [12, 24, 36, 48, 60], // Assuming flat based on lack of '%'? Needs verification.
                    apCost: [310, 312, 315, 320, 325],
                    location: "Garden of Dust",
                    materials: [
                        { level: 1, name: null, quantity: 0 }, 
                        { level: 2, name: "Divine Stone", quantity: 5 }, 
                        { level: 3, name: "Divine Stone", quantity: 10 }, 
                        { level: 4, name: "Divine Stone", quantity: 15 }, 
                        { level: 5, name: "Divine Stone", quantity: 20 }
                    ]
                },
                { // Data from dmgReduction2 in runeProgression
                    id: 'dmgReduction',
                    name: 'DMG Reduction I',
                    tier: 1,
                    maxLevel: 5,
                    valuePerLevel: [12, 24, 36, 48, 60], // Assuming flat based on lack of '%'? Needs verification.
                    apCost: [310, 312, 315, 320, 325],
                    location: "Garden of Dust",
                    materials: [
                        { level: 1, name: null, quantity: 0 }, 
                        { level: 2, name: "Divine Stone", quantity: 5 }, 
                        { level: 3, name: "Divine Stone", quantity: 10 }, 
                        { level: 4, name: "Divine Stone", quantity: 15 }, 
                        { level: 5, name: "Divine Stone", quantity: 20 }
                    ]
                }

            ]
        },
        dex: { // Base stat type
            baseStatType: 'dex', // Or 'dexterity' depending on StatsConfig
            iconId: 'dex',
            variants: [
                // Tier 1 data would go here if available
                { // Data from dex2 in runeProgression
                    id: 'dex2',
                    name: 'DEX II',
                    tier: 2,
                    maxLevel: 20,
                    valuePerLevel: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20],
                    apCost: [20, 50, 80, 110, 140, 170, 200, 230, 260, 290, 320, 350, 380, 410, 440, 470, 500, 530, 560, 590],
                    location: "NPC Chloe's Request with material from Labyrinth",
                    materials: [
                        { level: 1, name: null, quantity: 0 }, 
                        { level: 2, name: "Divine Stone", quantity: 2 }, 
                        { level: 3, name: "Divine Stone", quantity: 2 }, 
                        { level: 4, name: "Divine Stone", quantity: 2 }, 
                        { level: 5, name: "Divine Stone", quantity: 2 }, 
                        { level: 6, name: "Divine Stone", quantity: 2 }, 
                        { level: 7, name: "Divine Stone", quantity: 2 }, 
                        { level: 8, name: "Divine Stone", quantity: 2 }, 
                        { level: 9, name: "Divine Stone", quantity: 2 }, 
                        { level: 10, name: "Divine Stone", quantity: 2 }, 
                        { level: 11, name: "Divine Stone", quantity: 4 }, 
                        { level: 12, name: "Divine Stone", quantity: 6 }, 
                        { level: 13, name: "Divine Stone", quantity: 8 }, { level: 14, name: "Divine Stone", quantity: 10 }, { level: 15, name: "Divine Stone", quantity: 14 }, { level: 16, name: "Divine Stone", quantity: 14 }, { level: 17, name: "Divine Stone", quantity: 14 }, { level: 18, name: "Divine Stone", quantity: 14 }, { level: 19, name: "Divine Stone", quantity: 14 }, { level: 20, name: "Divine Stone", quantity: 20 }
                    ]
                },
                { // Data from dex2 in runeProgression
                    id: 'dex',
                    name: 'DEX I',
                    tier: 1,
                    maxLevel: 20,
                    valuePerLevel: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20],
                    apCost: [20, 50, 80, 110, 140, 170, 200, 230, 260, 290, 320, 350, 380, 410, 440, 470, 500, 530, 560, 590],
                    location: "NPC Chloe's Request with material from Labyrinth",
                    materials: [
                        { level: 1, name: null, quantity: 0 }, 
                        { level: 2, name: "Divine Stone", quantity: 2 }, 
                        { level: 3, name: "Divine Stone", quantity: 2 }, 
                        { level: 4, name: "Divine Stone", quantity: 2 }, 
                        { level: 5, name: "Divine Stone", quantity: 2 }, 
                        { level: 6, name: "Divine Stone", quantity: 2 }, 
                        { level: 7, name: "Divine Stone", quantity: 2 }, 
                        { level: 8, name: "Divine Stone", quantity: 2 }, 
                        { level: 9, name: "Divine Stone", quantity: 2 }, 
                        { level: 10, name: "Divine Stone", quantity: 2 }, 
                        { level: 11, name: "Divine Stone", quantity: 4 }, 
                        { level: 12, name: "Divine Stone", quantity: 6 }, 
                        { level: 13, name: "Divine Stone", quantity: 8 }, 
                        { level: 14, name: "Divine Stone", quantity: 10 }, 
                        { level: 15, name: "Divine Stone", quantity: 14 }, 
                        { level: 16, name: "Divine Stone", quantity: 14 }, 
                        { level: 17, name: "Divine Stone", quantity: 14 }, 
                        { level: 18, name: "Divine Stone", quantity: 14 }, 
                        { level: 19, name: "Divine Stone", quantity: 14 }, 
                        { level: 20, name: "Divine Stone", quantity: 20 }
                    ]
                },

            ]
        },
        str: { // Base stat type
            baseStatType: 'str', // Or 'strength' depending on StatsConfig
            iconId: 'str',
            variants: [
                // Tier 1 data would go here if available
                { // Data from str2 in runeProgression
                    id: 'str2',
                    name: 'STR II',
                    tier: 2,
                    maxLevel: 20,
                    valuePerLevel: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20],
                    apCost: [20, 50, 80, 110, 140, 170, 200, 230, 260, 290, 320, 350, 380, 410, 440, 470, 500, 530, 560, 590],
                    location: "NPC Chloe's Request with material from Labyrinth",
                    materials: [
                        { level: 1, name: null, quantity: 0 }, 
                        { level: 2, name: "Divine Stone", quantity: 2 }, 
                        { level: 3, name: "Divine Stone", quantity: 2 }, 
                        { level: 4, name: "Divine Stone", quantity: 2 }, 
                        { level: 5, name: "Divine Stone", quantity: 2 }, 
                        { level: 6, name: "Divine Stone", quantity: 2 }, 
                        { level: 7, name: "Divine Stone", quantity: 2 }, 
                        { level: 8, name: "Divine Stone", quantity: 2 }, 
                        { level: 9, name: "Divine Stone", quantity: 2 }, 
                        { level: 10, name: "Divine Stone", quantity: 2 }, 
                        { level: 11, name: "Divine Stone", quantity: 4 }, 
                        { level: 12, name: "Divine Stone", quantity: 6 }, 
                        { level: 13, name: "Divine Stone", quantity: 8 }, 
                        { level: 14, name: "Divine Stone", quantity: 10 }, 
                        { level: 15, name: "Divine Stone", quantity: 14 }, 
                        { level: 16, name: "Divine Stone", quantity: 14 }, 
                        { level: 17, name: "Divine Stone", quantity: 14 }, 
                        { level: 18, name: "Divine Stone", quantity: 14 }, 
                        { level: 19, name: "Divine Stone", quantity: 14 }, 
                        { level: 20, name: "Divine Stone", quantity: 20 }
                    ]
                },
                { // Data from str2 in runeProgression
                    id: 'str',
                    name: 'STR I',
                    tier: 1,
                    maxLevel: 20,
                    valuePerLevel: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20],
                    apCost: [20, 50, 80, 110, 140, 170, 200, 230, 260, 290, 320, 350, 380, 410, 440, 470, 500, 530, 560, 590],
                    location: "NPC Chloe's Request with material from Labyrinth",
                    materials: [
                        { level: 1, name: null, quantity: 0 }, 
                        { level: 2, name: "Divine Stone", quantity: 2 }, 
                        { level: 3, name: "Divine Stone", quantity: 2 }, 
                        { level: 4, name: "Divine Stone", quantity: 2 }, 
                        { level: 5, name: "Divine Stone", quantity: 2 }, 
                        { level: 6, name: "Divine Stone", quantity: 2 }, 
                        { level: 7, name: "Divine Stone", quantity: 2 }, 
                        { level: 8, name: "Divine Stone", quantity: 2 }, 
                        { level: 9, name: "Divine Stone", quantity: 2 }, 
                        { level: 10, name: "Divine Stone", quantity: 2 }, 
                        { level: 11, name: "Divine Stone", quantity: 4 }, 
                        { level: 12, name: "Divine Stone", quantity: 6 }, 
                        { level: 13, name: "Divine Stone", quantity: 8 }, 
                        { level: 14, name: "Divine Stone", quantity: 10 }, 
                        { level: 15, name: "Divine Stone", quantity: 14 }, 
                        { level: 16, name: "Divine Stone", quantity: 14 }, 
                        { level: 17, name: "Divine Stone", quantity: 14 }, 
                        { level: 18, name: "Divine Stone", quantity: 14 }, 
                        { level: 19, name: "Divine Stone", quantity: 14 }, 
                        { level: 20, name: "Divine Stone", quantity: 20 }
                    ]
                }
            ]
        },
        magicSkillAmp: {
            baseStatType: 'magicSkillAmp',
            iconId: 'magicSkillAmp',
            variants: [
                {
                    id: 'magicSkillAmp2',
                    name: 'Magic Skill Amp II',
                    tier: 2,
                    maxLevel: 5,
                    valuePerLevel: [1, 2, 3, 4, 5],
                    apCost: [300, 350, 400, 450, 500],
                    location: "Garden of Dust (Secret Chest)",
                    materials: [
                        { level: 1, name: null, quantity: 0 },
                        { level: 2, name: "Divine Stone", quantity: 5 },
                        { level: 3, name: "Divine Stone", quantity: 10 },
                        { level: 4, name: "Divine Stone", quantity: 15 },
                        { level: 5, name: "Divine Stone", quantity: 20 }
                    ]
                }
            ]
        },
        swordSkillAmp: {
            baseStatType: 'swordSkillAmp',
            iconId: 'swordSkillAmp',
            variants: [
                {
                    id: 'swordSkillAmp2',
                    name: 'Sword Skill Amp II',
                    tier: 2,
                    maxLevel: 5,
                    valuePerLevel: [1, 2, 3, 4, 5],
                    apCost: [300, 350, 400, 450, 500],
                    location: "Garden of Dust (Secret Chest)",
                    materials: [
                        { level: 1, name: null, quantity: 0 },
                        { level: 2, name: "Divine Stone", quantity: 5 },
                        { level: 3, name: "Divine Stone", quantity: 10 },
                        { level: 4, name: "Divine Stone", quantity: 15 },
                        { level: 5, name: "Divine Stone", quantity: 20 }
                    ]
                }
            ]
        },
        magicAttack: {
            baseStatType: 'magicAttack',
            iconId: 'magicAttack',
            variants: [
                {
                    id: 'magicAttack2',
                    name: 'Magic Attack II',
                    tier: 2,
                    maxLevel: 20,
                    valuePerLevel: [2, 4, 6, 8, 10, 12, 14, 16, 18, 20, 22, 24, 26, 28, 30, 32, 34, 36, 38, 40],
                    apCost: [20, 50, 80, 110, 140, 170, 200, 230, 260, 290, 320, 350, 380, 410, 440, 470, 500, 530, 560, 590],
                    location: "Mirage Island",
                    materials: [
                        { level: 1, name: null, quantity: 0 },
                        { level: 2, name: "Divine Stone", quantity: 1 },
                        { level: 3, name: "Divine Stone", quantity: 1 },
                        { level: 4, name: "Divine Stone", quantity: 1 },
                        { level: 5, name: "Divine Stone", quantity: 1 },
                        { level: 6, name: "Divine Stone", quantity: 1 },
                        { level: 7, name: "Divine Stone", quantity: 1 },
                        { level: 8, name: "Divine Stone", quantity: 1 },
                        { level: 9, name: "Divine Stone", quantity: 1 },
                        { level: 10, name: "Divine Stone", quantity: 1 },
                        { level: 11, name: "Divine Stone", quantity: 2 },
                        { level: 12, name: "Divine Stone", quantity: 3 },
                        { level: 13, name: "Divine Stone", quantity: 4 },
                        { level: 14, name: "Divine Stone", quantity: 5 },
                        { level: 15, name: "Divine Stone", quantity: 7 },
                        { level: 16, name: "Divine Stone", quantity: 7 },
                        { level: 17, name: "Divine Stone", quantity: 7 },
                        { level: 18, name: "Divine Stone", quantity: 7 },
                        { level: 19, name: "Divine Stone", quantity: 7 },
                        { level: 20, name: "Divine Stone", quantity: 10 }
                    ]
                }
            ]
        }
    }
};
