# Force Wing System Documentation

## Overview

The Force Wing System is a character progression system that provides stat bonuses through two main components:
1. **Wing Level Progression** - Direct stat scaling based on wing level
2. **Skill Slots** - 12 selectable skill slots with individual stat bonuses

## System Components

### 1. Wing Level Progression

**Level Range**: 1-400

**Base Stats Per Level**:
- +1 HP per level
- +1 All Attack per level
- +1 Defense per level

**Examples**:
- Level 1: +1 HP, +1 All Attack, +1 Defense
- Level 100: +100 HP, +100 All Attack, +100 Defense
- Level 400: +400 HP, +400 All Attack, +400 Defense

**Characteristics**:
- Simple linear progression
- No experience requirements
- Direct level input/selection
- Immediate stat application

### 2. Skill Slots System

**Total Slots**: 12 slots arranged in a specific pattern

**Slot Mechanics**:
- Each slot starts empty
- Click to open selection window
- 6 stat options per slot (slot-specific)
- Each selected stat has its own level progression

**Stat Level Ranges**:
- **Most stats**: Max level 5
- **Some stats**: Max level 4
- **Rare stats**: Max level 2

**Unlock System** (Future Implementation):
- Slots unlock at different wing levels
- Individual stats unlock at certain wing levels
- Option for "unlock all" vs "level-gated" modes

## User Interface

### Visual Layout
- Central force wing image with decorative wings (550px width)
- 12 slots arranged in organic V-formation matching in-game layout
- Slots positioned using manually mapped coordinates for authentic appearance
- Empty slots show as hexagonal outlines (60x60px)
- Filled slots display stat icons and levels
- Natural, asymmetric positioning creates wing-like flow
- Image size easily adjustable via CSS width property

### Interaction Flow
1. User clicks on empty slot
2. Selection window appears with 6 stat options
3. User selects desired stat
4. Stat level can be adjusted (1 to max level for that stat)
5. Slot updates to show selected stat and level
6. Stats are immediately applied to character

### UI Assets
- Base UI image: `assets/images/force-wing-ui/force_wing_ui_final.png` (550px width)
- Slot overlay image: `assets/images/force-wing-ui/slot_force_wing.png` (60x60px)
- Hexagonal slot styling matches game aesthetic
- Stat icons from existing StatsConfig system
- Responsive design with normalized coordinates (0-1 range)
- Easy resizing via `.fg-force-wing-ui-image { width: XXXpx; }` CSS property

## Technical Implementation

### Architecture Pattern
Follows established project patterns:
- **Data Loading**: PHP `wp_enqueue_script()` for data files
- **UI Creation**: HTML generation via `createSystemHTML()`
- **Stats Integration**: `BuildPlanner.updateStats('force-wing', stats)`
- **Save/Load**: `BuildSaverStore.saveData()` and `getEssentialData()`
- **Stat Display**: `StatIntegrationService.createStatSummaryHTML()`

### File Structure
```
js/force-wing-system/
├── force-wing-system.js       # Main system logic ✅
├── force-wing-data.js          # Slot configurations and stat data (TODO)

css/
└── force-wing-system.css       # Styling for UI components ✅

templates/systems/
└── force-wing-system.php       # PHP template with base UI ✅

assets/images/force-wing-ui/
├── force_wing_ui_final.png     # Base UI background image ✅
└── slot_force_wing.png         # Individual slot overlay image ✅
```

### Data Structure

**Wing Level Data**:
```javascript
{
    currentLevel: 1,        // Current wing level (1-400)
    maxLevel: 400          // Maximum allowed level
}
```

**Slot Data**:
```javascript
{
    slotId: 1,             // Slot identifier (1-12)
    selectedStat: null,    // Selected stat ID or null
    statLevel: 1,          // Level of selected stat (1-max)
    availableStats: [...]  // Array of available stats for this slot
}
```

**Save Data Structure**:
```javascript
{
    wingLevel: 1,
    slots: [
        { slotId: 1, selectedStat: 'attack', statLevel: 3 },
        { slotId: 2, selectedStat: null, statLevel: 1 },
        // ... 12 slots total
    ]
}
```

## Integration Points

### Stats System
- Uses existing `stats-config.js` for stat definitions
- Integrates with main build summary via `BuildPlanner.updateStats()`
- Follows standard stat naming conventions

### Build Saver
- Implements `getEssentialData()` for save functionality
- Uses `BuildSaverStore` for data persistence
- Supports build sharing via compressed URLs

### UI Components
- Uses `SelectionWindowManager` for stat selection popups
- Follows established styling patterns
- Responsive design matching other systems

## Future Enhancements

### Level-Gated Progression
- Implement slot unlock requirements
- Add stat unlock level requirements
- Toggle between "all unlocked" and "level-gated" modes

### Advanced Features
- Set bonuses for specific stat combinations
- Wing appearance customization
- Additional wing types/variants
- Enhanced visual effects for high-level wings

## Development Status

### Phase 1: Foundation ✅ COMPLETED
- [x] Documentation created
- [x] Basic PHP template setup
- [x] UI background image integration
- [x] Core JavaScript structure
- [x] Basic level progression
- [x] Slot positioning system
- [x] Manual coordinate mapping completed

### Phase 2: Slot System ✅ COMPLETED
- [x] Slot data structure
- [x] UI slot positioning (organic V-formation)
- [x] Slot element creation and styling
- [x] Selection window integration
- [x] Stat level management
- [x] UI slot interactions and click handlers
- [x] Circular stat icon display
- [x] Level indicator positioning

### Phase 3: Integration ✅ COMPLETED
- [x] Basic stats calculation (wing level only)
- [x] Save/load functionality structure
- [x] Build summary integration framework
- [x] Complete slot stats integration
- [x] Stats flowing to main build summary
- [x] Selection window fully functional

### Phase 4: Polish 🚧 IN PROGRESS
- [x] Selection window implementation
- [x] Circular icon styling implementation
- [x] Clean CSS without redundant styles
- [x] Documentation updates
- [ ] Level-gated progression option
- [ ] Performance optimization
- [ ] Final testing and refinement

## Implementation Notes

### Coordinate Mapping Process
The slot positioning was achieved through a manual coordinate mapping process:

1. **Browser-based mapping tool** - JavaScript console tool for precise clicking
2. **Visual feedback** - Red circles (55x60px) showing exact slot positions
3. **Undo functionality** - Ctrl+Z support for correcting mistakes
4. **Normalized coordinates** - 0-1 range for responsive positioning
5. **Organic V-formation** - Natural, asymmetric layout matching in-game appearance

### Current Architecture
- **Stellar system pattern** - Uses separate background + overlay approach
- **Responsive design** - Normalized coordinates work across screen sizes
- **Clean code** - All console logging and legacy code removed
- **Simplified CSS** - Removed unused slot styles and redundant constraints
- **Easy resizing** - Single CSS property controls entire UI size
- **Production ready** - Core foundation complete and stable

### Icon Styling Implementation
The circular stat icons were implemented with a simple but important fix:

**Problem**: Icons appeared square instead of circular
**Root Cause**: JavaScript was creating `<img>` elements without the required CSS class
**Solution**: Added `class="fg-force-wing-slot-icon"` to the img tag in JavaScript
**Result**: Clean, circular icons with proper border-radius styling

**Key Learnings**:
- CSS targeting `.fg-force-wing-slot-icon` but JavaScript wasn't applying the class
- Simple one-line fix: `class="fg-force-wing-slot-icon"` in the img element
- Icon container is needed for level indicator positioning (absolute positioning reference)
- Both icon and container sizes must match for proper circular display
- Clean CSS without `!important` overrides once the class issue was resolved

### Icon Size Adjustment
To adjust icon sizes, modify both elements in `css/force-wing-system.css`:
```css
.fg-force-wing-slot-icon-container {
    width: 48px;  /* Match icon size */
    height: 48px; /* Match icon size */
}

.fg-force-wing-slot-icon {
    width: 48px;  /* Desired icon size */
    height: 48px; /* Desired icon size */
}
```

### Next Steps
1. Implement level-gated progression option
2. Performance optimization and testing
3. Final refinement and polish
