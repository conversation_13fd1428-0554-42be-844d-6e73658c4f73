/* Build Planner Styles */

:root {
    /* Core colors */
    --bg-primary: #1a1a1a;
    --bg-secondary: #252525;
    --bg-tertiary: #333333;
    --bg-panel: #202020;
    --text-primary: #f5f5f5;
    --text-secondary: #b0b0b0;
    --accent-primary: #ff9900;
    --accent-secondary: #ffbb33;
    --border-light: rgba(255, 255, 255, 0.15);
    --border-dark: rgba(0, 0, 0, 0.3);
    --positive-value: #4caf50;
    --negative-value: #f44336;
    
    /* System colors */
    --pet-color: #ff9900;
    --stellar-color: #4a90e2;
    --honor-color: #9c27b0;
    
    /* Stats categories colors */
    --offensive-color: #ff6b6b;
    --defensive-color: #4ecdc4;
    --utility-color: #f9d423;
    --text-muted: #8b92a5;
    --shadow-light: 0 2px 10px rgba(0, 0, 0, 0.15);
    --shadow-medium: 0 4px 20px rgba(0, 0, 0, 0.25);
}

/* Global scrollbar styling for the entire plugin */
/* Firefox scrollbar styling */
[class*="fg-"], 
[class*="essence-"], 
.forceguides-build-planner,
.forceguides-build-planner * {
  scrollbar-width: thin;
  scrollbar-color: #3c3f44 #121416;
}

/* Webkit scrollbar styling (Chrome, Safari, Edge) */
[class*="fg-"]::-webkit-scrollbar, 
[class*="essence-"]::-webkit-scrollbar,
.forceguides-build-planner *::-webkit-scrollbar {
  width: 8px;
}

[class*="fg-"]::-webkit-scrollbar-track,
[class*="essence-"]::-webkit-scrollbar-track,
.forceguides-build-planner *::-webkit-scrollbar-track {
  background: #121416;
  border-radius: 4px;
}

[class*="fg-"]::-webkit-scrollbar-thumb,
[class*="essence-"]::-webkit-scrollbar-thumb,
.forceguides-build-planner *::-webkit-scrollbar-thumb {
  background: #3c3f44;
  border-radius: 4px;
}

[class*="fg-"]::-webkit-scrollbar-thumb:hover,
[class*="essence-"]::-webkit-scrollbar-thumb:hover,
.forceguides-build-planner *::-webkit-scrollbar-thumb:hover {
  background: #4f5a68;
}

/* Base Styles */
.fg-build-planner-container {
    display: flex;
    background: var(--bg-primary);
    border-radius: 10px;
    overflow: hidden;
    box-shadow: var(--shadow-medium);
    color: var(--text-primary);
    font-family: 'Inter', system-ui, -apple-system, BlinkMacSystemFont, sans-serif;
    min-height: 800px;
    margin: 20px auto;
    max-width: 1200px;
}

h3, h4 {
    font-weight: 600;
    margin-top: 0;
    margin-bottom: 15px;
}

/* System Selection Sidebar */
.fg-systems-sidebar {
    width: 200px;
    background: var(--bg-sidebar);
    border-right: 1px solid var(--border-dark);
    padding: 20px 0;
    display: flex;
    flex-direction: column;
    flex-shrink: 0;
}

.fg-sidebar-title {
    font-size: 1.1rem;
    font-weight: 600;
    padding: 0 20px 15px;
    border-bottom: 1px solid var(--border-light);
    margin-bottom: 15px;
}

.fg-system-buttons {
    display: flex;
    flex-direction: column;
    gap: 8px;
    padding: 0 10px;
}

.fg-system-button {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px 15px;
    border-radius: 8px;
    cursor: pointer;
    transition: background-color 0.2s, transform 0.2s;
    user-select: none;
}

.fg-system-button:hover:not(.disabled) {
    background-color: rgba(255, 255, 255, 0.05);
    transform: translateX(3px);
}

.fg-system-button.active {
    background-color: rgba(61, 132, 230, 0.15);
    border-left: 3px solid var(--accent-primary);
    padding-left: 12px;
}

.fg-system-button.disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

/* System icons for sidebar */
.fg-system-button .icon {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    margin-right: 8px;
    background-color: #444;
    padding: 5px;
    box-sizing: border-box;
}

.fg-system-button .class-icon {
    background-color: #0097a7; /* Teal for class */
}

.fg-system-button .stellar-icon {
    background-color: #4a148c; /* Purple for stellar */
}

.fg-system-button .honor-icon {
    background-color: #b71c1c; /* Dark red for honor medals */
}

.fg-system-button .pet-icon {
    background-color: #1b5e20; /* Green for pets */
}

.fg-system-button .equipment-icon {
    background-color: #827717; /* Olive for equipment */
}

.fg-system-button .costume-icon {
    background-color: #880e4f; /* Pink for costumes */
}

.fg-system-button .gold-merit-icon {
    background-color: #ff6f00; /* Orange/gold for gold merit */
}

.fg-system-button .platinum-merit-icon {
    background-color: #757575; /* Silver/platinum for platinum merit */
}

.fg-system-button .force-wing-icon {
    background-color: #01579b; /* Blue for force wing */
}

.fg-system-button .stats-icon {
    background-color: #6a1b9a; /* Purple for stats */
}

.fg-system-button .essence-runes-icon {
    background-color: #1a237e; /* Indigo for essence runes */
}

.fg-system-button .karma-runes-icon {
    background-color: #bf360c; /* Brown for karma runes */
}

.fg-system-button .gear-icon {
    background-color: #0d47a1; /* Blue for gear */
}

.fg-system-button .overlord-mastery-icon {
    background-color: #c2185b; /* Pink/purple for overlord mastery */
}

.fg-system-button .placeholder-icon {
    background-color: #555; /* Gray for placeholder */
}

.fg-system-button svg {
    width: 24px;
    height: 24px;
}

/* Main Content Area */
.fg-main-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    min-width: 0; /* Fix for flexbox children */
}

/* System Display Area */
.fg-system-display {
    flex: 1;
    padding: 20px;
    position: relative;
    background: var(--bg-secondary);
    overflow: hidden;
    min-height: 600px;
}

.fg-system-panel {
    display: none;
    height: 100%;
    width: 100%;
}

.fg-system-panel.active {
    display: block;
}

/* Stellar Canvas Container */
.stellar-canvas-container {
    width: 100%;
    height: 100%;
    min-height: 600px;
    display: flex;
    justify-content: center;
    align-items: center;
    background-color: var(--bg-primary);
    border-radius: 8px;
    overflow: hidden;
}

#stellar-canvas {
    width: 100%;
    height: 100%;
    display: block;
}

/* Gear Placeholder */
.gear-placeholder {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100%;
    background: rgba(0, 0, 0, 0.1);
    border-radius: 8px;
    border: 1px dashed var(--border-light);
}

/* Stats Summary Area */
.fg-stats-summary {
    background: var(--bg-panel);
    padding: 20px;
    border-top: 1px solid var(--border-dark);
}

.fg-summary-tabs {
    display: flex;
    border-bottom: 1px solid var(--border-light);
    margin-bottom: 15px;
}

.fg-summary-tab {
    padding: 10px 20px;
    background: transparent;
    border: none;
    color: var(--text-secondary);
    cursor: pointer;
    font-weight: 600;
    position: relative;
    transition: color 0.2s;
}

.fg-summary-tab:hover {
    color: var(--text-primary);
}

.fg-summary-tab.active {
    color: var(--text-primary);
}

.fg-summary-tab.active::after {
    content: '';
    position: absolute;
    bottom: -1px;
    left: 0;
    width: 100%;
    height: 2px;
    background-color: var(--accent-primary);
}

.fg-summary-tab[data-tab="atk-stats"].active::after {
    background-color: var(--offensive-color);
}

.fg-summary-tab[data-tab="def-stats"].active::after {
    background-color: var(--defensive-color);
}

.fg-summary-tab[data-tab="other-stats"].active::after {
    background-color: var(--utility-color);
}

.fg-summary-content {
    padding: 15px 0;
}

/* New 3-column stats table */
.fg-stats-table {
    display: flex;
    width: 100%;
    border-radius: 4px;
    overflow: hidden;
}

.fg-stats-column {
    flex: 1;
    min-width: 0;
    padding: 10px;
    background-color: rgba(0, 0, 0, 0.2);
    border-right: 1px solid var(--border-light);
}

.fg-stats-column:last-child {
    border-right: none;
}

.fg-stat-item {
    display: flex;
    align-items: center;
    padding: 4px 8px;
    margin-bottom: 2px;
    font-size: 13px;
    line-height: 1.2;
    justify-content: space-between;
    border-bottom: 1px solid rgba(255, 255, 255, 0.05);
}

.fg-stat-item:last-child {
    margin-bottom: 0;
    border-bottom: none;
}

.fg-stat-name {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    color: var(--text-primary);
    margin-right: 8px;
}

.fg-stat-value {
    font-weight: 600;
    text-align: right;
    white-space: nowrap;
}

/* Color coding for stat values */
#atk-stats-list .fg-stat-value {
    color: var(--offensive-color, #ff6b6b);
}

#def-stats-list .fg-stat-value {
    color: var(--defensive-color, #4ecdc4);
}

#other-stats-list .fg-stat-value {
    color: var(--utility-color, #f9d423);
}

.fg-stat-icon {
    width: 16px;
    height: 16px;
    margin-right: 6px;
    object-fit: contain;
}

/* Make tab content take up full height */
.fg-summary-tab-content {
    display: none;
}

.fg-summary-tab-content.active {
    display: block;
}

/* Remove scrollbar styling since we're not using scrollbars anymore */
.fg-summary-tab-content::-webkit-scrollbar {
    width: 0;
    display: none;
}

.fg-summary-tab-content::-webkit-scrollbar-track {
    display: none;
}

.fg-summary-tab-content::-webkit-scrollbar-thumb {
    display: none;
}

.fg-summary-tab-content::-webkit-scrollbar-thumb:hover {
    display: none;
}

/* Responsive Adjustments */
@media (max-width: 768px) {
    .fg-build-planner-container {
        flex-direction: column;
    }
    
    .fg-systems-sidebar {
        width: 100%;
        border-right: none;
        border-bottom: 1px solid var(--border-dark);
        padding: 15px;
    }
    
    .fg-system-buttons {
        flex-direction: row;
        flex-wrap: wrap;
        justify-content: center;
    }
    
    .fg-system-button {
        width: auto;
    }
}

/* Stellar Node Panel Styles */
#stellar-node-panel {
    position: absolute;
    top: 20px;
    right: 20px;
    width: 300px;
    background-color: rgba(25, 30, 45, 0.95);
    border-radius: 8px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.5);
    color: #fff;
    z-index: 1000;
    padding: 15px;
    max-height: 80vh;
    overflow-y: auto;
    animation: fade-in 0.3s ease-in-out;
}

#stellar-node-panel h3 {
    margin: 0 0 15px 0;
    border-bottom: 1px solid #4a90e2;
    padding-bottom: 8px;
    color: #8ce3ff;
}

#stellar-node-panel h4 {
    margin: 0 0 10px 0;
    color: #ffd866;
}

#stellar-node-panel button {
    cursor: pointer;
    transition: all 0.2s ease;
}

#stellar-node-panel button:hover {
    opacity: 0.8;
}

.stat-item {
    transition: background-color 0.2s ease;
}

.stat-item:hover {
    background-color: rgba(80, 90, 110, 0.6);
}

@keyframes fade-in {
    from { opacity: 0; transform: translateY(-10px); }
    to { opacity: 1; transform: translateY(0); }
}

/* Stellar System Placeholder */
.stellar-placeholder {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
    min-height: 500px;
    padding: 30px;
    text-align: center;
    color: #fff;
    background-color: var(--bg-secondary);
    border-radius: 8px;
}

.stellar-placeholder .star-icon {
    font-size: 80px;
    margin-bottom: 20px;
    color: #9c27b0;
    text-shadow: 0 0 20px rgba(156, 39, 176, 0.5);
}

.stellar-placeholder h2 {
    margin-bottom: 15px;
    font-size: 28px;
    color: #e1bee7;
}

.stellar-placeholder .coming-soon {
    font-size: 20px;
    opacity: 0.7;
    margin-bottom: 30px;
    font-weight: bold;
    letter-spacing: 1px;
}

.stellar-placeholder .description {
    max-width: 500px;
    line-height: 1.6;
    opacity: 0.8;
}

/* System Placeholders */
.system-placeholder {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    height: 100%;
    background: rgba(0, 0, 0, 0.1);
    border-radius: 8px;
    border: 1px dashed var(--border-light);
    padding: 30px;
    text-align: center;
}

.system-placeholder h2 {
    margin-top: 0;
    margin-bottom: 10px;
    color: var(--text-primary);
    font-size: 1.8rem;
}

.system-placeholder .coming-soon {
    font-size: 1.2rem;
    font-weight: 600;
    color: var(--accent-primary);
    background: rgba(61, 132, 230, 0.15);
    padding: 5px 15px;
    border-radius: 20px;
    margin-bottom: 20px;
}

.system-placeholder .description {
    color: var(--text-secondary);
    max-width: 500px;
    line-height: 1.6;
} 