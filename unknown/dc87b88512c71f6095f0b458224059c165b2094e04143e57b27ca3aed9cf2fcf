# ForceGuides Build Planner - Project Overview

## Project Description

A comprehensive web-based tool for game character optimization. Players can simulate character progression across multiple in-game systems, save and share builds, and optimize for maximum damage output.

**WordPress plugin with modular JavaScript architecture.**

## Core Architecture

### Key Components
- **PHP Plugin**: WordPress integration, script loading, templates
- **Build Planner Core**: System navigation, stats consolidation, central store
- **Stats Summary**: Categorized display (offensive/defensive/utility) with modifiers
- **Build Saver**: localStorage persistence, build sharing foundation
- **Stats Config**: Stat definitions, names, icons, categories
- **Systems**: Self-contained modules for each game progression system

### Design Patterns
- **Module Pattern**: Encapsulated system namespaces
- **Observer Pattern**: Systems publish stats to core
- **Central Store**: BuildSaverStore for consistent state
- **Event Delegation**: Efficient dynamic content handling

## Current Progress

### Completed Systems ✅
- **Pet System**: Multi-tier progression (Normal/Covenant/Trust)
- **Essence Runes**: Slot-based rune equipping with leveling
- **Stellar Link**: Constellation skill tree with color bonuses
- **Costumes**: Three costume types with epic craft bonuses
- **Equipment**: Comprehensive gear system with upgrades
- **Honor Medal**: Medal progression system
- **Overlord Mastery**: Level 200+ skill mastery system

### Core Features ✅
- System navigation and dynamic loading
- Categorized stats summary (offensive/defensive/utility)
- Build saving/loading with localStorage
- Build sharing with compressed URLs
- Damage calculator integration
- Standardized stat integration across all systems

### Key Architectural Improvements
- **Standardized StatIntegrationService**: Uniform stat displays across all systems
- **Centralized BuildSaverStore**: Consistent save/load for all systems
- **Modular Stats Summary**: Clean separation from core logic
- **Stat Modifier System**: Handles ALL ATTACK, ALL AMP modifiers properly
- **Parallel System Loading**: All systems load at startup for immediate stats availability

## Recent Key Fixes
- **StatsConfig Integration**: Standardized icon paths and stat names across systems
- **Stats Integration**: Fixed systems not appearing in main summary via proper `BuildPlanner.updateStats()` usage
- **System Naming**: Resolved save/load issues caused by inconsistent system ID naming
- **Equipment Upgrades**: Fixed belt-specific upgrade display issues

## Future Goals
- **Enhanced Build Management**: Multiple saved builds, naming, comparison
- **Damage Calculator**: Game-accurate damage formulas and optimization
- **Build Optimization**: Auto-suggestions for optimal stat allocations

## Development Best Practices
- **Performance**: Batch DOM operations, use event delegation, cache elements
- **Code Structure**: Simple modules, consistent naming, separate logic from UI
- **Error Handling**: Validate early, fail fast, minimal error paths
- **Maintenance**: Remove unused code, eliminate duplication, consistent patterns

### System Development Guidelines

## ⚠️ MANDATORY SYSTEM SETUP PROCEDURES

**To prevent setup errors and ensure consistency, ALL new systems MUST follow these standardized procedures:**

### 1. **Data Loading Pattern** (REQUIRED)
- ✅ **Use PHP `wp_enqueue_script()`** like pet system - simple and reliable
- **Example**: Add to `forceguides-build-planner.php`:
```php
// Your system data
$your_system_data_path = plugin_dir_path(__FILE__) . 'js/your-system/your-system-data.js';
$your_system_data_version = file_exists($your_system_data_path) ? filemtime($your_system_data_path) : time();

wp_enqueue_script('forceguides-your-system-data',
    plugin_dir_url(__FILE__) . 'js/your-system/your-system-data.js',
    array('jquery'),
    $your_system_data_version, true);
```

### 2. **Stat Summary Display** (REQUIRED)
- ✅ **Use `StatIntegrationService.createStatSummaryHTML(stats)`** - standardized across all systems
- ❌ **Never create custom stat display HTML** - causes inconsistency and duplicate code
- **Example**:
```javascript
updateStatsSummary: function(stats) {
    if (!this.elements.summaryContent) return;

    // Use StatIntegrationService for consistent stat display (like pet, honor, costume systems)
    if (typeof StatIntegrationService !== 'undefined') {
        this.elements.summaryContent.innerHTML = StatIntegrationService.createStatSummaryHTML(stats);
    } else {
        // Simple fallback if service isn't available
        this.elements.summaryContent.innerHTML = '<p class="no-stats">No stats selected yet.</p>';
    }
}
```

### 3. **UI Creation Pattern** (REQUIRED)
- ✅ **Use HTML generation** like pet/honor systems - `createSystemHTML()` → `panel.innerHTML = html`
- ❌ **Never use DOM manipulation** - complex and error-prone
- **Example**:
```javascript
initUI: function() {
    this.createSystemUI();
},

createSystemUI: function() {
    const html = `<div class="your-system-container">...</div>`;
    this.elements.panel.innerHTML = html;
    // Cache elements and setup handlers
}
```

### 4. **Stats Integration** (REQUIRED)
- ✅ **Use `BuildPlanner.updateStats('system-id', stats)`** - standard method
- ❌ **Never use alternative methods** - causes stats to not appear in summary
- **Example**:
```javascript
updateSystemStats: function() {
    const stats = this.calculateStats();

    // Update the stats in the build planner (using same method as all other systems)
    if (typeof BuildPlanner !== 'undefined' && BuildPlanner.updateStats) {
        BuildPlanner.updateStats('your-system', stats);
    }
}
```

### 5. **Save/Load Integration** (REQUIRED)
- ✅ **Use `BuildSaverStore.saveData()` and `BuildSaverStore.getSystemData()`** - standardized
- ❌ **Never create custom save mechanisms** - breaks build sharing
- **Example**:
```javascript
saveToStore: function() {
    if (typeof BuildSaverStore !== 'undefined' && BuildSaverStore.saveData) {
        const essentialData = this.getEssentialData();
        BuildSaverStore.saveData('your-system', essentialData);
    }
},

loadFromStore: function() {
    if (window.BuildSaverStore && BuildSaverStore.dataLoaded) {
        const systemData = BuildSaverStore.getSystemData('your-system');
        if (systemData) {
            return this.loadFromData(systemData);
        }
    }
    return false;
}
```

### 6. **System Registration** (REQUIRED)
- ✅ **Add to `forceguides-build-planner.php`**: `fg_register_system('your-system', $system_js_urls, $system_css_urls);`
- ✅ **Add to `build-planner-core.js` systems array**: `{ id: 'your-system', name: 'Your System' }`
- ✅ **Add to `templates/build-planner.php`**: `<?php include plugin_dir_path(__FILE__) . 'systems/your-system.php'; ?>`

### 7. **Template Structure** (REQUIRED)
- ✅ **Use simple placeholder template** like pet system
- ❌ **Never use complex template structure** - JavaScript should generate all content
- **Example**:
```php
<div id="fg-your-system-system" class="fg-system-panel">
    <!-- Your System placeholder -->
    <div class="system-placeholder">
        <h2>Your System</h2>
        <p class="coming-soon">Coming Soon</p>
    </div>
</div>
```

### **CRITICAL REMINDERS**
1. **Follow pet system pattern** - it's the proven, simple approach
2. **Use StatIntegrationService** - never create custom stat displays
3. **PHP data loading** - never dynamic JavaScript loading
4. **Standard save/load** - use BuildSaverStore methods
5. **Test immediately** - verify stats appear in summary

**Following these procedures prevents 90% of common setup issues and ensures consistency across all systems.**