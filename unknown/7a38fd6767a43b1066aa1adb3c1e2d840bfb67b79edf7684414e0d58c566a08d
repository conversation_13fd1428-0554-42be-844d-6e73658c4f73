/* Pet System Styles - Optimized */

:root {
  /* Base colors */
  --fg-dark-bg: #1c1e22;
  --fg-border: #3c3f44;
  --fg-darker-bg: rgba(0, 0, 0, 0.2);
  --fg-text: #d0d0d0;

  /* Accent colors */
  --fg-accent: #66bb6a;
  --fg-highlight: #4c8e50;
  --fg-header: #ff3333;
  --fg-hover-bg: rgba(27, 94, 32, 0.2);

  /* Button colors */
  --fg-button-bg: #3a3a3a;
  --fg-button-border: #5e5e5e;
  --fg-button-hover-bg: #4a4a4a;

  /* Remove button colors */
  --fg-remove-button-bg: rgba(187, 66, 66, 0.7);
  --fg-remove-button-hover-bg: rgba(211, 47, 47, 0.9);
}

/* Base styles */
.fg-pet-system-container {
  padding: 20px;
  max-width: 100%;
  background-color: var(--fg-dark-bg);
  border-radius: 5px;
  border: 1px solid var(--fg-border);
  box-sizing: border-box;
}

/* Panels & Headers */
.fg-pet-tier-panel {
  background: rgba(28, 30, 34, 0.5);
  border-radius: 8px;
  padding: 15px;
  margin-bottom: 20px;
  border: 1px solid var(--fg-border);
}

.fg-pet-tier-header {
  font-size: 1rem;
  color: var(--fg-header);
  margin-bottom: 15px;
  text-align: center;
  font-weight: bold;
  padding-bottom: 6px;
  border-bottom: 1px dashed rgba(255, 51, 51, 0.3);
  text-shadow: 0 1px 1px rgba(0, 0, 0, 0.5);
}

/* Pet Slots Container */
.fg-pet-slots-container {
  display: flex;
  flex-wrap: wrap;
  gap: 13px;
  margin-top: 10px;
  margin-bottom: 10px;
}

.fg-pet-slot {
  position: relative;
  min-height: 45px;
  max-height: 52px;
  aspect-ratio: 1/1;
  border: 1px solid #444;
  border-radius: 4px;
  background-color: var(--fg-darker-bg);
  cursor: pointer;
  transition: all 0.2s ease;
  user-select: none;
}

.fg-pet-slot:hover {
  border-color: #4f5a68;
  box-shadow: inset 0 0 4px rgba(79, 90, 104, 0.7);
}

.fg-pet-slot.empty {
  border-color: #333;
  background: linear-gradient(135deg, #1a1a1a 0%, #222 100%);
}

.fg-pet-slot.selected {
  border-color: var(--fg-highlight);
  background: rgba(27, 94, 32, 0.3);
}

/* Slot wrapper for value below */
.fg-pet-slot-wrapper {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 2px;
}

/* Stat value below slot */
.fg-pet-slot-stat-value-below {
  font-size: 0.8em;
  color: var(--fg-header);
  font-weight: bold;
  text-align: center;
  line-height: 1;
  user-select: text;
  margin: 0;
}

/* Slot Elements */
.fg-pet-slot-level {
  position: absolute;
  top: 1px;
  right: 3px;
  font-size: 0.7rem;
  color: #888;
}

.fg-pet-slot-remove {
  position: absolute;
  top: 2px;
  left: 2px;
  width: 16px;
  height: 16px;
  background: var(--fg-remove-button-bg);
  color: white;
  border: none;
  border-radius: 50%;
  font-size: 12px;
  line-height: 1;
  padding: 0;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background-color 0.2s;
  z-index: 5;
}

.fg-pet-slot-remove:hover {
  background: var(--fg-remove-button-hover-bg);
}

.fg-pet-slot-content {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}

/* Stat Icons */
.fg-pet-slot-stat {
  width: 100%;
  height: 100%;
  position: relative;
  padding: 2px;
  text-align: center;
  line-height: 1.1;
}

.fg-pet-slot-stat-icon {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}

.fg-pet-slot-stat-icon img {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

/* Icon styling for stat options */
.fg-pet-stat-option-icon,
.fg-pet-selected-stat-icon {
  object-fit: contain;
  border-radius: 3px;
  background-color: rgba(27, 94, 32, 0.15);
  border: 1px solid rgba(102, 187, 106, 0.3);
  padding: 2px;
}

.fg-pet-stat-option-icon {
  width: 24px;
  height: 24px;
}

.fg-pet-selected-stat-icon {
  width: 20px;
  height: 20px;
  margin-right: 8px;
  vertical-align: middle;
  padding: 1px;
}

.fg-pet-stat-option-left {
  display: flex;
  align-items: center;
  gap: 10px;
}

.fg-pet-selected-stat-info {
  display: flex;
  align-items: center;
}

/* Stat Selection Popup */
.fg-pet-stat-popup {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999;
  opacity: 0;
  visibility: hidden;
  transition: opacity 0.3s, visibility 0.3s;
  pointer-events: none;
}

.fg-pet-stat-popup.active {
  opacity: 1;
  visibility: visible;
  pointer-events: auto;
}

.fg-pet-stat-popup-content {
  background: var(--fg-dark-bg);
  border-radius: 5px;
  width: 90%;
  max-width: 650px;
  max-height: 80vh;
  overflow: hidden;
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.5);
  display: flex;
  flex-direction: column;
  border: 1px solid var(--fg-border);
}

.fg-pet-stat-popup-header {
  padding: 10px 15px;
  border-bottom: 1px solid var(--fg-border);
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: #272a2e;
}

.fg-pet-stat-popup-header h3 {
  margin: 0;
  color: #e0e0e0;
  font-size: 1rem;
}

.fg-pet-stat-popup-close {
  background: transparent;
  border: none;
  font-size: 18px;
  cursor: pointer;
  color: #a0a0a0;
  padding: 0;
  line-height: 1;
  transition: color 0.2s;
}

.fg-pet-stat-popup-close:hover {
  color: #ffffff;
}

.fg-pet-stat-popup-body {
  padding: 0;
  overflow-y: auto;
  max-height: calc(80vh - 60px);
}

/* Scrollbar styles removed - now handled by global styles */

/* Stat options */
.fg-pet-stat-options {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 8px;
  padding: 15px;
}

.fg-pet-stat-option {
  background: var(--fg-darker-bg);
  border-radius: 3px;
  padding: 8px 10px;
  cursor: pointer;
  transition: background-color 0.2s;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border: 1px solid var(--fg-border);
}

.fg-pet-stat-option:hover {
  background: var(--fg-hover-bg);
  border-color: var(--fg-accent);
}

.fg-pet-stat-option-name {
  font-weight: 500;
  color: var(--fg-text);
  font-size: 0.85rem;
  display: flex;
  flex-wrap: wrap;
  word-break: normal;
}

.fg-pet-stat-option-name span.chance {
  color: #aaa;
  font-size: 0.8rem;
  font-weight: normal;
  margin-left: 4px;
}

.fg-pet-stat-option-value {
  color: var(--fg-accent);
  font-weight: 600;
  font-size: 0.85rem;
}

/* Selected Stats section */
.fg-pet-selected-stats,
.fg-pet-reroll-costs {
  background: var(--fg-darker-bg);
  border-radius: 5px;
  padding: 15px;
  margin-top: 20px;
  border: 1px solid var(--fg-border);
}

.fg-pet-selected-stats h3,
.fg-pet-reroll-costs h3 {
  margin-top: 0;
  margin-bottom: 12px;
  border-bottom: 1px solid var(--fg-border);
  padding-bottom: 8px;
  font-size: 1rem;
}

.fg-pet-selected-stats h3 {
  color: #e0e0e0;
}

.fg-pet-reroll-costs h3 {
  color: #f8d64e;
}

.fg-pet-selected-list {
  display: flex;
  flex-direction: column;
  gap: 5px;
  max-height: 300px;
  overflow-y: auto;
}

.fg-pet-selected-stat {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 6px 10px;
  background: rgba(0, 0, 0, 0.2);
  border-radius: 3px;
  transition: background-color 0.2s;
  border: 1px solid #333333;
}

.fg-pet-selected-stat:hover {
  background: rgba(0, 0, 0, 0.3);
}

.fg-pet-selected-stat-name {
  font-weight: 600;
  color: var(--fg-text);
  font-size: 0.85rem;
}

.fg-pet-selected-stat-value,
.fg-pet-resource-value {
  font-weight: 600;
  color: var(--fg-accent);
  font-size: 0.85rem;
}

/* Resources and costs */
.fg-pet-resource-item {
  display: flex;
  justify-content: space-between;
  padding: 6px 0;
  border-bottom: 1px dotted var(--fg-border);
}

.fg-pet-resource-name {
  color: var(--fg-text);
  font-size: 0.85rem;
}

.fg-pet-resource-value {
  color: #a9e34b;
}

/* Placeholders and animations */
p.no-stats,
.no-costs {
  color: #707070;
  font-style: italic;
  text-align: center;
  font-size: 0.9rem;
  padding: 15px 0;
}



@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}