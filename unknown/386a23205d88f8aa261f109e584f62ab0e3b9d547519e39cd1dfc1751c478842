/**
 * Costumes System Styles
 * UI styles for the costumes system - mimics the game's costume system
 */

.fg-costume-system-container {
    width: 100%;
    max-width: 800px;
    margin: 0 auto;
    padding: 15px;
    background-color: #1c1e22;
    border-radius: 5px;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.7);
    color: #f0f0f0;
    border: 1px solid rgba(60, 63, 68, 0.8);
}

.fg-costume-system-container h2 {
    margin: 0 0 20px 0;
    padding: 0 0 10px 0;
    color: #e0e0e0;
    font-size: 1.4rem;
    text-align: center;
    border-bottom: 1px solid #3c3f44;
}

/* Costume rows container */
.fg-costume-rows-container {
    display: flex;
    flex-direction: column;
    gap: 15px;
    margin-bottom: 20px;
}

/* Individual costume row */
.fg-costume-row {
    display: flex;
    align-items: center;
    background: rgba(28, 30, 34, 0.5);
    padding: 15px;
    border-radius: 8px;
    border: 1px solid #3c3f44;
    margin-bottom: 5px;
}

.fg-costume-name {
    width: 180px;
    min-width: 180px;
    font-weight: bold;
    color: #e0e0e0;
    font-size: 0.95rem;
}

/* Slots container */
.fg-costume-slots {
    display: flex;
    gap: 10px;
    flex: 1;
}

/* Individual slot styles */
.fg-costume-slot {
    position: relative;
    min-height: 45px;
    max-height: 52px;
    aspect-ratio: 1/1;
    border-radius: 4px;
    background-color: rgba(0, 0, 0, 0.2);
    border: 1px solid #444;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
    overflow: hidden;
}

.fg-costume-slot:hover {
    border-color: #4f5a68;
    box-shadow: inset 0 0 4px rgba(79, 90, 104, 0.7);
}

.fg-costume-slot.empty {
    border-color: #333;
    background: linear-gradient(135deg, #1a1a1a 0%, #222 100%);
}

.fg-costume-slot.selected {
    border-color: #4c8e50;
    background: rgba(27, 94, 32, 0.3);
}

.fg-costume-slot-empty {
    color: #707070;
    font-size: 0.85rem;
    text-align: center;
}

/* Slot stat content */
.fg-costume-slot-stat {
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 100%;
    height: 100%;
    padding: 0;
}

.fg-costume-slot-stat-icon {
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.2);
    border-radius: 4px;
    padding: 0;
    display: flex;
    align-items: center;
    justify-content: center;
}

.fg-costume-slot-stat-icon img {
    width: 100%;
    height: 100%;
    object-fit: contain;
}

.fg-costume-slot-details {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
}

.fg-costume-slot-stat-name {
    font-size: 0.75rem;
    color: #d0d0d0;
    margin-bottom: 2px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 100%;
}

.fg-costume-slot-stat-value {
    font-size: 0.85rem;
    color: #66bb6a;
    font-weight: bold;
}

.fg-costume-slot-remove {
    position: absolute;
    top: 2px;
    left: 2px;
    width: 16px;
    height: 16px;
    background: rgba(187, 66, 66, 0.7);
    color: white;
    border: none;
    border-radius: 50%;
    font-size: 12px;
    line-height: 1;
    padding: 0;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: background-color 0.2s;
    z-index: 5;
}

.fg-costume-slot-remove:hover {
    background: rgba(211, 47, 47, 0.9);
}

/* Epic craft dropdown */
.fg-costume-epic-craft {
    width: 200px;
    min-width: 200px;
    margin-left: 15px;
}

.fg-costume-epic-craft-select {
    width: 100%;
    padding: 8px 10px;
    border-radius: 4px;
    background-color: #272a2e;
    border: 1px solid #3c3f44;
    color: #e0e0e0;
    font-size: 0.9rem;
    cursor: pointer;
    appearance: none;
    -webkit-appearance: none;
    -moz-appearance: none;
    background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23d0d0d0'%3e%3cpath d='M7 10l5 5 5-5z'/%3e%3c/svg%3e");
    background-repeat: no-repeat;
    background-position: right 8px center;
    background-size: 16px;
}

.fg-costume-epic-craft-select:focus {
    outline: none;
    border-color: #4f5a68;
}

/* Epic craft option quality colors */
.fg-costume-epic-craft-select option.epic-legendary {
    color: #ff9800;
}

.fg-costume-epic-craft-select option.epic-epic {
    color: #673ab7;
}

.fg-costume-epic-craft-select option.epic-rare {
    color: #2196f3;
}

/* Selected stats summary */
.fg-costume-selected-stats {
    margin-top: 20px;
    padding: 15px;
    background: rgba(28, 30, 34, 0.5);
    border-radius: 8px;
    border: 1px solid #3c3f44;
}

.fg-costume-selected-stats h3 {
    margin: 0 0 15px 0;
    padding: 0 0 8px 0;
    color: #e0e0e0;
    font-size: 1rem;
    text-align: center;
    border-bottom: 1px solid #3c3f44;
}

.fg-costume-selected-list {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
}

/* Custom stat summary styles removed to use StatIntegrationService standard styles */

/* Popup for stat selection */
.fg-costume-stat-popup {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999;
    opacity: 0;
    visibility: hidden;
    transition: opacity 0.3s ease, visibility 0.3s ease;
}

.fg-costume-stat-popup.active {
    opacity: 1;
    visibility: visible;
}

.fg-costume-stat-popup-content {
    background: #1c1e22;
    border-radius: 5px;
    width: 90%;
    max-width: 600px;
    max-height: 80vh;
    overflow: hidden;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.5);
    display: flex;
    flex-direction: column;
    border: 1px solid #3c3f44;
}

.fg-costume-stat-popup-header {
    padding: 12px 15px;
    border-bottom: 1px solid #3c3f44;
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: #272a2e;
}

.fg-costume-stat-popup-header h3 {
    margin: 0;
    color: #e0e0e0;
    font-size: 1rem;
}

.fg-costume-stat-popup-close {
    background: none;
    border: none;
    color: #e0e0e0;
    font-size: 1.5rem;
    line-height: 1;
    cursor: pointer;
    opacity: 0.7;
    transition: opacity 0.2s;
}

.fg-costume-stat-popup-close:hover {
    opacity: 1;
}

.fg-costume-stat-popup-body {
    padding: 15px;
    overflow-y: auto;
    max-height: calc(80vh - 60px);
}

.fg-costume-stat-options {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 10px;
}

.fg-costume-stat-option {
    background: rgba(0, 0, 0, 0.15);
    border-radius: 4px;
    padding: 10px 12px;
    cursor: pointer;
    transition: all 0.2s;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border: 1px solid #3c3f44;
}

.fg-costume-stat-option:hover {
    background: rgba(40, 40, 40, 0.3);
    border-color: #4f5a68;
}

.fg-costume-stat-option-left {
    display: flex;
    align-items: center;
}

.fg-costume-stat-option-icon {
    width: 24px;
    height: 24px;
    margin-right: 10px;
    border-radius: 3px;
    background-color: rgba(0, 0, 0, 0.2);
    padding: 2px;
}

.fg-costume-stat-option-name {
    color: #d0d0d0;
    font-size: 0.9rem;
}

.fg-costume-stat-option-value {
    color: #66bb6a;
    font-weight: bold;
    font-size: 0.9rem;
}

/* Responsive styles */
@media screen and (max-width: 768px) {
    .fg-costume-row {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }

    .fg-costume-name {
        width: 100%;
        margin-bottom: 5px;
    }

    .fg-costume-slots {
        width: 100%;
        justify-content: space-between;
    }

    .fg-costume-epic-craft {
        width: 100%;
        margin-left: 0;
        margin-top: 10px;
    }

    /* Removed custom responsive style for fg-costume-stat-summary-item */
}

/* Green text for epic craft - similar to the screenshot */
.epic-craft-text {
    color: #a9e34b;
}

/* Force green text on epic options in the dropdown */
.fg-costume-epic-craft-select option {
    color: #a9e34b;
}

/* Costume with special display - for the green [Wing Costume] text in the screenshot */
.fg-costume-name-prefix {
    color: #a9e34b;
    font-weight: bold;
}