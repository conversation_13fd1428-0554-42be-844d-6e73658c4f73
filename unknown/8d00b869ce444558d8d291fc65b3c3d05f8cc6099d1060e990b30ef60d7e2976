/**
 * Achievement System
 * Handles achievement selection and stat calculation
 * Follows the established project patterns for system implementation
 */

// Ensure AchievementData is loaded
(function() {
    // Check if AchievementData is already loaded
    if (typeof window.AchievementData !== 'undefined') {
        return;
    }

    // Function to load a script dynamically
    function loadScript(url) {
        return new Promise((resolve, reject) => {
            const script = document.createElement('script');
            script.src = url;
            script.onload = resolve;
            script.onerror = reject;
            document.head.appendChild(script);
        });
    }

    // Use WordPress plugin URL from global variable
    const basePath = window.forceguidesPlannerData ? forceguidesPlannerData.pluginUrl : '';

    // Load the achievement-data.js file
    loadScript(basePath + 'js/achievement-system/achievement-data.js')
        .then(() => {})
        .catch(error => {});
})();

// Define the system globally to ensure it's always available
window.AchievementSystem = {
    // Track system state
    isInitialized: false,

    // Cache DOM elements
    elements: {},

    // Current active category
    activeCategory: 'quests',

    // Selected achievements data
    selectedAchievements: {},

    // Initialize the system
    init: function() {
        if (this.isInitialized) {
            return;
        }

        // Wait for AchievementData to be available
        if (typeof window.AchievementData === 'undefined') {
            setTimeout(() => this.init(), 100);
            return;
        }

        this.createSystemHTML();
        this.setupEventListeners();
        this.loadFromStore();
        this.isInitialized = true;
    },

    // Create the system HTML structure
    createSystemHTML: function() {
        const container = document.getElementById('fg-achievement-system');
        if (!container) return;

        container.innerHTML = `
            <div class="fg-achievement-system-container">
                <div class="fg-achievement-layout">
                    <div class="fg-achievement-sidebar">
                        ${this.createCategorySidebar()}
                    </div>
                    <div class="fg-achievement-main-content">
                        ${this.createCategoryPanels()}
                    </div>
                </div>
            </div>
        `;

        // Cache elements
        this.elements.container = container;
        this.elements.sidebar = container.querySelector('.fg-achievement-sidebar');
        this.elements.mainContent = container.querySelector('.fg-achievement-main-content');

        // Render initial category
        this.renderCategory(this.activeCategory);
    },

    // Create category sidebar
    createCategorySidebar: function() {
        const categories = window.AchievementData.categories;
        const counts = this.getCategoryCounts();

        let html = '';
        for (const categoryId in categories) {
            const category = categories[categoryId];
            const count = counts[categoryId];
            const isActive = categoryId === this.activeCategory ? 'active' : '';

            html += `
                <div class="fg-achievement-category-item ${isActive}" data-category="${categoryId}">
                    ${category.name}
                    <span class="fg-achievement-category-count">(${count.completed}/${count.total})</span>
                </div>
            `;
        }

        return html;
    },

    // Create category panels
    createCategoryPanels: function() {
        const categories = window.AchievementData.categories;
        let html = '';

        for (const categoryId in categories) {
            const isActive = categoryId === this.activeCategory ? 'active' : '';
            html += `
                <div id="fg-achievement-${categoryId}-content" class="fg-achievement-category-panel ${isActive}">
                    <!-- Content will be populated by renderCategory -->
                </div>
            `;
        }

        return html;
    },

    // Setup event listeners
    setupEventListeners: function() {
        // Category navigation
        this.elements.sidebar.addEventListener('click', (e) => {
            const categoryItem = e.target.closest('.fg-achievement-category-item');
            if (categoryItem) {
                const categoryId = categoryItem.dataset.category;
                this.switchCategory(categoryId);
            }
        });

        // Achievement interactions will be handled by event delegation
        this.elements.mainContent.addEventListener('change', (e) => {
            if (e.target.matches('.fg-achievement-milestone-select')) {
                const achievementId = e.target.dataset.achievementId;
                const milestone = parseInt(e.target.value);
                this.updateMilestone(achievementId, milestone);
            } else if (e.target.matches('.fg-achievement-checkbox')) {
                const achievementId = e.target.dataset.achievementId;
                const completed = e.target.checked;
                this.toggleAchievement(achievementId, completed);
            }
        });
    },

    // Switch active category
    switchCategory: function(categoryId) {
        // Update active category
        this.activeCategory = categoryId;

        // Update sidebar
        this.elements.sidebar.querySelectorAll('.fg-achievement-category-item').forEach(item => {
            item.classList.toggle('active', item.dataset.category === categoryId);
        });

        // Update panels
        this.elements.mainContent.querySelectorAll('.fg-achievement-category-panel').forEach(panel => {
            panel.classList.toggle('active', panel.id === `fg-achievement-${categoryId}-content`);
        });

        // Render category content
        this.renderCategory(categoryId);
    },

    // Render achievements for a category
    renderCategory: function(categoryId) {
        const panel = document.getElementById(`fg-achievement-${categoryId}-content`);
        if (!panel) return;

        const achievements = window.AchievementData.getCategoryAchievements(categoryId);

        if (Object.keys(achievements).length === 0) {
            panel.innerHTML = `
                <div class="fg-achievement-empty-state">
                    ${window.AchievementData.categories[categoryId].name} achievements coming soon...
                </div>
            `;
            return;
        }

        let html = '';
        for (const achievementId in achievements) {
            const achievement = achievements[achievementId];
            html += this.createAchievementCard(achievementId, achievement);
        }

        panel.innerHTML = html;
    },

    // Create achievement card HTML
    createAchievementCard: function(achievementId, achievement) {
        const selectedData = this.selectedAchievements[achievementId] || {};

        if (achievement.type === 'milestone') {
            return this.createMilestoneCard(achievementId, achievement, selectedData);
        } else {
            return this.createSingleCard(achievementId, achievement, selectedData);
        }
    },

    // Create milestone achievement card
    createMilestoneCard: function(achievementId, achievement, selectedData) {
        const currentMilestone = selectedData.milestone || 0;
        const progressPercentage = (currentMilestone / achievement.milestones.length) * 100;

        let milestoneOptions = '<option value="0">Not Started</option>';
        achievement.milestones.forEach((milestone, index) => {
            const selected = currentMilestone === index + 1 ? 'selected' : '';
            milestoneOptions += `
                <option value="${index + 1}" ${selected}>
                    ${milestone.threshold} completions
                </option>
            `;
        });

        return `
            <div class="fg-achievement-card" data-achievement-id="${achievementId}">
                <div class="fg-achievement-header">
                    <div class="fg-achievement-name">${achievement.name}</div>
                    <div class="fg-achievement-type">Milestone</div>
                </div>
                <div class="fg-achievement-progress-container">
                    <div class="fg-achievement-progress-bar">
                        <div class="fg-achievement-progress-fill" style="width: ${progressPercentage}%"></div>
                        <div class="fg-achievement-progress-text">${currentMilestone}/${achievement.milestones.length}</div>
                    </div>
                </div>
                <div class="fg-achievement-milestone-selector">
                    <label>Current Milestone:</label>
                    <select class="fg-achievement-milestone-select" data-achievement-id="${achievementId}">
                        ${milestoneOptions}
                    </select>
                </div>
            </div>
        `;
    },

    // Create single achievement card
    createSingleCard: function(achievementId, achievement, selectedData) {
        const completed = selectedData.completed || false;
        const progressPercentage = completed ? 100 : 0;

        return `
            <div class="fg-achievement-card" data-achievement-id="${achievementId}">
                <div class="fg-achievement-header">
                    <div class="fg-achievement-name">${achievement.name}</div>
                    <div class="fg-achievement-type">Single</div>
                </div>
                <div class="fg-achievement-checkbox-container">
                    <input type="checkbox" class="fg-achievement-checkbox"
                           data-achievement-id="${achievementId}" ${completed ? 'checked' : ''}>
                    <label>Achievement Completed</label>
                </div>
                <div class="fg-achievement-progress-container">
                    <div class="fg-achievement-progress-bar">
                        <div class="fg-achievement-progress-fill" style="width: ${progressPercentage}%"></div>
                        <div class="fg-achievement-progress-text">${completed ? 'Complete' : 'Not Complete'}</div>
                    </div>
                </div>
            </div>
        `;
    },

    // Render stats display using StatIntegrationService
    renderStatsDisplay: function(stats, title) {
        if (!stats || Object.keys(stats).length === 0) {
            return `
                <div class="fg-achievement-stats-display">
                    <div class="fg-achievement-stats-title">${title}:</div>
                    <div class="fg-achievement-empty-state">No stats</div>
                </div>
            `;
        }

        // Use StatIntegrationService for consistent stat display (like pet, honor, costume systems)
        let statsHtml = '';
        if (typeof StatIntegrationService !== 'undefined') {
            statsHtml = StatIntegrationService.createStatSummaryHTML(stats);
        } else {
            // Fallback to basic display if service isn't available
            for (const statId in stats) {
                const value = stats[statId];
                const statInfo = window.StatsConfig ? window.StatsConfig.getStatInfo(statId) : null;
                const statName = statInfo ? statInfo.name : statId;
                const isPercentage = statInfo ? statInfo.isPercentage : false;

                statsHtml += `
                    <div class="fg-achievement-stat-item">
                        <span class="fg-achievement-stat-name">${statName}:</span>
                        <span class="fg-achievement-stat-value">+${value}${isPercentage ? '%' : ''}</span>
                    </div>
                `;
            }
        }

        return `
            <div class="fg-achievement-stats-display">
                <div class="fg-achievement-stats-title">${title}:</div>
                ${statsHtml}
            </div>
        `;
    },

    // Calculate cumulative stats for milestone achievements
    calculateCumulativeStats: function(achievement, currentMilestone) {
        const totalStats = {};

        for (let i = 0; i < currentMilestone; i++) {
            const milestone = achievement.milestones[i];
            if (milestone && milestone.stats) {
                for (const statId in milestone.stats) {
                    totalStats[statId] = (totalStats[statId] || 0) + milestone.stats[statId];
                }
            }
        }

        return totalStats;
    },

    // Update milestone for achievement
    updateMilestone: function(achievementId, milestone) {
        if (!this.selectedAchievements[achievementId]) {
            this.selectedAchievements[achievementId] = {};
        }

        this.selectedAchievements[achievementId].milestone = milestone;

        // Update progress bar immediately
        this.updateProgressBar(achievementId, milestone);

        this.updateAchievementStats();
        this.saveData();
        this.updateCategoryCounts();
    },

    // Update progress bar for a specific achievement
    updateProgressBar: function(achievementId, milestone) {
        const card = this.elements.mainContent.querySelector(`[data-achievement-id="${achievementId}"]`);
        if (!card) return;

        const achievement = window.AchievementData.getAchievementById(achievementId);
        if (!achievement || achievement.type !== 'milestone') return;

        const progressFill = card.querySelector('.fg-achievement-progress-fill');
        const progressText = card.querySelector('.fg-achievement-progress-text');

        if (progressFill && progressText) {
            const progressPercentage = (milestone / achievement.milestones.length) * 100;
            progressFill.style.width = `${progressPercentage}%`;
            progressText.textContent = `${milestone}/${achievement.milestones.length}`;
        }
    },

    // Toggle single achievement
    toggleAchievement: function(achievementId, completed) {
        if (!this.selectedAchievements[achievementId]) {
            this.selectedAchievements[achievementId] = {};
        }

        this.selectedAchievements[achievementId].completed = completed;

        // Update progress bar immediately for single achievements
        this.updateSingleProgressBar(achievementId, completed);

        this.updateAchievementStats();
        this.saveData();
        this.updateCategoryCounts();
    },

    // Update progress bar for single achievement
    updateSingleProgressBar: function(achievementId, completed) {
        const card = this.elements.mainContent.querySelector(`[data-achievement-id="${achievementId}"]`);
        if (!card) return;

        const progressFill = card.querySelector('.fg-achievement-progress-fill');
        const progressText = card.querySelector('.fg-achievement-progress-text');

        if (progressFill && progressText) {
            const progressPercentage = completed ? 100 : 0;
            progressFill.style.width = `${progressPercentage}%`;
            progressText.textContent = completed ? 'Complete' : 'Not Complete';
        }
    },

    // Calculate total stats from all selected achievements
    calculateTotalStats: function() {
        const totalStats = {};

        for (const achievementId in this.selectedAchievements) {
            const selectedData = this.selectedAchievements[achievementId];
            const achievement = window.AchievementData.getAchievementById(achievementId);

            if (!achievement) continue;

            if (achievement.type === 'milestone' && selectedData.milestone) {
                const cumulativeStats = this.calculateCumulativeStats(achievement, selectedData.milestone);
                for (const statId in cumulativeStats) {
                    totalStats[statId] = (totalStats[statId] || 0) + cumulativeStats[statId];
                }
            } else if (achievement.type === 'single' && selectedData.completed) {
                for (const statId in achievement.stats) {
                    totalStats[statId] = (totalStats[statId] || 0) + achievement.stats[statId];
                }
            }
        }

        return totalStats;
    },

    // Update achievement stats in the main planner (using same method as all other systems)
    updateAchievementStats: function() {
        const totalStats = this.calculateTotalStats();

        // Update main planner stats
        if (typeof BuildPlanner !== 'undefined' && BuildPlanner.updateStats) {
            BuildPlanner.updateStats('achievement', totalStats);
        }

        // Update the stats summary in our system UI
        this.updateStatsSummary(totalStats);
    },

    // Update stats summary in UI using StatIntegrationService
    updateStatsSummary: function(stats) {
        // Find or create a summary container in the achievement system
        let summaryContainer = this.elements.container.querySelector('.fg-achievement-stats-summary');
        if (!summaryContainer) {
            // Create summary container if it doesn't exist
            const mainContent = this.elements.container.querySelector('.fg-achievement-main-content');
            if (mainContent) {
                summaryContainer = document.createElement('div');
                summaryContainer.className = 'fg-achievement-stats-summary';
                summaryContainer.innerHTML = `
                    <div class="fg-achievement-stats-title">Total Achievement Stats:</div>
                    <div class="fg-achievement-summary-content"></div>
                `;
                mainContent.appendChild(summaryContainer);
            }
        }

        const summaryContent = summaryContainer ? summaryContainer.querySelector('.fg-achievement-summary-content') : null;
        if (!summaryContent) return;

        // Use StatIntegrationService for consistent stat display (like pet, honor, costume systems)
        if (typeof StatIntegrationService !== 'undefined') {
            summaryContent.innerHTML = StatIntegrationService.createStatSummaryHTML(stats);
        } else {
            // Simple fallback if service isn't available
            summaryContent.innerHTML = '<p class="no-stats">No achievement stats selected yet.</p>';
        }
    },

    // Wrapper method for backward compatibility
    updateStats: function() {
        this.updateAchievementStats();
    },

    // Get category counts for display
    getCategoryCounts: function() {
        const counts = window.AchievementData.getCategoryCounts();

        // Calculate completed counts based on selected achievements
        for (const achievementId in this.selectedAchievements) {
            const selectedData = this.selectedAchievements[achievementId];
            const achievement = window.AchievementData.getAchievementById(achievementId);

            if (!achievement) continue;

            const categoryId = achievement.category;
            if (counts[categoryId]) {
                if (achievement.type === 'milestone' && selectedData.milestone > 0) {
                    counts[categoryId].completed++;
                } else if (achievement.type === 'single' && selectedData.completed) {
                    counts[categoryId].completed++;
                }
            }
        }

        return counts;
    },

    // Update category counts in UI
    updateCategoryCounts: function() {
        const counts = this.getCategoryCounts();

        for (const categoryId in counts) {
            const categoryItem = this.elements.sidebar.querySelector(`[data-category="${categoryId}"]`);
            if (categoryItem) {
                const countSpan = categoryItem.querySelector('.fg-achievement-category-count');
                if (countSpan) {
                    const count = counts[categoryId];
                    countSpan.textContent = `(${count.completed}/${count.total})`;
                }
            }
        }
    },

    // Save data using BuildSaverStore
    saveData: function() {
        if (window.BuildSaverStore && typeof BuildSaverStore.saveData === 'function') {
            BuildSaverStore.saveData();
        }
    },

    // Get essential data for saving
    getEssentialData: function() {
        return {
            selectedAchievements: JSON.parse(JSON.stringify(this.selectedAchievements)),
            activeCategory: this.activeCategory
        };
    },

    // Load data from BuildSaverStore
    loadFromStore: function() {
        if (window.BuildSaverStore && BuildSaverStore.dataLoaded) {
            const systemData = BuildSaverStore.getSystemData('achievement');
            if (systemData) {
                return this.loadFromData(systemData);
            }
        }
        return false;
    },

    // Load from data object
    loadFromData: function(data) {
        if (data.selectedAchievements) {
            this.selectedAchievements = data.selectedAchievements;
        }

        if (data.activeCategory) {
            this.activeCategory = data.activeCategory;
        }

        // Update UI and stats
        this.updateAchievementStats();
        this.updateCategoryCounts();

        // Re-render current category if system is initialized
        if (this.isInitialized) {
            this.renderCategory(this.activeCategory);
        }

        return true;
    }
};

// Initialize when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    // Wait a bit for other systems to load
    setTimeout(() => {
        if (window.AchievementSystem) {
            AchievementSystem.init();
        }
    }, 100);
});
