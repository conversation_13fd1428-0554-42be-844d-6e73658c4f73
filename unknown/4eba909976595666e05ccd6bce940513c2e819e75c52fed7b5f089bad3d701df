/**
 * Force Wing System Data
 * Configuration for Force Wing slots and available stats
 */

const ForceWingData = {
    // Wing level progression data
    wingLevels: {
        normal: { min: 1, max: 100, name: "Normal" },
        rare: { min: 101, max: 200, name: "<PERSON> Wing" },
        unique: { min: 201, max: 300, name: "<PERSON><PERSON>" },
        epic: { min: 301, max: 400, name: "<PERSON>" },
        master: { min: 401, max: 500, name: "Master" },
        legend: { min: 501, max: 600, name: "Legend" } // Not implemented yet
    },

    // Slot configurations - each slot has 6 available stats
    // Based on the training data image for slots 0-5 (image slots 1-6)
    slotConfigs: {
        // Slot 0 (Image Slot 1)
        0: {
            availableStats: [
                {
                    statId: 'pvpIgnorePenetration', // PvP Ignore Penetration
                    maxLevel: 5,
                    values: [2, 4, 6, 7, 15] // Level 1-5 values
                },
                {
                    statId: 'pvpDamageReduce', // PvE Ignore Penetration
                    maxLevel: 5,
                    values: [2, 16, 18, 22, 30]
                },
                {
                    statId: 'pvpIgnorePenetration', // PvP DMG Reduce
                    maxLevel: 5,
                    values: [1, 2, 3, 4, 5]
                },
                {
                    statId: 'pveDamageReduce', // PvE DMG Reduce
                    maxLevel: 5,
                    values: [3, 30, 26, 34, 50]
                }
            ]
        },

        // Slot 1 (Image Slot 2)
        1: {
            availableStats: [
                {
                    statId: 'pvpIgnorePenetration', // PvP Ignore Penetration
                    maxLevel: 5,
                    values: [1, 2, 3, 4, 5]
                },
                {
                    statId: 'pveIgnorePenetration', // PvE Ignore Penetration
                    maxLevel: 5,
                    values: [3, 16, 18, 22, 30]
                },
                {
                    statId: 'pvpDamageReduce', // PvP DMG Reduce
                    maxLevel: 5,
                    values: [2, 16, 22, 34, 52]
                },
                {
                    statId: 'pveDamageReduce', // PvE DMG Reduce
                    maxLevel: 5,
                    values: [20, 21, 31, 45, 80]
                }
            ]
        },

        // Slot 2 (Image Slot 3)
        2: {
            availableStats: [
                {
                    statId: 'pveDefense', // PvE Defense
                    maxLevel: 5,
                    values: [10, 30, 42, 54, 75]
                },
                {
                    statId: 'hp', // HP
                    maxLevel: 5,
                    values: [25, 120, 150, 180, 250]
                }
            ]
        },

        // Slot 3 (Image Slot 4)
        3: {
            availableStats: [
                {
                    statId: 'pveDefense', // PvE Defense
                    maxLevel: 5,
                    values: [10, 30, 42, 54, 75]
                },
                {
                    statId: 'hp', // HP
                    maxLevel: 5,
                    values: [16, 180, 210, 250, 350]
                }
            ]
        },

        // Slot 4 (Image Slot 5)
        4: {
            availableStats: [
                {
                    statId: 'ignoreResistStun', // Ignore Resist Stun
                    maxLevel: 4,
                    values: [1, 2, 4, 8]
                },
                {
                    statId: 'resistDown', // Resist Down
                    maxLevel: 4,
                    values: [2, 3, 6, 16]
                }
            ]
        },

        // Slot 5 (Image Slot 6)
        5: {
            availableStats: [
                {
                    statId: 'resistStun', // Resist Stun
                    maxLevel: 4,
                    values: [1, 2, 4, 8]
                },
                {
                    statId: 'resistCritRate', // Resist Crit. Rate
                    maxLevel: 4,
                    values: [1, 2, 75, 14] // Note: Level 3 shows 75 which seems unusual
                }
            ]
        },

        // Slot 6 (Training Slot 7)
        6: {
            availableStats: [
                {
                    statId: 'pvpNormalDamageUp', // PvP Normal DMG Up
                    maxLevel: 4,
                    values: [1, 2, 3, 6]
                },
                {
                    statId: 'pvpAddDamage', // PvP Add DMG
                    maxLevel: 5,
                    values: [3, 8, 12, 20, 35]
                },
                {
                    statId: 'pveNormalDamageUp', // PvE Normal DMG Up
                    maxLevel: 4,
                    values: [2, 4, 6, 12]
                },
                {
                    statId: 'pveAddDamage', // PvE Add DMG
                    maxLevel: 5,
                    values: [8, 20, 32, 48, 75]
                }
            ]
        },

        // Slot 7 (Training Slot 8)
        7: {
            availableStats: [
                {
                    statId: 'pveNormalDamageUp', // PvE Normal DMG Up
                    maxLevel: 4,
                    values: [1, 2, 3, 6]
                },
                {
                    statId: 'pveAddDamage', // PvE Add DMG
                    maxLevel: 5,
                    values: [3, 8, 12, 20, 35]
                },
                {
                    statId: 'pvpNormalDamageUp', // PvP Normal DMG Up
                    maxLevel: 4,
                    values: [2, 4, 6, 12]
                },
                {
                    statId: 'pvpAddDamage', // PvP Add DMG
                    maxLevel: 5,
                    values: [8, 20, 32, 48, 75]
                }
            ]
        },

        // Slot 8 (Training Slot 9)
        8: {
            availableStats: [
                {
                    statId: 'pvpAllAttackUp', // PvP All Attack Up
                    maxLevel: 5,
                    values: [4, 8, 12, 18, 30]
                },
                {
                    statId: 'pvpAllSkillAmp', // PvP All Skill Amp.
                    maxLevel: 3,
                    values: [1, 2, 4]
                },
                {
                    statId: 'pveAllAttackUp', // PvE All Attack Up
                    maxLevel: 5,
                    values: [6, 14, 22, 32, 50]
                },
                {
                    statId: 'pveAllSkillAmp', // PvE All Skill Amp.
                    maxLevel: 3,
                    values: [2, 4, 8]
                }
            ]
        },

        // Slot 9 (Training Slot 10)
        9: {
            availableStats: [
                {
                    statId: 'pveAllAttackUp', // PvE All Attack Up
                    maxLevel: 5,
                    values: [4, 8, 12, 18, 30]
                },
                {
                    statId: 'pveAllSkillAmp', // PvE All Skill Amp.
                    maxLevel: 3,
                    values: [1, 2, 4]
                },
                {
                    statId: 'pvpAllAttackUp', // PvP All Attack Up
                    maxLevel: 5,
                    values: [6, 14, 22, 32, 50]
                },
                {
                    statId: 'pvpAllSkillAmp', // PvP All Skill Amp.
                    maxLevel: 3,
                    values: [2, 4, 8]
                }
            ]
        },

        // Slot 10 (Training Slot 11)
        10: {
            availableStats: [
                {
                    statId: 'pvpPenetration', // PvP Penetration
                    maxLevel: 4,
                    values: [2, 5, 9, 15]
                },
                {
                    statId: 'pvpCritDamage', // PvP Crit. DMG
                    maxLevel: 3,
                    values: [1, 4, 8]
                },
                {
                    statId: 'pvePenetration', // PvE Penetration
                    maxLevel: 5,
                    values: [3, 7, 12, 16, 25]
                },
                {
                    statId: 'pveCritDamage', // PvE Crit. DMG
                    maxLevel: 4,
                    values: [2, 4, 8, 16]
                }
            ]
        },

        // Slot 11 (Training Slot 12)
        11: {
            availableStats: [
                {
                    statId: 'pvePenetration', // PvE Penetration
                    maxLevel: 4,
                    values: [2, 5, 9, 15]
                },
                {
                    statId: 'pveCritDamage', // PvE Crit. DMG
                    maxLevel: 3,
                    values: [1, 4, 8]
                },
                {
                    statId: 'pvpPenetration', // PvP Penetration
                    maxLevel: 5,
                    values: [3, 7, 12, 16, 25]
                },
                {
                    statId: 'pvpCritDamage', // PvP Crit. DMG
                    maxLevel: 4,
                    values: [2, 4, 8, 16]
                }
            ]
        }
    },

    // Helper function to get wing level tier name
    getWingTierName: function(level) {
        for (const [tier, data] of Object.entries(this.wingLevels)) {
            if (level >= data.min && level <= data.max) {
                return data.name;
            }
        }
        return "Unknown";
    },

    // Helper function to check if a wing level is implemented
    isWingLevelImplemented: function(level) {
        // Legend tier (501-600) is not implemented yet
        return level <= 500;
    },

    // Helper function to get available stats for a slot
    getSlotStats: function(slotId) {
        return this.slotConfigs[slotId]?.availableStats || [];
    },

    // Helper function to get stat value at specific level
    getStatValue: function(slotId, statId, level) {
        const slotConfig = this.slotConfigs[slotId];
        if (!slotConfig) return 0;

        const statConfig = slotConfig.availableStats.find(stat => stat.statId === statId);
        if (!statConfig) return 0;

        // Level is 1-based, array is 0-based
        const valueIndex = level - 1;
        if (valueIndex < 0 || valueIndex >= statConfig.values.length) return 0;

        return statConfig.values[valueIndex] || 0;
    }
};

// Make it globally available
window.ForceWingData = ForceWingData;
