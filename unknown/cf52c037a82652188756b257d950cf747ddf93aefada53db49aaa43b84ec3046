/**
 * Achievement System Data
 * Contains all achievement definitions organized by categories
 */

// Define the achievement system data globally to ensure it's always available
window.AchievementData = {
    // Achievement categories with their achievements
    categories: {
        quests: {
            name: "Quests",
            achievements: {
                // Quest achievements will be added here
            }
        },
        dungeons: {
            name: "Dungeon",
            achievements: {
                "illusion-castle-underworld": {
                    name: "Illusion Castle - Underworld",
                    type: "milestone",
                    milestones: [
                        { threshold: 100, stats: { "defenseRate": 2 } },
                        { threshold: 500, stats: { "defenseRate": 4 } },
                        { threshold: 1000, stats: { "defenseRate": 6 } },
                        { threshold: 5000, stats: { "defenseRate": 8 } }
                    ]
                },
                "illusion-castle-radiant": {
                    name: "Illusion Castle - Radiant Hall",
                    type: "milestone",
                    milestones: [
                        { threshold: 100, stats: { "attackRate": 2 } },
                        { threshold: 500, stats: { "attackRate": 4 } },
                        { threshold: 1000, stats: { "attackRate": 6 } },
                        { threshold: 5000, stats: { "attackRate": 8 } }
                    ]
                },
                "illusion-castle-underworld-apocrypha": {
                    name: "Illusion Castle - Underworld (Apocrypha)",
                    type: "milestone",
                    milestones: [
                        { threshold: 100, stats: { "resistSuppression": 1 } },
                        { threshold: 500, stats: { "resistSuppression": 1 } },
                        { threshold: 1000, stats: { "resistSuppression": 1 } },
                        { threshold: 5000, stats: { "resistSuppression": 2 } }
                    ]
                },
                "illusion-castle-radiant-apocrypha": {
                    name: "Illusion Castle - Radiant Hall (Apocrypha)",
                    type: "milestone",
                    milestones: [
                        { threshold: 100, stats: { "resistSilence": 1 } },
                        { threshold: 500, stats: { "resistSilence": 1 } },
                        { threshold: 1000, stats: { "resistSilence": 1 } },
                        { threshold: 5000, stats: { "resistSilence": 2 } }
                    ]
                },
                "forgotten-temple-b1f": {
                    name: "Forgotten Temple B1F",
                    type: "milestone",
                    milestones: [
                        { threshold: 100, stats: { "hp": 4 } },
                        { threshold: 500, stats: { "hp": 6 } },
                        { threshold: 1000, stats: { "hp": 8 } },
                        { threshold: 5000, stats: { "hp": 12 } }
                    ]
                },
                "forgotten-temple-b2f": {
                    name: "Forgotten Temple B2F",
                    type: "milestone",
                    milestones: [
                        { threshold: 100, stats: { "accuracy": 5 } },
                        { threshold: 500, stats: { "accuracy": 10 } },
                        { threshold: 1000, stats: { "accuracy": 15 } },
                        { threshold: 5000, stats: { "accuracy": 20 } }
                    ]
                },
                "forgotten-temple-b2f-awakening": {
                    name: "Forgotten Temple B2F (Awakening)",
                    type: "milestone",
                    milestones: [
                        { threshold: 100, stats: { "resistCritDmg": 2 } },
                        { threshold: 500, stats: { "resistCritDmg": 2 } },
                        { threshold: 1000, stats: { "resistCritDmg": 2 } },
                        { threshold: 5000, stats: { "resistCritDmg": 2 } }
                    ]
                },
                "forgotten-temple-b3f": {
                    name: "Forgotten Temple B3F",
                    type: "milestone",
                    milestones: [
                        { threshold: 100, stats: { "allAttackUp": 4 } },
                        { threshold: 500, stats: { "allAttackUp": 6, "penetration": 3 } },
                        { threshold: 1000, stats: { "allAttackUp": 8, "penetration": 5 } },
                        { threshold: 5000, stats: { "allAttackUp": 12, "penetration": 7 } }
                    ]
                }
            }
        },
        pvp: {
            name: "PvP",
            achievements: {
                // PvP achievements will be added here
            }
        },
        "mission-war": {
            name: "Mission War",
            achievements: {
                // Mission War achievements will be added here
            }
        },
        hunting: {
            name: "Hunting",
            achievements: {
                // Hunting achievements will be added here
            }
        },
        shared: {
            name: "Shared Achievements",
            achievements: {
                // Shared achievements will be added here
            }
        },
        normal: {
            name: "Normal",
            achievements: {
                "tutorial-master": {
                    name: "Tutorial Master",
                    type: "single",
                    stats: { "exp": 10, "movementSpeed": 5 }
                },
                "world-explorer": {
                    name: "World Explorer",
                    type: "single",
                    stats: { "movementSpeed": 10, "exp": 5 }
                }
            }
        }
    },

    // Helper function to get all achievements from a category
    getCategoryAchievements: function(categoryId) {
        return this.categories[categoryId] ? this.categories[categoryId].achievements : {};
    },

    // Helper function to get achievement by ID across all categories
    getAchievementById: function(achievementId) {
        for (const categoryId in this.categories) {
            const achievements = this.categories[categoryId].achievements;
            if (achievements[achievementId]) {
                return {
                    ...achievements[achievementId],
                    id: achievementId,
                    category: categoryId
                };
            }
        }
        return null;
    },

    // Helper function to get category counts (for UI display)
    getCategoryCounts: function() {
        const counts = {};
        for (const categoryId in this.categories) {
            const achievements = this.categories[categoryId].achievements;
            counts[categoryId] = {
                total: Object.keys(achievements).length,
                completed: 0 // Will be calculated by the system based on user selections
            };
        }
        return counts;
    }
};

// Ensure the data is available globally
if (typeof window !== 'undefined') {
    window.AchievementData = window.AchievementData || AchievementData;
}
