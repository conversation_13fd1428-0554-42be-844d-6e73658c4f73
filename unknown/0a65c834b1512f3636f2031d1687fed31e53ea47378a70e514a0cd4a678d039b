/**
 * Build Saver
 * Provides functionality to save and load builds from localStorage
 */

// Create a central store for build data
window.BuildSaverStore = {
    // Loaded build data
    buildData: null,

    // Whether data has been loaded
    dataLoaded: false,

    // Get data for a specific system
    getSystemData: function(systemId) {
        if (!this.dataLoaded || !this.buildData || !this.buildData.systems) {
            return null;
        }

        // Only check for exact match
        return this.buildData.systems[systemId] || null;
    },

    // Store the build data
    setBuildData: function(data) {
        this.buildData = data;
        this.dataLoaded = true;
    },

    // Get data for a system - compatibility method for equipment system
    getData: function(systemId) {
        return this.getSystemData(systemId);
    },

    // Save data for a system - will be used by saveBuild
    saveData: function(systemId, data) {
        if (!this.buildData) {
            this.buildData = {
                timestamp: Date.now(),
                version: '1.0',
                systems: {}
            };
        }

        if (!this.buildData.systems) {
            this.buildData.systems = {};
        }

        this.buildData.systems[systemId] = data;
        this.dataLoaded = true;

        // Auto-save removed - only manual saves now
    }
};

// Initialize the build saver namespace
window.BuildSaver = {
    // Key for storing the build in localStorage
    STORAGE_KEY: 'fg_current_build',

    // Initialize the build saver
    init: function() {
        // Load any saved build data immediately
        this.loadSavedBuildToStore();

        // Create and append the save button to the UI
        this.createSaveButton();

        // Setup event listeners
        this.setupEventListeners();
    },

    // Create the save button UI
    createSaveButton: function() {
        // Create the save build button
        const saveButton = document.createElement('div');
        saveButton.className = 'fg-system-button fg-save-build-button';
        saveButton.id = 'fg-save-build-btn';
        saveButton.innerHTML = `
            <span class="icon save-icon">
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="24" height="24" fill="white">
                    <path d="M17 3H5c-1.11 0-2 .9-2 2v14c0 1.1.89 2 2 2h14c1.1 0 2-.9 2-2V7l-4-4zm-5 16c-1.66 0-3-1.34-3-3s1.34-3 3-3 3 1.34 3 3-1.34 3-3 3zm3-10H5V5h10v4z"/>
                </svg>
            </span>
            <span>Save Build</span>
        `;

        // Add CSS styles for the save button
        const style = document.createElement('style');
        style.textContent = `
            .fg-save-build-button {
                background-color: #ff5500 !important;
                margin-top: 20px !important;
                border-top: 1px solid #3a3a48;
                padding-top: 20px !important;
            }

            .fg-save-build-button:hover {
                background-color: #ff7722 !important;
            }

            .fg-save-notification {
                position: fixed;
                top: 20px;
                right: 20px;
                background: #2a2a36;
                color: #fff;
                padding: 12px 16px;
                border-radius: 4px;
                box-shadow: 0 4px 12px rgba(0,0,0,0.2);
                z-index: 9999;
                transform: translateY(-100px);
                opacity: 0;
                transition: all 0.3s ease;
            }

            .fg-save-notification.show {
                transform: translateY(0);
                opacity: 1;
            }
        `;
        document.head.appendChild(style);

        // Append to the system buttons container
        const buttonsContainer = document.querySelector('.fg-system-buttons');
        if (buttonsContainer) {
            buttonsContainer.appendChild(saveButton);
        }
    },

    // Set up event listeners
    setupEventListeners: function() {
        // Save button click handler
        const saveButton = document.getElementById('fg-save-build-btn');
        if (saveButton) {
            saveButton.addEventListener('click', () => {
                this.saveBuild();
                this.showNotification('Build saved successfully!');
            });
        }
    },

    // Load the saved build from localStorage into the central store
    loadSavedBuildToStore: function() {
        const savedBuild = localStorage.getItem(this.STORAGE_KEY);
        if (!savedBuild) {
            return false;
        }

        try {
            // Parse build data
            const buildData = JSON.parse(savedBuild);

            // Store in the central repository
            BuildSaverStore.setBuildData(buildData);

            return true;
        } catch (error) {
            return false;
        }
    },

    // Save the current build to localStorage
    saveBuild: function() {
        try {
            // Ensure data is properly structured
            this.verifySystemsData();

            // Capture the current build state
            const buildData = this.captureBuildState();

            // Count saved systems
            const savedSystemsCount = Object.keys(buildData.systems).length;

            if (savedSystemsCount === 0) {
                this.showNotification('No data found to save. Have you made any changes?', true);
                return false;
            }

            // Save to localStorage
            localStorage.setItem(this.STORAGE_KEY, JSON.stringify(buildData));

            // Update the central store
            BuildSaverStore.setBuildData(buildData);

            return true;
        } catch (error) {
            this.showNotification('Failed to save build: ' + error.message, true);
            return false;
        }
    },

    // Verify data structure for all systems before saving
    verifySystemsData: function() {
        // Verify Essence Runes System
        if (window.EssenceRunesSystem && window.EssenceRunesSystem.isInitialized) {
            if (!window.EssenceRunesSystem.data) {
                window.EssenceRunesSystem.data = {};
            }

            if (!window.EssenceRunesSystem.data.selectedRunes) {
                window.EssenceRunesSystem.data.selectedRunes = [];
            }

            if (!Array.isArray(window.EssenceRunesSystem.data.selectedRunes)) {
                window.EssenceRunesSystem.data.selectedRunes = [];
            }
        }

        // Verify Honor Medal System
        if (window.HonorMedalSystem && window.HonorMedalSystem.isInitialized) {
            if (!window.HonorMedalSystem.selectedStats) {
                window.HonorMedalSystem.selectedStats = {
                    captain: Array(4).fill(null),
                    general: Array(6).fill(null),
                    commander: Array(8).fill(null),
                    hero: Array(10).fill(null),
                    legend: Array(12).fill(null)
                };
            }

            // Ensure all rank arrays exist
            const requiredRanks = ['captain', 'general', 'commander', 'hero', 'legend'];
            requiredRanks.forEach(rankId => {
                if (!window.HonorMedalSystem.selectedStats[rankId]) {
                    window.HonorMedalSystem.selectedStats[rankId] = Array(
                        rankId === 'captain' ? 4 :
                        rankId === 'general' ? 6 :
                        rankId === 'commander' ? 8 :
                        rankId === 'hero' ? 10 : 12
                    ).fill(null);
                }
            });
        }

        // Verify Costume System
        if (window.CostumesSystem && window.CostumesSystem.isInitialized) {
            if (!window.CostumesSystem.selectedStats) {
                window.CostumesSystem.selectedStats = {
                    generic: Array(3).fill(null),
                    forceWing: Array(3).fill(null),
                    vehicle: Array(3).fill(null)
                };
            }

            // Ensure all costume type arrays exist
            const costumeTypes = ['generic', 'forceWing', 'vehicle'];
            costumeTypes.forEach(type => {
                if (!window.CostumesSystem.selectedStats[type]) {
                    window.CostumesSystem.selectedStats[type] = Array(3).fill(null);
                }
            });

            // Ensure epicCraftOptions exists
            if (!window.CostumesSystem.epicCraftOptions) {
                window.CostumesSystem.epicCraftOptions = {
                    generic: null,
                    forceWing: null,
                    vehicle: null
                };
            }
        }

        // Verify Pet System (add if needed)
        // No special verification needed for Pet System currently
    },

    // Capture the current state of all systems
    captureBuildState: function() {
        const buildData = {
            timestamp: Date.now(),
            version: '1.0',
            systems: {}
        };

        // Define which systems to check
        const systemsToCheck = ['PetSystem', 'EssenceRunesSystem', 'KarmaRunesSystem', 'EquipmentSystem', 'ClassSystem', 'StellarSystem', 'HonorMedalSystem', 'CostumesSystem', 'OverlordMasterySystem'];

        // Try to get essential data from each system
        systemsToCheck.forEach(systemName => {
            // Get the system from the window
            const system = window[systemName];

            // Check if system exists
            if (!system) {
                console.warn(`${systemName} not found or not initialized.`);
                return;
            }

            // Get system ID from name (convert PetSystem -> pet, EssenceRunesSystem -> essence-runes)
            const systemId = systemName.replace('System', '')
                .replace(/([a-z])([A-Z])/g, '$1-$2')
                .toLowerCase();

            if (typeof system.getEssentialData === 'function') {
                try {
                    const data = system.getEssentialData();
                    buildData.systems[systemId] = data;
                } catch (error) {
                }
            }
        });

        return buildData;
    },

    // Show a notification message
    showNotification: function(message, isError = false) {
        // Remove any existing notifications
        const existingNotification = document.querySelector('.fg-save-notification');
        if (existingNotification) {
            existingNotification.remove();
        }

        // Create notification element
        const notification = document.createElement('div');
        notification.className = 'fg-save-notification';
        notification.innerHTML = message;

        if (isError) {
            notification.style.backgroundColor = '#8B0000';
        }

        // Add to DOM
        document.body.appendChild(notification);

        // Show notification with animation
        setTimeout(() => {
            notification.classList.add('show');
        }, 10);

        // Remove after delay
        setTimeout(() => {
            notification.classList.remove('show');
            setTimeout(() => {
                notification.remove();
            }, 300);
        }, 3000);
    }
};

// Initialize when document is ready
document.addEventListener('DOMContentLoaded', () => {
    // Only initialize if we're on the build planner page
    if (document.querySelector('.fg-build-planner-container')) {
        BuildSaver.init();
    }
});