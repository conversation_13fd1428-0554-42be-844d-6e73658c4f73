/**
 * Build Compression
 * Provides ultra-compact binary compression for build sharing
 * Significantly reduces URL length compared to standard JSON+LZString
 * Supports multiple systems with an extensible architecture
 */

window.BuildCompression = {
    // Version of the compression algorithm
    version: 3,

    // Registry of system encoders and decoders
    systems: {
        // System ID to encoder/decoder mapping
        honor: {
            id: 'honor',           // System identifier
            prefix: 'h',          // Single-character prefix for this system
            encode: null,          // Will be set to encodeHonorSystem
            decode: null,          // Will be set to decodeHonorSystem
            priority: 1            // Priority for encoding (lower = higher priority)
        },
        pet: {
            id: 'pet',
            prefix: 'p',
            encode: null,          // Will be set to encodePetSystem
            decode: null,          // Will be set to decodePetSystem
            priority: 2
        }
        // Additional systems can be registered here
    },

    /**
     * Honor Medal System specific binary encoding
     * Converts the Honor Medal System data to a highly compact binary format
     */
    encodeHonorSystem: function(honorData) {
        if (!honorData || !honorData.selectedStats) {
            return null;
        }

        // Create a binary buffer
        let buffer = [];

        // Add version (1 byte)
        buffer.push(this.version);

        // Process each rank
        for (const rankId in honorData.selectedStats) {
            const slots = honorData.selectedStats[rankId];
            if (!slots || !slots.length) continue;

            // Check if any slots in this rank have data
            const hasData = slots.some(slot => slot !== null && slot !== undefined && slot.statId);
            if (!hasData) continue;

            // Add rank ID (1 byte)
            const rankIds = {'captain': 0, 'general': 1, 'commander': 2, 'hero': 3, 'legend': 4};
            buffer.push(rankIds[rankId] || 0);

            // Check if all filled slots have the same stat and level
            const firstFilledSlot = slots.find(slot => slot !== null && slot !== undefined && slot.statId);
            const allSame = firstFilledSlot && slots.every(slot =>
                !slot || !slot.statId || (slot.statId === firstFilledSlot.statId && slot.level === firstFilledSlot.level)
            );

            // Add slot bitmap (2 bytes)
            let slotBitmap = 0;
            slots.forEach((slot, index) => {
                if (slot && slot.statId) {
                    slotBitmap |= (1 << index);
                }
            });

            // Add bitmap and flag for all-same optimization
            buffer.push(((allSame ? 1 : 0) << 7) | ((slotBitmap >> 8) & 0x7F)); // High byte with flag
            buffer.push(slotBitmap & 0xFF);                                     // Low byte

            if (allSame && firstFilledSlot) {
                // All slots have the same stat and level - just add one entry
                const statIds = {
                    'hp': 0, 'mp': 1, 'attack': 2, 'defense': 3, 'attackRate': 4,
                    'defenseRate': 5, 'str': 6, 'int': 7, 'dex': 8, 'penetration': 9,
                    'accuracy': 10, 'evasion': 11, 'critRate': 12, 'critDamage': 13,
                    'allAttackUp': 14, 'hpAutoHeal': 15, 'mpAutoHeal': 16,
                    'ignoreEvasion': 17, 'ignoreAccuracy': 18, 'ignoreDamageReduce': 19,
                    'ignoreResistCritDmg': 20, 'addDamage': 21,
                    'cancelIgnorePenetration': 22, 'cancelIgnoreDefense': 23,
                    'cancelIgnoreEvasion': 24, 'cancelIgnoreAccuracy': 25,
                    'cancelIgnoreDamageReduce': 26, 'cancelIgnoreResistCritDmg': 27
                };

                // Add stat ID (1 byte)
                buffer.push(statIds[firstFilledSlot.statId] || 0);

                // Add level (1 byte)
                buffer.push(firstFilledSlot.level);
            } else {
                // For each filled slot
                slots.forEach(slot => {
                    if (!slot || !slot.statId) return;

                    // Get stat ID
                    const statIds = {
                        'hp': 0, 'mp': 1, 'attack': 2, 'defense': 3, 'attackRate': 4,
                        'defenseRate': 5, 'str': 6, 'int': 7, 'dex': 8, 'penetration': 9,
                        'accuracy': 10, 'evasion': 11, 'critRate': 12, 'critDamage': 13,
                        'allAttackUp': 14, 'hpAutoHeal': 15, 'mpAutoHeal': 16,
                        'ignoreEvasion': 17, 'ignoreAccuracy': 18, 'ignoreDamageReduce': 19,
                        'ignoreResistCritDmg': 20, 'addDamage': 21,
                        'cancelIgnorePenetration': 22, 'cancelIgnoreDefense': 23,
                        'cancelIgnoreEvasion': 24, 'cancelIgnoreAccuracy': 25,
                        'cancelIgnoreDamageReduce': 26, 'cancelIgnoreResistCritDmg': 27
                    };

                    // Add stat ID (1 byte)
                    buffer.push(statIds[slot.statId] || 0);

                    // Add level (1 byte)
                    buffer.push(slot.level);
                });
            }
        }

        // Convert to base64url
        return this.arrayToBase64Url(buffer);
    },

    /**
     * Honor Medal System specific binary decoding
     */
    decodeHonorSystem: function(encodedData) {
        if (!encodedData) {
            return null;
        }

        // Convert from base64url to array
        const buffer = this.base64UrlToArray(encodedData);

        // Initialize result structure
        const result = {
            selectedStats: {
                captain: Array(4).fill(null),
                general: Array(6).fill(null),
                commander: Array(8).fill(null),
                hero: Array(10).fill(null),
                legend: Array(12).fill(null)
            }
        };

        // Get version (not used currently but kept for future compatibility)
        const version = buffer[0]; // eslint-disable-line no-unused-vars

        // Current position in the buffer
        let pos = 1;

        // Process the buffer
        while (pos < buffer.length) {
            // Get rank ID
            const rankIdNum = buffer[pos++];
            const rankIds = {0: 'captain', 1: 'general', 2: 'commander', 3: 'hero', 4: 'legend'};
            const rankId = rankIds[rankIdNum] || 'captain';

            // Get slot bitmap and all-same flag
            const bitmapHighWithFlag = buffer[pos++];
            const allSame = (bitmapHighWithFlag & 0x80) !== 0;
            const slotBitmapHigh = bitmapHighWithFlag & 0x7F;
            const slotBitmapLow = buffer[pos++];
            const slotBitmap = (slotBitmapHigh << 8) | slotBitmapLow;

            if (allSame) {
                // All slots have the same stat and level
                const statIdNum = buffer[pos++];
                const level = buffer[pos++];

                const statIds = {
                    0: 'hp', 1: 'mp', 2: 'attack', 3: 'defense', 4: 'attackRate',
                    5: 'defenseRate', 6: 'str', 7: 'int', 8: 'dex', 9: 'penetration',
                    10: 'accuracy', 11: 'evasion', 12: 'critRate', 13: 'critDamage',
                    14: 'allAttackUp', 15: 'hpAutoHeal', 16: 'mpAutoHeal',
                    17: 'ignoreEvasion', 18: 'ignoreAccuracy', 19: 'ignoreDamageReduce',
                    20: 'ignoreResistCritDmg', 21: 'addDamage',
                    22: 'cancelIgnorePenetration', 23: 'cancelIgnoreDefense',
                    24: 'cancelIgnoreEvasion', 25: 'cancelIgnoreAccuracy',
                    26: 'cancelIgnoreDamageReduce', 27: 'cancelIgnoreResistCritDmg'
                };
                const statId = statIds[statIdNum] || 'hp';

                // Fill all slots that are in the bitmap
                for (let slotIndex = 0; slotIndex < 16; slotIndex++) {
                    if ((slotBitmap & (1 << slotIndex)) === 0) continue;

                    if (result.selectedStats[rankId] && slotIndex < result.selectedStats[rankId].length) {
                        result.selectedStats[rankId][slotIndex] = {
                            statId: statId,
                            level: level
                        };
                    }
                }
            } else {
                // Process each slot
                for (let slotIndex = 0; slotIndex < 16; slotIndex++) {
                    // Check if this slot is filled
                    if ((slotBitmap & (1 << slotIndex)) === 0) continue;

                    // Get stat ID
                    const statIdNum = buffer[pos++];
                    const statIds = {
                        0: 'hp', 1: 'mp', 2: 'attack', 3: 'defense', 4: 'attackRate',
                        5: 'defenseRate', 6: 'str', 7: 'int', 8: 'dex', 9: 'penetration',
                        10: 'accuracy', 11: 'evasion', 12: 'critRate', 13: 'critDamage',
                        14: 'allAttackUp', 15: 'hpAutoHeal', 16: 'mpAutoHeal',
                        17: 'ignoreEvasion', 18: 'ignoreAccuracy', 19: 'ignoreDamageReduce',
                        20: 'ignoreResistCritDmg', 21: 'addDamage',
                        22: 'cancelIgnorePenetration', 23: 'cancelIgnoreDefense',
                        24: 'cancelIgnoreEvasion', 25: 'cancelIgnoreAccuracy',
                        26: 'cancelIgnoreDamageReduce', 27: 'cancelIgnoreResistCritDmg'
                    };
                    const statId = statIds[statIdNum] || 'hp';

                    // Get level
                    const level = buffer[pos++];

                    // Add to result
                    if (result.selectedStats[rankId] && slotIndex < result.selectedStats[rankId].length) {
                        result.selectedStats[rankId][slotIndex] = {
                            statId: statId,
                            level: level
                        };
                    }
                }
            }
        }

        return result;
    },

    /**
     * Convert array of bytes to base64url
     */
    arrayToBase64Url: function(array) {
        const binary = String.fromCharCode.apply(null, array);
        const base64 = btoa(binary);
        // Make base64 URL-safe
        return base64.replace(/\+/g, '-').replace(/\//g, '_').replace(/=+$/, '');
    },

    /**
     * Convert base64url to array of bytes
     */
    base64UrlToArray: function(base64url) {
        // Restore standard base64
        const base64 = base64url.replace(/-/g, '+').replace(/_/g, '/');
        const binary = atob(base64);
        const array = new Uint8Array(binary.length);
        for (let i = 0; i < binary.length; i++) {
            array[i] = binary.charCodeAt(i);
        }
        return array;
    },

    /**
     * Encode build data with custom compression
     * Uses system-specific encoders for supported systems
     */
    encodeBuildData: function(buildData) {
        if (!buildData || !buildData.systems) {
            return null;
        }

        // Check if we have Honor Medal System data
        if (buildData.systems.honor) {
            // Use binary Honor Medal System encoder
            const honorEncoded = this.encodeHonorSystem(buildData.systems.honor);
            if (honorEncoded) {
                // Honor Medal System only - use the direct encoded string with prefix
                return 'h' + honorEncoded;
            }
        }

        // Fallback to standard compression if no Honor Medal System data or encoding failed
        const jsonData = JSON.stringify(buildData);
        return LZString.compressToBase64(jsonData);
    },

    /**
     * Pet System specific binary encoding
     * Converts the Pet System data to a highly compact binary format
     */
    encodePetSystem: function(petData) {
        if (!petData || !petData.selectedStats) {
            return null;
        }

        // Create a binary buffer
        let buffer = [];

        // Add version (1 byte)
        buffer.push(this.version);

        // Process each tier
        const tiers = ['normal', 'covenant', 'trust'];

        for (let tierIndex = 0; tierIndex < tiers.length; tierIndex++) {
            const tier = tiers[tierIndex];
            const slots = petData.selectedStats[tier];

            if (!slots || !slots.length) continue;

            // Check if any slots in this tier have data
            const hasData = slots.some(slot => slot !== null && slot !== undefined);
            if (!hasData) continue;

            // Add tier ID (1 byte)
            buffer.push(tierIndex);

            // Check if all filled slots have the same stat
            const firstFilledSlot = slots.find(slot => slot !== null && slot !== undefined);
            const allSame = firstFilledSlot && slots.every(slot =>
                !slot || (slot.id === firstFilledSlot.id && slot.value === firstFilledSlot.value)
            );

            // Add slot bitmap (2 bytes)
            let slotBitmap = 0;
            slots.forEach((slot, index) => {
                if (slot) {
                    slotBitmap |= (1 << index);
                }
            });

            // Add bitmap and flag for all-same optimization
            buffer.push(((allSame ? 1 : 0) << 7) | ((slotBitmap >> 8) & 0x7F)); // High byte with flag
            buffer.push(slotBitmap & 0xFF);                                     // Low byte

            if (allSame && firstFilledSlot) {
                // All slots have the same stat - just add one entry
                // Get stat ID mapping from PetSystemData if available
                let statIdMap = {};
                let statId = 0;

                // Try to get the stat ID from a predefined map or assign a numeric ID
                if (window.PetSystemData && window.PetSystemData.statIdMap) {
                    statIdMap = window.PetSystemData.statIdMap;
                    statId = statIdMap[firstFilledSlot.id] || 0;
                } else {
                    // Fallback to common stat IDs if possible
                    const commonStatIds = {
                        'hp': 0, 'mp': 1, 'attack': 2, 'defense': 3, 'attackRate': 4,
                        'defenseRate': 5, 'str': 6, 'int': 7, 'dex': 8, 'penetration': 9,
                        'accuracy': 10, 'evasion': 11, 'critRate': 12, 'critDamage': 13,
                        'allAttackUp': 14, 'hpAutoHeal': 15, 'mpAutoHeal': 16
                    };
                    statId = commonStatIds[firstFilledSlot.id] || 0;
                }

                // Add stat ID (1 byte)
                buffer.push(statId);

                // Add value (2 bytes - to support larger values)
                const value = firstFilledSlot.value || 0;
                buffer.push((value >> 8) & 0xFF); // High byte
                buffer.push(value & 0xFF);        // Low byte
            } else {
                // For each filled slot
                slots.forEach(slot => {
                    if (!slot) return;

                    // Get stat ID mapping
                    let statIdMap = {};
                    let statId = 0;

                    // Try to get the stat ID from a predefined map or assign a numeric ID
                    if (window.PetSystemData && window.PetSystemData.statIdMap) {
                        statIdMap = window.PetSystemData.statIdMap;
                        statId = statIdMap[slot.id] || 0;
                    } else {
                        // Fallback to common stat IDs if possible
                        const commonStatIds = {
                            'hp': 0, 'mp': 1, 'attack': 2, 'defense': 3, 'attackRate': 4,
                            'defenseRate': 5, 'str': 6, 'int': 7, 'dex': 8, 'penetration': 9,
                            'accuracy': 10, 'evasion': 11, 'critRate': 12, 'critDamage': 13,
                            'allAttackUp': 14, 'hpAutoHeal': 15, 'mpAutoHeal': 16
                        };
                        statId = commonStatIds[slot.id] || 0;
                    }

                    // Add stat ID (1 byte)
                    buffer.push(statId);

                    // Add value (2 bytes - to support larger values)
                    const value = slot.value || 0;
                    buffer.push((value >> 8) & 0xFF); // High byte
                    buffer.push(value & 0xFF);        // Low byte
                });
            }
        }

        // Convert to base64url
        return this.arrayToBase64Url(buffer);
    },

    /**
     * Pet System specific binary decoding
     */
    decodePetSystem: function(encodedData) {
        if (!encodedData) {
            return null;
        }

        // Convert from base64url to array
        const buffer = this.base64UrlToArray(encodedData);

        // Initialize result structure
        const result = {
            selectedStats: {
                normal: Array(10).fill(null),
                covenant: Array(10).fill(null),
                trust: Array(10).fill(null)
            }
        };

        // Skip version (not used currently but kept for future compatibility)
        // const version = buffer[0];

        // Current position in the buffer
        let pos = 1;

        // Process the buffer
        while (pos < buffer.length) {
            // Get tier ID
            const tierIdNum = buffer[pos++];
            const tiers = ['normal', 'covenant', 'trust'];
            const tier = tiers[tierIdNum] || 'normal';

            // Get slot bitmap and all-same flag
            const bitmapHighWithFlag = buffer[pos++];
            const allSame = (bitmapHighWithFlag & 0x80) !== 0;
            const slotBitmapHigh = bitmapHighWithFlag & 0x7F;
            const slotBitmapLow = buffer[pos++];
            const slotBitmap = (slotBitmapHigh << 8) | slotBitmapLow;

            if (allSame) {
                // All slots have the same stat
                const statIdNum = buffer[pos++];

                // Get value (2 bytes)
                const valueHigh = buffer[pos++];
                const valueLow = buffer[pos++];
                const value = (valueHigh << 8) | valueLow;

                // Get stat ID mapping
                let statId = 'hp'; // Default fallback

                // Try to get the stat ID from a predefined map
                if (window.PetSystemData && window.PetSystemData.reverseStatIdMap) {
                    statId = window.PetSystemData.reverseStatIdMap[statIdNum] || 'hp';
                } else {
                    // Fallback to common stat IDs if possible
                    const reverseStatIds = {
                        0: 'hp', 1: 'mp', 2: 'attack', 3: 'defense', 4: 'attackRate',
                        5: 'defenseRate', 6: 'str', 7: 'int', 8: 'dex', 9: 'penetration',
                        10: 'accuracy', 11: 'evasion', 12: 'critRate', 13: 'critDamage',
                        14: 'allAttackUp', 15: 'hpAutoHeal', 16: 'mpAutoHeal'
                    };
                    statId = reverseStatIds[statIdNum] || 'hp';
                }

                // Fill all slots that are in the bitmap
                for (let slotIndex = 0; slotIndex < 10; slotIndex++) {
                    if ((slotBitmap & (1 << slotIndex)) === 0) continue;

                    if (result.selectedStats[tier]) {
                        result.selectedStats[tier][slotIndex] = {
                            id: statId,
                            value: value
                        };
                    }
                }
            } else {
                // Process each slot
                for (let slotIndex = 0; slotIndex < 10; slotIndex++) {
                    // Check if this slot is filled
                    if ((slotBitmap & (1 << slotIndex)) === 0) continue;

                    // Get stat ID
                    const statIdNum = buffer[pos++];

                    // Get value (2 bytes)
                    const valueHigh = buffer[pos++];
                    const valueLow = buffer[pos++];
                    const value = (valueHigh << 8) | valueLow;

                    // Get stat ID mapping
                    let statId = 'hp'; // Default fallback

                    // Try to get the stat ID from a predefined map
                    if (window.PetSystemData && window.PetSystemData.reverseStatIdMap) {
                        statId = window.PetSystemData.reverseStatIdMap[statIdNum] || 'hp';
                    } else {
                        // Fallback to common stat IDs if possible
                        const reverseStatIds = {
                            0: 'hp', 1: 'mp', 2: 'attack', 3: 'defense', 4: 'attackRate',
                            5: 'defenseRate', 6: 'str', 7: 'int', 8: 'dex', 9: 'penetration',
                            10: 'accuracy', 11: 'evasion', 12: 'critRate', 13: 'critDamage',
                            14: 'allAttackUp', 15: 'hpAutoHeal', 16: 'mpAutoHeal'
                        };
                        statId = reverseStatIds[statIdNum] || 'hp';
                    }

                    // Add to result
                    if (result.selectedStats[tier]) {
                        result.selectedStats[tier][slotIndex] = {
                            id: statId,
                            value: value
                        };
                    }
                }
            }
        }

        return result;
    },

    /**
     * Register a system encoder/decoder
     * This allows adding new systems without modifying the core code
     */
    registerSystem: function(systemId, prefix, encodeFn, decodeFn, priority) {
        if (!systemId || !prefix || typeof encodeFn !== 'function' || typeof decodeFn !== 'function') {
            console.error('Invalid system registration parameters');
            return false;
        }

        // Check if prefix is already used
        for (const sysId in this.systems) {
            if (this.systems[sysId].prefix === prefix) {
                console.error(`Prefix '${prefix}' is already used by system '${sysId}'`);
                return false;
            }
        }

        // Register the system
        this.systems[systemId] = {
            id: systemId,
            prefix: prefix,
            encode: encodeFn,
            decode: decodeFn,
            priority: priority || 999 // Default to low priority
        };

        return true;
    },

    /**
     * Encode multiple systems into a single string
     * Format: 'm' + [system count (1 byte)] + [system1 prefix (1 byte)] + [system1 data length (2 bytes)] + [system1 data] + ...
     */
    encodeMultiSystem: function(buildData) {
        if (!buildData || !buildData.systems) {
            return null;
        }

        // Create a binary buffer
        let buffer = [];

        // Add version (1 byte)
        buffer.push(this.version);

        // Track which systems we've encoded
        const encodedSystems = [];

        // Get systems sorted by priority
        const systemIds = Object.keys(this.systems).sort((a, b) =>
            (this.systems[a].priority || 999) - (this.systems[b].priority || 999)
        );

        // Try to encode each system
        for (const systemId of systemIds) {
            const system = this.systems[systemId];
            const systemData = buildData.systems[systemId];

            // Skip if no data for this system or no encoder
            if (!systemData || !system.encode) continue;

            // Try to encode
            const encodedData = system.encode.call(this, systemData);
            if (!encodedData) continue;

            // Add to our list of encoded systems
            encodedSystems.push({
                prefix: system.prefix,
                data: encodedData
            });
        }

        // If no systems were encoded, return null
        if (encodedSystems.length === 0) {
            return null;
        }

        // If only one system was encoded, use the single-system format for backward compatibility
        if (encodedSystems.length === 1) {
            return encodedSystems[0].prefix + encodedSystems[0].data;
        }

        // Add system count (1 byte)
        buffer.push(encodedSystems.length);

        // Add each system's data
        for (const system of encodedSystems) {
            // Add system prefix (1 byte - ASCII code of the prefix character)
            buffer.push(system.prefix.charCodeAt(0));

            // Convert the encoded data back to a binary array
            const systemDataArray = this.base64UrlToArray(system.data);

            // Add system data length (2 bytes)
            buffer.push((systemDataArray.length >> 8) & 0xFF); // High byte
            buffer.push(systemDataArray.length & 0xFF);        // Low byte

            // Add system data
            buffer.push(...systemDataArray);
        }

        // Convert to base64url with multi-system prefix
        return 'm' + this.arrayToBase64Url(buffer);
    },

    /**
     * Decode a multi-system encoded string
     */
    decodeMultiSystem: function(encodedData) {
        if (!encodedData || encodedData.length < 2) {
            return null;
        }

        // Remove the 'm' prefix
        const dataWithoutPrefix = encodedData.substring(1);

        // Convert from base64url to array
        const buffer = this.base64UrlToArray(dataWithoutPrefix);

        // Skip version (not used currently but kept for future compatibility)
        // const version = buffer[0];

        // Current position in the buffer
        let pos = 1;

        // Get system count
        const systemCount = buffer[pos++];

        // Initialize result
        const result = {
            timestamp: Date.now(),
            version: '1.0',
            systems: {}
        };

        // Process each system
        for (let i = 0; i < systemCount; i++) {
            // Get system prefix
            const prefixCode = buffer[pos++];
            const prefix = String.fromCharCode(prefixCode);

            // Get system data length
            const lengthHigh = buffer[pos++];
            const lengthLow = buffer[pos++];
            const length = (lengthHigh << 8) | lengthLow;

            // Extract system data
            const systemDataArray = buffer.slice(pos, pos + length);
            pos += length;

            // Convert to base64url
            const systemData = this.arrayToBase64Url(systemDataArray);

            // Find the system by prefix
            let systemId = null;
            for (const sysId in this.systems) {
                if (this.systems[sysId].prefix === prefix) {
                    systemId = sysId;
                    break;
                }
            }

            // Skip if system not found
            if (!systemId || !this.systems[systemId].decode) continue;

            // Decode the system data
            const decodedData = this.systems[systemId].decode.call(this, systemData);
            if (!decodedData) continue;

            // Add to result
            result.systems[systemId] = decodedData;
        }

        return result;
    },

    /**
     * Encode build data with custom compression
     * Uses system-specific encoders for supported systems
     */
    encodeBuildData: function(buildData) {
        if (!buildData || !buildData.systems) {
            return null;
        }

        // Register system encoders/decoders if not already set
        if (!this.systems.honor.encode) {
            this.systems.honor.encode = this.encodeHonorSystem;
            this.systems.honor.decode = this.decodeHonorSystem;
        }

        if (!this.systems.pet.encode) {
            this.systems.pet.encode = this.encodePetSystem;
            this.systems.pet.decode = this.decodePetSystem;
        }

        // Check if we have multiple systems with data
        const systemsWithData = Object.keys(buildData.systems).filter(sysId =>
            buildData.systems[sysId] && this.systems[sysId] && this.systems[sysId].encode
        );

        if (systemsWithData.length > 1) {
            // Multiple systems - use multi-system encoder
            const multiEncoded = this.encodeMultiSystem(buildData);
            if (multiEncoded) {
                return multiEncoded;
            }
        } else if (systemsWithData.length === 1) {
            // Single system - use direct encoder for backward compatibility
            const systemId = systemsWithData[0];
            const system = this.systems[systemId];

            if (system && system.encode) {
                const encoded = system.encode.call(this, buildData.systems[systemId]);
                if (encoded) {
                    return system.prefix + encoded;
                }
            }
        }

        // Fallback to standard compression if no system-specific encoding worked
        const jsonData = JSON.stringify(buildData);
        return LZString.compressToBase64(jsonData);
    },

    /**
     * Decode build data from custom compression
     */
    decodeBuildData: function(encodedData) {
        if (!encodedData) {
            return null;
        }

        // Register system encoders/decoders if not already set
        if (!this.systems.honor.encode) {
            this.systems.honor.encode = this.encodeHonorSystem;
            this.systems.honor.decode = this.decodeHonorSystem;
        }

        if (!this.systems.pet.encode) {
            this.systems.pet.encode = this.encodePetSystem;
            this.systems.pet.decode = this.decodePetSystem;
        }

        // Check if it's our multi-system format
        if (encodedData.startsWith('m')) {
            // Multi-system format
            return this.decodeMultiSystem(encodedData);
        }

        // Check if it's a single-system format
        for (const systemId in this.systems) {
            const system = this.systems[systemId];
            if (encodedData.startsWith(system.prefix) && system.decode) {
                // Single system format
                const systemEncoded = encodedData.substring(1);
                const systemData = system.decode.call(this, systemEncoded);

                if (systemData) {
                    return {
                        timestamp: Date.now(),
                        version: '1.0',
                        systems: {
                            [systemId]: systemData
                        }
                    };
                }
            }
        }

        // Otherwise, try to decompress with LZString
        try {
            const jsonData = LZString.decompressFromBase64(encodedData);
            if (!jsonData) {
                throw new Error('Decompression failed');
            }

            return JSON.parse(jsonData);
        } catch (error) {
            console.error('Error decoding build data:', error);
            return null;
        }
    }
};
