# Divine Upgrades Display Bug & Solution Pattern

## Issue Description

The Equipment System, when updated with grade-specific divine upgrades, experienced a recurring UI issue:

- Divine upgrade stats properly calculated and affected total stats in background
- Stats were correctly applied to DPS calculations and final stat summary
- However, no upgrade bonuses displayed in the equipment detail panel
- Display issue occurred specifically when moving divine upgrade slider

![Divine Upgrade Display Bug](../assets/images/docs/divine-upgrade-bug.png)

## Root Cause Analysis

The issue stemmed from a mismatch between data structure changes and UI update patterns:

1. Original data structure used a simple array where `divineUpgrades[level]` contained bonuses
2. New structure uses an object with grade-specific arrays, requiring `divineUpgrades[grade][stat][level]`
3. While calculation code was updated to use new helper function, the display update code was missed
4. Specifically, `renderDivineUpgradeSection()` was updated but `updateWeaponStats()` still used old pattern

## Solution Pattern

Solution requires consistent use of the same data access pattern in both places:

1. Initial rendering function (`renderDivineUpgradeSection`)
2. Update function triggered by slider changes (`updateWeaponStats`)

### Correct Implementation

Both functions need to:

1. Access data through the helper function: `WeaponsData.getDivineUpgradeStats(material, level)`
2. Convert the returned object format to array format for display: 
   ```js
   Object.entries(statsObj).map(([stat, value]) => ({ stat, value }))
   ```
3. Pass to the display formatter: `formatBonusesList(divineBonuses)`

The actual fix:

```js
// In updateWeaponStats function
const divineContainer = document.querySelector('.fg-divine-bonuses');
if (divineContainer) {
    let divineBonuses = [];
    
    if (window.WeaponsData && typeof WeaponsData.getDivineUpgradeStats === 'function' && item.material) {
        // Convert the stats object to array format for display
        const statsObj = WeaponsData.getDivineUpgradeStats(item.material, settings.divineLevel);
        divineBonuses = Object.entries(statsObj).map(([stat, value]) => ({ stat, value }));
    }
    
    divineContainer.innerHTML = this.formatBonusesList(divineBonuses);
}
```

## Lessons Learned & Prevention

1. **Data Structure Updates**: When changing data structures, use text search to find all code locations accessing that structure

2. **UI Update Patterns**: Always ensure both initial rendering and subsequent updates use the same data access pattern

3. **Helper Functions**: Create centralized helper functions for data access, then use those helpers consistently:
   - Initial render
   - Update on slider/input change
   - Stats calculation 

4. **Testing Procedure**: Test both initial display and slider changes:
   - Load item and verify stats display correctly initially
   - Move sliders and verify stats update correctly
   - Test with all material grades to ensure grade-specific values work

5. **UI Event Flow**: 
   - Slider changes trigger event listener
   - Event listener updates settings value
   - Event listener calls updateWeaponStats() function
   - updateWeaponStats() function should display stats using helpers

## Similar Patterns to Watch For

This pattern of "display vs. calculation" mismatches is common and tends to happen in:

1. Equipment upgrades with specialized stats (belts, divine upgrades, etc.)
2. Rendering stat displays vs. calculating actual stats
3. Any system with separate code paths for:
   - Initial rendering
   - Update on user input
   - Global stats calculation

## Fix Verification

To verify the fix is correct:

1. Load any weapon with a material grade (mithril, archridium, palladium)
2. Move divine upgrade slider to different levels (0-15)
3. Confirm stats display matches expected values from the grade's chart
4. Test with each material grade to ensure grade-specific values appear correctly
5. Verify accuracy stat appears correctly at level 15 