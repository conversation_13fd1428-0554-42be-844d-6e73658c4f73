/**
 * Pet System Data
 * Contains all data definitions for the pet system including:
 * - Available stats for each tier
 * - Stat ID mappings for compression
 */

// Define the pet system data globally to ensure it's always available
window.PetSystemData = {
    // Available stats for each tier
    tierStats: {
        normal: [
            { id: 'hp', value: 80 },
            { id: 'mp', value: 30 },
            { id: 'allAttackUp', value: 10 },
            { id: 'defense', value: 12 },
            { id: 'attackRate', value: 80 },
            { id: 'defenseRate', value: 45 },
            { id: 'critDamage', value: 2 },
            { id: 'critRate', value: 1 },
            { id: 'minDamage', value: 1 },
            { id: 'maxHpSteal', value: 5 },
            { id: 'maxMpSteal', value: 5 },
            { id: 'maxCritRate', value: 1 },
            { id: 'allSkillAmp', value: 1 },
            { id: 'hpAbsorb', value: 1 },
            { id: 'mpAbsorb', value: 1 },
            { id: 'evasion', value: 100 },
            { id: 'hpAutoHeal', value: 5 },
            { id: 'mpAutoHeal', value: 5 },
            { id: 'addDamage', value: 10 },
            { id: 'skillExp', value: 10 },
            { id: 'alzDropAmount', value: 10 },
            { id: '2SlotDropRate', value: 2 },
            { id: 'resistCritRate', value: 1 },
            { id: 'resistCritDmg', value: 2 },
            { id: 'resistUnableToMove', value: 2 },
            { id: 'resistDown', value: 2 },
            { id: 'resistKnockback', value: 2 },
            { id: 'resistStun', value: 2 }
        ],
        covenant: [
            { id: 'hp', value: 120 },
            { id: 'mp', value: 50 },
            { id: 'allAttackUp', value: 15 },
            { id: 'defense', value: 20 },
            { id: 'attackRate', value: 100 },
            { id: 'defenseRate', value: 60 },
            { id: 'critDamage', value: 4 },
            { id: 'allSkillAmp', value: 2 },
            { id: 'hpAutoHeal', value: 10 },
            { id: 'mpAutoHeal', value: 10 },
            { id: 'resistCritDmg', value: 5 },
            { id: 'resistDown', value: 2 },
            { id: 'resistKnockback', value: 2 },
            { id: 'resistStun', value: 2 },
            { id: 'addDamage', value: 20 },
            { id: 'accuracy', value: 80 },
            { id: 'penetration', value: 11 },
            { id: 'ignorePenetration', value: 12 },
            { id: 'resistSkillAmp', value: 2 },
            { id: 'ignoreAccuracy', value: 100 },
            { id: 'damageReduce', value: 12 },
            { id: 'ignoreDamageReduce', value: 25 },
            { id: 'str', value: 10 },
            { id: 'int', value: 10 },
            { id: 'dex', value: 10 },
            { id: 'ignoreResistCritDmg', value: 6 },
            { id: 'ignoreResistSkillAmp', value: 3 }
        ],
        trust: [
            { id: 'hp', value: 160 },
            { id: 'allAttackUp', value: 20 },
            { id: 'defense', value: 30 },
            { id: 'attackRate', value: 120 },
            { id: 'defenseRate', value: 75 },
            { id: 'hpAutoHeal', value: 15 },
            { id: 'mpAutoHeal', value: 15 },
            { id: 'addDamage', value: 30 },
            { id: 'resistCritDmg', value: 8 },
            { id: 'resistDown', value: 2 },
            { id: 'resistKnockback', value: 2 },
            { id: 'resistStun', value: 2 },
            { id: 'accuracy', value: 100 },
            { id: 'penetration', value: 15 },
            { id: 'ignorePenetration', value: 16 },
            { id: 'resistSkillAmp', value: 3 },
            { id: 'ignoreAccuracy', value: 120 },
            { id: 'damageReduce', value: 15 },
            { id: 'ignoreDamageReduce', value: 35 },
            { id: 'ignoreResistCritDmg', value: 8 },
            { id: 'ignoreResistSkillAmp', value: 4 },
            { id: 'ignoreResistCritRate', value: 1 },
            { id: 'normalDamageUp', value: 3 },
            { id: 'ignoreResistKnockback', value: 3 },
            { id: 'ignoreResistDown', value: 3 },
            { id: 'ignoreResistStun', value: 3 },
            { id: 'auraDurationIncrease', value: 2 }
        ]
    },

    // Stat ID mappings for compression (numeric IDs for each stat)
    statIdMap: {
        'hp': 0, 'mp': 1, 'attack': 2, 'defense': 3, 'attackRate': 4,
        'defenseRate': 5, 'str': 6, 'int': 7, 'dex': 8, 'penetration': 9,
        'accuracy': 10, 'evasion': 11, 'critRate': 12, 'critDamage': 13,
        'allAttackUp': 14, 'hpAutoHeal': 15, 'mpAutoHeal': 16,
        'minDamage': 17, 'maxHpSteal': 18, 'maxMpSteal': 19, 'maxCritRate': 20,
        'allSkillAmp': 21, 'hpAbsorb': 22, 'mpAbsorb': 23, 'addDamage': 24,
        'skillExp': 25, 'alzDropAmount': 26, '2SlotDropRate': 27,
        'resistCritRate': 28, 'resistCritDmg': 29, 'resistUnableToMove': 30,
        'resistDown': 31, 'resistKnockback': 32, 'resistStun': 33,
        'ignorePenetration': 34, 'resistSkillAmp': 35, 'ignoreAccuracy': 36,
        'damageReduce': 37, 'ignoreDamageReduce': 38, 'ignoreResistCritDmg': 39,
        'ignoreResistSkillAmp': 40, 'ignoreResistCritRate': 41, 'normalDamageUp': 42,
        'ignoreResistKnockback': 43, 'ignoreResistDown': 44, 'ignoreResistStun': 45,
        'auraDurationIncrease': 46
    },

    // Reverse mapping for decompression (numeric ID to stat name)
    reverseStatIdMap: {
        0: 'hp', 1: 'mp', 2: 'attack', 3: 'defense', 4: 'attackRate',
        5: 'defenseRate', 6: 'str', 7: 'int', 8: 'dex', 9: 'penetration',
        10: 'accuracy', 11: 'evasion', 12: 'critRate', 13: 'critDamage',
        14: 'allAttackUp', 15: 'hpAutoHeal', 16: 'mpAutoHeal', 17: 'minDamage',
        18: 'maxHpSteal', 19: 'maxMpSteal', 20: 'maxCritRate', 21: 'allSkillAmp',
        22: 'hpAbsorb', 23: 'mpAbsorb', 24: 'addDamage', 25: 'skillExp',
        26: 'alzDropAmount', 27: '2SlotDropRate', 28: 'resistCritRate',
        29: 'resistCritDmg', 30: 'resistUnableToMove', 31: 'resistDown',
        32: 'resistKnockback', 33: 'resistStun', 34: 'ignorePenetration',
        35: 'resistSkillAmp', 36: 'ignoreAccuracy', 37: 'damageReduce',
        38: 'ignoreDamageReduce', 39: 'ignoreResistCritDmg', 40: 'ignoreResistSkillAmp',
        41: 'ignoreResistCritRate', 42: 'normalDamageUp', 43: 'ignoreResistKnockback',
        44: 'ignoreResistDown', 45: 'ignoreResistStun', 46: 'auraDurationIncrease'
    }
};

// Ensure this data is loaded first before the pet system actually initializes