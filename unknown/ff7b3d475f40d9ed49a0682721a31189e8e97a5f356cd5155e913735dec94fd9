# Achievement System - Design Document

## Overview

The Achievement System allows players to track and select completed achievements that provide stat bonuses. Achievements come in two types:
1. **Single Completion**: Achievements that are either completed or not (binary state)
2. **Milestone-based**: Achievements with multiple milestones, each providing incremental stats

## System Requirements

### Core Features
- **Achievement Categories**: Organize achievements by type (Dungeons, PvP, Exploration, etc.)
- **Milestone Selection**: For milestone-based achievements, allow users to select their current progress
- **Cumulative Stats**: All selected achievement stats are added together
- **Progress Visualization**: Show progress bars for milestone achievements
- **Stat Integration**: Use stats-config.js for consistent stat definitions and icons

### Achievement Types

#### 1. Single Completion Achievements
- Binary state: completed or not completed
- Full progress bar when completed
- Single stat reward upon completion
- Examples: "Complete Tutorial", "Reach Level 100"

#### 2. Milestone-based Achievements
- Multiple completion thresholds (e.g., 100, 500, 1000, 5000)
- Each milestone provides additional stats
- Stats are cumulative (100 + 500 + 1000 + 5000 rewards)
- Progress bar shows current milestone
- Examples: Dungeon completion counts, PvP kills, etc.

### Data Structure

#### Achievement Data Format
```javascript
const AchievementData = {
    categories: {
        dungeons: {
            name: "Dungeon Achievements",
            achievements: {
                "illusion-castle-underworld": {
                    name: "Illusion Castle - Underworld",
                    type: "milestone",
                    milestones: [
                        { threshold: 100, stats: { "defense-rate": 2 } },
                        { threshold: 500, stats: { "defense-rate": 4 } },
                        { threshold: 1000, stats: { "defense-rate": 6 } },
                        { threshold: 5000, stats: { "defense-rate": 8 } }
                    ]
                },
                "illusion-castle-radiant": {
                    name: "Illusion Castle - Radiant Hall",
                    type: "milestone",
                    milestones: [
                        { threshold: 100, stats: { "attack-rate": 2 } },
                        { threshold: 500, stats: { "attack-rate": 4 } },
                        { threshold: 1000, stats: { "attack-rate": 6 } },
                        { threshold: 5000, stats: { "attack-rate": 8 } }
                    ]
                }
            }
        },
        exploration: {
            name: "Exploration Achievements",
            achievements: {
                "world-explorer": {
                    name: "World Explorer",
                    type: "single",
                    stats: { "movement-speed": 10, "experience-rate": 5 }
                }
            }
        }
    }
};
```

### UI Design Requirements

#### Layout Structure
1. **Category Tabs**: Horizontal tabs for different achievement categories
2. **Achievement List**: Scrollable list within each category
3. **Achievement Cards**: Each achievement displayed as a card with:
   - Achievement name and description
   - Progress bar (full for single, milestone-based for others)
   - Milestone selector (for milestone achievements)
   - Stat rewards display
4. **Stats Summary**: Integration with existing StatIntegrationService

#### Visual Design
- **Progress Bars**: Orange/yellow progress bars matching game UI
- **Milestone Selector**: Dropdown or slider for selecting current milestone
- **Stat Display**: Use stats-config.js icons and formatting
- **Responsive Design**: Follow existing system patterns

### Technical Implementation

#### File Structure
```
js/achievement-system/
├── achievement-system.js          # Main system logic
├── achievement-data.js            # Achievement definitions
└── achievement-system.css         # System-specific styles

templates/systems/
└── achievement-system.php         # PHP template

docs/
└── ACHIEVEMENT-SYSTEM.md          # This documentation
```

#### Integration Points
1. **Stats Config**: Use StatsConfig.getStatInfo() for stat definitions
2. **Build Planner**: Use BuildPlanner.updateStats() for stat integration
3. **Build Saver**: Use BuildSaverStore for save/load functionality
4. **Stat Integration Service**: Use for consistent stat display

#### Key Methods
- `calculateTotalStats()`: Sum all selected achievement stats
- `updateMilestone(achievementId, milestone)`: Update milestone selection
- `toggleAchievement(achievementId)`: Toggle single completion achievements
- `renderAchievementCard(achievement)`: Generate achievement UI
- `updateProgressBar(achievementId, progress)`: Update visual progress

### Development Plan

#### Phase 1: Core Structure
1. Create basic file structure following system guidelines
2. Implement achievement data structure
3. Create basic UI with category tabs
4. Integrate with BuildPlanner stats system

#### Phase 2: Milestone System
1. Implement milestone selection UI
2. Add progress bar visualization
3. Implement cumulative stat calculation
4. Add save/load functionality

#### Phase 3: Polish & Integration
1. Add all achievement data from game
2. Implement search/filter functionality
3. Add tooltips and help text
4. Performance optimization
5. Testing and bug fixes

### Sample Data (Starting Set)

#### Dungeon Achievements
- **Illusion Castle - Underworld**: Defense Rate +2/+4/+6/+8 (100/500/1000/5000 runs)
- **Illusion Castle - Radiant Hall**: Attack Rate +2/+4/+6/+8 (100/500/1000/5000 runs)
- **Illusion Castle - Underworld (Apocrypha)**: Resist Suppression +1%/+1%/+1%/+2% (100/500/1000/5000 runs)
- **Illusion Castle - Radiant Hall (Apocrypha)**: Resist Silent +1%/+1%/+1%/+2% (100/500/1000/5000 runs)

### Notes
- All stats are cumulative within each achievement
- Multiple achievements can be selected simultaneously
- System follows existing architectural patterns
- Uses standardized save/load mechanisms
- Integrates with existing stats summary panel
