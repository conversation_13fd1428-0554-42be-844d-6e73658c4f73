/**
 * Class System CSS
 * Styling for the class selection and attribute distribution UI
 */

/* Main container */
.fg-class-system-container {
    display: flex;
    flex-direction: column;
    gap: 20px;
    width: 100%;
    max-width: 1000px;
    margin: 0 auto;
    padding: 20px;
}

/* Section headers */
.fg-section-header {
    font-size: 1.2rem;
    margin: 0 0 15px 0;
    border-bottom: 1px solid #353535;
    padding-bottom: 8px;
    color: #f0f0f0;
}

/* Class selection section */
.fg-class-selection-section {
    background-color: #282828;
    border-radius: 8px;
    padding: 15px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

/* Class selection grid */
.fg-class-grid {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    justify-content: center;
}

@media (max-width: 768px) {
    .fg-class-grid {
        justify-content: center;
    }
}

/* Class option */
.fg-class-option {
    display: flex;
    flex-direction: column;
    align-items: center;
    background-color: #333333;
    border-radius: 6px;
    padding: 8px 6px;
    cursor: pointer;
    transition: all 0.2s ease;
    border: 2px solid transparent;
    flex: 0 0 auto;
    min-width: 80px;
}

.fg-class-option:hover {
    background-color: #404040;
}

.fg-class-option.selected {
    background-color: #455a64;
    border-color: #64b5f6;
}

/* Class icon */
.fg-class-icon {
    width: 50px;
    height: 50px;
    background-color: #424242;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 4px;
}

.fg-icon-placeholder {
    font-size: 24px;
    color: #bbbbbb;
}

/* Class name */
.fg-class-name {
    font-size: 0.8rem;
    font-weight: 500;
    color: #e0e0e0;
    text-align: center;
}

/* Attribute section */
.fg-attribute-section {
    background-color: #282828;
    border-radius: 8px;
    padding: 15px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

/* Points info */
.fg-points-info {
    background-color: #2c2c2c;
    border-radius: 6px;
    padding: 10px 15px;
    margin-bottom: 15px;
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
}

.fg-points-info p {
    margin: 0;
    color: #d0d0d0;
    font-size: 0.9rem;
}

.fg-points-info span {
    font-weight: 600;
    color: #f0f0f0;
}

.fg-remaining-points {
    color: #64b5f6 !important;
}

/* Attributes container */
.fg-attributes-container {
    display: flex;
    flex-direction: column;
    gap: 15px;
    margin-bottom: 15px;
}

/* Attribute control */
.fg-attribute-control {
    display: flex;
    align-items: center;
    background-color: #333333;
    border-radius: 6px;
    padding: 8px 10px;
    gap: 8px;
    position: relative;
    max-width: 400px;
    margin: 0 auto;
}

/* Attribute label */
.fg-attribute-label {
    display: flex;
    align-items: center;
    gap: 6px;
    flex: 0 0 70px;
    font-weight: 600;
    color: #e0e0e0;
}

.fg-attribute-icon {
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.fg-attribute-icon img {
    width: 100%;
    height: 100%;
    object-fit: contain;
}

/* Attribute value control */
.fg-attribute-value-control {
    display: flex;
    align-items: center;
    gap: 6px;
    flex: 1;
}

.fg-attribute-btn {
    background-color: #444444;
    border: none;
    color: #ffffff;
    border-radius: 4px;
    width: 28px;
    height: 28px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: background-color 0.2s ease;
    font-size: 0.9rem;
}

.fg-attribute-btn:hover {
    background-color: #555555;
}

.fg-increment-10, .fg-increment-100 {
    width: auto;
    min-width: 36px;
    padding: 0 6px;
}

.fg-attribute-value {
    background-color: #3a3a3a;
    padding: 4px 10px;
    border-radius: 4px;
    min-width: 50px;
    text-align: center;
    font-weight: 600;
    color: #f0f0f0;
}

/* Tooltip trigger */
.fg-attribute-tooltip-trigger {
    position: absolute;
    right: 15px;
    width: 24px;
    height: 24px;
    background-color: #3a3a3a;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
}

.fg-icon-info {
    color: #90caf9;
    font-size: 14px;
    font-style: normal;
}

/* Reset button */
.fg-reset-attributes-btn {
    background-color: #555555;
    border: none;
    color: #ffffff;
    border-radius: 4px;
    padding: 8px 16px;
    cursor: pointer;
    transition: background-color 0.2s ease;
    margin-top: 10px;
    font-weight: 500;
}

.fg-reset-attributes-btn:hover {
    background-color: #666666;
}

/* Derived stats section */
.fg-derived-stats-section {
    background-color: #282828;
    border-radius: 8px;
    padding: 15px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

/* Derived stats container */
.fg-derived-stats-container {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 15px;
}

@media (max-width: 768px) {
    .fg-derived-stats-container {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 480px) {
    .fg-derived-stats-container {
        grid-template-columns: 1fr;
    }
}

/* Derived stats group */
.fg-derived-stats-group {
    background-color: #333333;
    border-radius: 6px;
    padding: 12px;
}

.fg-stats-group-header {
    font-size: 0.95rem;
    color: #e0e0e0;
    margin: 0 0 10px 0;
    padding-bottom: 6px;
    border-bottom: 1px solid #444444;
}

/* Derived stats */
.fg-derived-stats {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

/* Individual derived stat */
.fg-derived-stat {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 6px 8px;
    background-color: #3a3a3a;
    border-radius: 4px;
}

.fg-derived-stat-icon {
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.fg-derived-stat-icon img {
    width: 100%;
    height: 100%;
    object-fit: contain;
}

.fg-derived-stat-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex: 1;
}

.fg-derived-stat-name {
    font-size: 0.85rem;
    color: #d0d0d0;
}

.fg-derived-stat-value {
    font-size: 0.85rem;
    font-weight: 600;
    color: #f0f0f0;
}

/* Attribute tooltip */
.fg-attribute-tooltip {
    position: absolute;
    background-color: #2c2c2c;
    border-radius: 6px;
    padding: 12px;
    box-shadow: 0 3px 6px rgba(0, 0, 0, 0.3);
    z-index: 1000;
    min-width: 220px;
    max-width: 300px;
}

.fg-attribute-tooltip h4 {
    font-size: 0.95rem;
    color: #e0e0e0;
    margin: 0 0 8px 0;
    padding-bottom: 6px;
    border-bottom: 1px solid #444444;
}

.fg-attribute-tooltip p {
    margin: 0 0 10px 0;
    font-size: 0.85rem;
    color: #d0d0d0;
}

.fg-tooltip-stats {
    display: flex;
    flex-direction: column;
    gap: 6px;
}

.fg-tooltip-stat {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 4px 8px;
    background-color: #3a3a3a;
    border-radius: 4px;
}

.fg-tooltip-stat-name {
    font-size: 0.85rem;
    color: #d0d0d0;
}

.fg-tooltip-stat-value {
    font-size: 0.85rem;
    font-weight: 600;
    color: #f0f0f0;
} 