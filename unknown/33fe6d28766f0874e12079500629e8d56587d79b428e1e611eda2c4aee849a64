/**
 * Damage Calculator
 * Calculates and displays damage per hit for both normal and critical hits
 * Based on the game's damage formula from the reverse-engineered code
 */

const DamageCalculator = {
    // Store for previous damage values
    previousSwordNormal: 0,
    previousSwordCrit: 0,
    previousMagicNormal: 0,
    previousMagicCrit: 0,
    previousSwordDPS: 0,
    previousMagicDPS: 0,

    // Enemy configuration (defaults)
    enemyConfig: {
        level: 1,
        defense: 0,
        damageReduction: 0,
        damageReductionPercent: 0,
        finalDamageDecrease: 0
    },

    // Initialize the calculator
    init: function() {
        // Create damage display element if it doesn't exist
        this.createDamageDisplayIfNeeded();

        // Register with StatsSummary for stat updates
        if (typeof StatsSummary !== 'undefined') {
            // Store original method
            const originalUpdateTotalStats = StatsSummary.updateTotalStats;

            // Override with our version that triggers damage calculation
            StatsSummary.updateTotalStats = (totalStats) => {
                // Call original method first
                originalUpdateTotalStats.call(StatsSummary, totalStats);

                // Calculate and update damage using derived stats
                this.calculateAndUpdateDamage(StatsSummary.derivedStats || totalStats);
            };

            // Check if we already have stats to calculate damage with
            if (StatsSummary.derivedStats && Object.keys(StatsSummary.derivedStats).length > 0) {
                setTimeout(() => {
                    this.calculateAndUpdateDamage(StatsSummary.derivedStats);
                }, 500);
            } else if (StatsSummary.totalStats && Object.keys(StatsSummary.totalStats).length > 0) {
                setTimeout(() => {
                    this.calculateAndUpdateDamage(StatsSummary.totalStats);
                }, 500);
            }
        }
    },

    // Create the damage display element
    createDamageDisplayIfNeeded: function() {
        if (!document.getElementById('fg-damage-display')) {
            const displayContainer = document.createElement('div');
            displayContainer.id = 'fg-damage-display-container';
            displayContainer.className = 'fg-damage-display-container';

            displayContainer.innerHTML = `
                <div class="fg-damage-title">Damage estimates</div>
                <div class="fg-damage-right-section">
                    <div class="fg-damage-section">
                        <div class="fg-damage-type">Sword</div>
                        <div class="fg-damage-hit-types">
                            <div class="fg-damage-hit-type">
                                <span class="fg-damage-hit-label">Normal:</span>
                                <span id="fg-sword-normal-display" class="fg-damage-value">0</span>
                                <span id="fg-sword-normal-change" class="fg-damage-change"></span>
                            </div>
                            <div class="fg-damage-hit-type">
                                <span class="fg-damage-hit-label">Critical:</span>
                                <span id="fg-sword-crit-display" class="fg-damage-value">0</span>
                                <span id="fg-sword-crit-change" class="fg-damage-change"></span>
                            </div>
                            <div class="fg-damage-hit-type fg-dps-row">
                                <span class="fg-damage-hit-label">DPS:</span>
                                <span id="fg-sword-dps-display" class="fg-damage-value">0</span>
                                <span id="fg-sword-dps-change" class="fg-damage-change"></span>
                            </div>
                        </div>
                    </div>
                    <div class="fg-damage-section">
                        <div class="fg-damage-type">Magic</div>
                        <div class="fg-damage-hit-types">
                            <div class="fg-damage-hit-type">
                                <span class="fg-damage-hit-label">Normal:</span>
                                <span id="fg-magic-normal-display" class="fg-damage-value">0</span>
                                <span id="fg-magic-normal-change" class="fg-damage-change"></span>
                            </div>
                            <div class="fg-damage-hit-type">
                                <span class="fg-damage-hit-label">Critical:</span>
                                <span id="fg-magic-crit-display" class="fg-damage-value">0</span>
                                <span id="fg-magic-crit-change" class="fg-damage-change"></span>
                            </div>
                            <div class="fg-damage-hit-type fg-dps-row">
                                <span class="fg-damage-hit-label">DPS:</span>
                                <span id="fg-magic-dps-display" class="fg-damage-value">0</span>
                                <span id="fg-magic-dps-change" class="fg-damage-change"></span>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="fg-damage-config-button" id="fg-damage-config-button">⚙️</div>
            `;

            // Insert at the beginning of the main content
            const mainContent = document.querySelector('.fg-main-content');
            if (mainContent) {
                mainContent.insertBefore(displayContainer, mainContent.firstChild);

                // Create enemy config panel (hidden by default)
                this.createEnemyConfigPanel(mainContent);

                // Add CSS for damage display
                this.addDamageStyles();

                // Add click event for config button
                document.getElementById('fg-damage-config-button').addEventListener('click', () => {
                    const configPanel = document.getElementById('fg-enemy-config-panel');
                    if (configPanel) {
                        configPanel.classList.toggle('fg-enemy-config-panel-visible');
                    }
                });
            }
        }
    },

    // Create enemy configuration panel
    createEnemyConfigPanel: function(parentElement) {
        const configPanel = document.createElement('div');
        configPanel.id = 'fg-enemy-config-panel';
        configPanel.className = 'fg-enemy-config-panel';

        configPanel.innerHTML = `
            <div class="fg-enemy-config-title">Enemy Configuration</div>
            <div class="fg-enemy-config-row">
                <label for="fg-enemy-level" title="Enemy level affects damage calculations through level difference penalty">Level:</label>
                <input type="number" id="fg-enemy-level" min="1" max="100" value="${this.enemyConfig.level}">
            </div>
            <div class="fg-enemy-config-row">
                <label for="fg-enemy-defense" title="Higher defense reduces incoming damage. Counteracted by penetration">Defense:</label>
                <input type="number" id="fg-enemy-defense" min="0" value="${this.enemyConfig.defense}">
            </div>
            <div class="fg-enemy-config-row">
                <label for="fg-enemy-dmg-reduction" title="Flat damage reduction applied after other calculations">Damage Reduction:</label>
                <input type="number" id="fg-enemy-dmg-reduction" min="0" value="${this.enemyConfig.damageReduction}">
            </div>
            <div class="fg-enemy-config-row">
                <label for="fg-enemy-dmg-reduction-pct" title="Percentage-based damage reduction">Damage Reduction %:</label>
                <input type="number" id="fg-enemy-dmg-reduction-pct" min="0" max="100" value="${this.enemyConfig.damageReductionPercent}">
            </div>
            <div class="fg-enemy-config-row">
                <label for="fg-enemy-final-dmg-decrease" title="Applied at the end of all damage calculations">Final Damage Decrease %:</label>
                <input type="number" id="fg-enemy-final-dmg-decrease" min="0" max="100" value="${this.enemyConfig.finalDamageDecrease}">
            </div>
            <button id="fg-enemy-config-save" class="fg-enemy-config-save">Apply</button>
            <div class="fg-enemy-config-info">
                <p>Configure enemy stats to get more accurate damage calculations. Hover over labels for more information.</p>
            </div>
        `;

        // Insert after the damage display
        parentElement.insertBefore(configPanel, parentElement.firstChild.nextSibling);

        // Add event listener for save button
        setTimeout(() => {
            document.getElementById('fg-enemy-config-save').addEventListener('click', () => {
                this.saveEnemyConfig();
            });
        }, 100);
    },

    // Save enemy configuration
    saveEnemyConfig: function() {
        const level = parseInt(document.getElementById('fg-enemy-level').value) || 1;
        const defense = parseInt(document.getElementById('fg-enemy-defense').value) || 0;
        const damageReduction = parseInt(document.getElementById('fg-enemy-dmg-reduction').value) || 0;
        const damageReductionPercent = parseInt(document.getElementById('fg-enemy-dmg-reduction-pct').value) || 0;
        const finalDamageDecrease = parseInt(document.getElementById('fg-enemy-final-dmg-decrease').value) || 0;

        this.configureEnemy({
            level: level,
            defense: defense,
            damageReduction: damageReduction,
            damageReductionPercent: damageReductionPercent,
            finalDamageDecrease: finalDamageDecrease
        });

        // Hide the panel
        const configPanel = document.getElementById('fg-enemy-config-panel');
        if (configPanel) {
            configPanel.classList.remove('fg-enemy-config-panel-visible');
        }
    },

    // Add CSS styles for damage display
    addDamageStyles: function() {
        const style = document.createElement('style');
        style.textContent = `
            .fg-damage-display-container {
                background-color: #2a2a2a;
                border: 1px solid #4e4e4e;
                border-radius: 6px;
                padding: 10px;
                margin-bottom: 15px;
                text-align: center;
                box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2);
                display: flex;
                flex-direction: row;
                align-items: center;
                justify-content: space-between;
                position: relative;
                max-width: 100%;
                min-height: 46px;
            }

            .fg-damage-title {
                font-size: 14px;
                color: #aaa;
                text-transform: uppercase;
                letter-spacing: 0.5px;
                margin-right: 10px;
                white-space: nowrap;
                flex: 1;
                text-align: left;
            }

            .fg-damage-right-section {
                display: flex;
                align-items: center;
                justify-content: center;
                flex: 2;
                text-align: center;
                gap: 15px;
            }

            .fg-damage-section {
                display: flex;
                flex-direction: column;
                align-items: center;
                min-width: 120px;
            }

            .fg-damage-type {
                font-size: 14px;
                color: #ccc;
                margin-bottom: 2px;
                font-weight: bold;
            }

            .fg-damage-hit-types {
                display: flex;
                flex-direction: column;
                gap: 4px;
            }

            .fg-damage-hit-type {
                display: flex;
                align-items: center;
                justify-content: space-between;
                gap: 5px;
            }

            .fg-damage-hit-label {
                font-size: 12px;
                color: #aaa;
                min-width: 55px;
                text-align: right;
            }

            .fg-damage-value {
                font-size: 14px;
                font-weight: bold;
                color: #fff;
                text-shadow: 0 0 5px rgba(255, 255, 255, 0.3);
                min-width: 60px;
                text-align: right;
            }

            .fg-damage-change {
                font-size: 10px;
                min-height: 12px;
                min-width: 40px;
                text-align: left;
            }

            .fg-damage-increase {
                color: #4caf50;
            }

            .fg-damage-decrease {
                color: #f44336;
            }

            .fg-damage-config-button {
                cursor: pointer;
                font-size: 16px;
                margin-left: 10px;
                opacity: 0.7;
                transition: opacity 0.2s;
            }

            .fg-damage-config-button:hover {
                opacity: 1;
            }

            .fg-enemy-config-panel {
                background-color: #2a2a2a;
                border: 1px solid #4e4e4e;
                border-radius: 6px;
                padding: 15px;
                margin-bottom: 15px;
                box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
                max-width: 100%;
                display: none;
            }

            .fg-enemy-config-panel-visible {
                display: block;
            }

            .fg-enemy-config-title {
                font-size: 16px;
                color: #fff;
                margin-bottom: 15px;
                text-align: center;
                font-weight: bold;
            }

            .fg-enemy-config-row {
                display: flex;
                justify-content: space-between;
                align-items: center;
                margin-bottom: 10px;
            }

            .fg-enemy-config-row label {
                flex: 1;
                color: #ccc;
                font-size: 14px;
            }

            .fg-enemy-config-row input {
                flex: 1;
                background-color: #333;
                border: 1px solid #555;
                border-radius: 4px;
                color: #fff;
                padding: 5px;
                width: 80px;
            }

            .fg-enemy-config-save {
                background-color: #4caf50;
                color: white;
                border: none;
                border-radius: 4px;
                padding: 8px 15px;
                margin-top: 10px;
                cursor: pointer;
                width: 100%;
                font-weight: bold;
            }

            .fg-enemy-config-save:hover {
                background-color: #45a049;
            }

            .fg-enemy-config-info {
                margin-top: 10px;
                font-size: 12px;
                color: #999;
                text-align: center;
            }

            .fg-enemy-config-row label[title] {
                cursor: help;
                border-bottom: 1px dotted #777;
            }

            @keyframes pulse {
                0% { transform: scale(1); }
                50% { transform: scale(1.03); }
                100% { transform: scale(1); }
            }

            .fg-damage-pulse {
                animation: pulse 0.4s ease;
            }

            .fg-dps-row {
                margin-top: 5px;
                border-top: 1px dotted #444;
                padding-top: 5px;
            }
        `;
        document.head.appendChild(style);
    },

    // Calculate damage based on stats
    calculateDamage: function(stats) {
        // Make sure stats exists and is an object
        if (!stats || typeof stats !== 'object') {
            return {
                sword: { normal: 0, crit: 0 },
                magic: { normal: 0, crit: 0 }
            };
        }

        // Base stats
        let attack = stats.attack || 100;
        let magicAttack = stats.magicAttack || 100;
        let penetration = stats.penetration || 0;
        let critDamage = stats.critDamage || 0;
        let skillAmp = stats.skillAmp || 0;
        let swordSkillAmp = stats.swordSkillAmp || 0;
        let magicSkillAmp = stats.magicSkillAmp || 0;
        let addDamage = stats.addDamage || 0;
        let finalDamageIncrease = stats.finalDamageIncrease || 0;
        let level = stats.level || 1;

        // Enemy stats (defaults)
        let enemyLevel = stats.enemyLevel || this.enemyConfig.level || level; // Default to same level as player
        let enemyDefense = stats.enemyDefense || this.enemyConfig.defense || 0;
        let enemyDamageReduction = stats.enemyDamageReduction || this.enemyConfig.damageReduction || 0;
        let enemyDamageReductionPercent = stats.enemyDamageReductionPercent || this.enemyConfig.damageReductionPercent || 0;
        let enemyFinalDamageDecrease = stats.enemyFinalDamageDecrease || this.enemyConfig.finalDamageDecrease || 0;

        // Calculate effective penetration vs defense (simplified)
        let effectiveDefense = enemyDefense;
        if (penetration > 0) {
            // Simplified penetration formula based on the provided code
            const penetrationEffect = (penetration * 1000000) / (penetration + 400);
            effectiveDefense = enemyDefense * Math.max(0.5, Math.min(1, (1000000 - penetrationEffect) / 1000000));
        }

        // SWORD DAMAGE CALCULATION
        // -----------------------

        // Base damage calculation
        let swordNormalDamage = attack;

        // Apply sword skill amplification
        let totalSwordAmp = skillAmp + swordSkillAmp;
        swordNormalDamage = swordNormalDamage * (100 + totalSwordAmp) / 100;

        // Apply level difference penalty (simplified)
        let levelDifference = Math.max(0, Math.min(25, enemyLevel - level));
        swordNormalDamage = swordNormalDamage * (100 - levelDifference * 2) / 100;

        // Apply defense calculation (simplified from the formula)
        swordNormalDamage = 2 * swordNormalDamage * (10000 - (effectiveDefense * 10000) / (effectiveDefense + swordNormalDamage)) / 10000;

        // Add flat damage
        swordNormalDamage += addDamage;

        // Apply damage reduction
        swordNormalDamage -= enemyDamageReduction;
        swordNormalDamage = swordNormalDamage * (100 - enemyDamageReductionPercent) / 100;

        // Apply final damage modifiers
        swordNormalDamage = swordNormalDamage * (100 + finalDamageIncrease) / 100;
        swordNormalDamage = swordNormalDamage * (100 - enemyFinalDamageDecrease) / 100;

        // Ensure damage is at least 1
        swordNormalDamage = Math.max(1, swordNormalDamage);

        // Calculate critical hit damage
        let swordCritDamage = swordNormalDamage * (100 + critDamage) / 100;

        // MAGIC DAMAGE CALCULATION
        // -----------------------

        // Base damage calculation
        let magicNormalDamage = magicAttack;

        // Apply magic skill amplification
        let totalMagicAmp = skillAmp + magicSkillAmp;
        magicNormalDamage = magicNormalDamage * (100 + totalMagicAmp) / 100;

        // Apply level difference penalty (simplified)
        magicNormalDamage = magicNormalDamage * (100 - levelDifference * 2) / 100;

        // Apply defense calculation (simplified from the formula)
        magicNormalDamage = 2 * magicNormalDamage * (10000 - (effectiveDefense * 10000) / (effectiveDefense + magicNormalDamage)) / 10000;

        // Add flat damage
        magicNormalDamage += addDamage;

        // Apply damage reduction
        magicNormalDamage -= enemyDamageReduction;
        magicNormalDamage = magicNormalDamage * (100 - enemyDamageReductionPercent) / 100;

        // Apply final damage modifiers
        magicNormalDamage = magicNormalDamage * (100 + finalDamageIncrease) / 100;
        magicNormalDamage = magicNormalDamage * (100 - enemyFinalDamageDecrease) / 100;

        // Ensure damage is at least 1
        magicNormalDamage = Math.max(1, magicNormalDamage);

        // Calculate critical hit damage
        let magicCritDamage = magicNormalDamage * (100 + critDamage) / 100;

        // Calculate DPS based on crit rate
        let critRate = stats.critRate || 0;
        let maxCritRate = stats.maxCritRate || 70; // Default max crit rate is 70%

        // Cap crit rate at the max value
        critRate = Math.min(critRate, maxCritRate);

        // Convert percentages to decimals for calculation
        const critRateDecimal = critRate / 100;

        // Calculate average damage considering crit chance
        const swordAvgDamage = swordNormalDamage * (1 - critRateDecimal) + swordCritDamage * critRateDecimal;
        const magicAvgDamage = magicNormalDamage * (1 - critRateDecimal) + magicCritDamage * critRateDecimal;

        // Assume 1 attack per second for DPS calculation
        const swordDPS = Math.round(swordAvgDamage);
        const magicDPS = Math.round(magicAvgDamage);

        // Round values for display
        return {
            sword: {
                normal: Math.round(swordNormalDamage),
                crit: Math.round(swordCritDamage),
                dps: swordDPS
            },
            magic: {
                normal: Math.round(magicNormalDamage),
                crit: Math.round(magicCritDamage),
                dps: magicDPS
            }
        };
    },

    // Calculate and update the damage display
    calculateAndUpdateDamage: function(stats) {
        // Safety check for stats
        if (!stats || typeof stats !== 'object') {
            return;
        }

        const damageValues = this.calculateDamage(stats);

        // Get the damage display elements
        const swordNormalDisplay = document.getElementById('fg-sword-normal-display');
        const swordNormalChangeEl = document.getElementById('fg-sword-normal-change');
        const swordCritDisplay = document.getElementById('fg-sword-crit-display');
        const swordCritChangeEl = document.getElementById('fg-sword-crit-change');
        const swordDpsDisplay = document.getElementById('fg-sword-dps-display');
        const swordDpsChangeEl = document.getElementById('fg-sword-dps-change');

        const magicNormalDisplay = document.getElementById('fg-magic-normal-display');
        const magicNormalChangeEl = document.getElementById('fg-magic-normal-change');
        const magicCritDisplay = document.getElementById('fg-magic-crit-display');
        const magicCritChangeEl = document.getElementById('fg-magic-crit-change');
        const magicDpsDisplay = document.getElementById('fg-magic-dps-display');
        const magicDpsChangeEl = document.getElementById('fg-magic-dps-change');

        if (swordNormalDisplay && swordCritDisplay && magicNormalDisplay && magicCritDisplay) {
            // Calculate changes from previous damage values
            const swordNormalChange = damageValues.sword.normal - this.previousSwordNormal;
            const swordCritChange = damageValues.sword.crit - this.previousSwordCrit;
            const swordDpsChange = damageValues.sword.dps - this.previousSwordDPS;
            const magicNormalChange = damageValues.magic.normal - this.previousMagicNormal;
            const magicCritChange = damageValues.magic.crit - this.previousMagicCrit;
            const magicDpsChange = damageValues.magic.dps - this.previousMagicDPS;

            // Format damage values with thousands separators
            const formattedSwordNormal = this.formatNumber(damageValues.sword.normal);
            const formattedSwordCrit = this.formatNumber(damageValues.sword.crit);
            const formattedSwordDps = this.formatNumber(damageValues.sword.dps);
            const formattedMagicNormal = this.formatNumber(damageValues.magic.normal);
            const formattedMagicCrit = this.formatNumber(damageValues.magic.crit);
            const formattedMagicDps = this.formatNumber(damageValues.magic.dps);

            // Update the displays
            swordNormalDisplay.textContent = formattedSwordNormal;
            swordCritDisplay.textContent = formattedSwordCrit;
            swordDpsDisplay.textContent = formattedSwordDps;
            magicNormalDisplay.textContent = formattedMagicNormal;
            magicCritDisplay.textContent = formattedMagicCrit;
            magicDpsDisplay.textContent = formattedMagicDps;

            // Add/remove pulse animation classes
            const allDisplays = [swordNormalDisplay, swordCritDisplay, swordDpsDisplay,
                               magicNormalDisplay, magicCritDisplay, magicDpsDisplay];
            allDisplays.forEach(display => {
                display.classList.remove('fg-damage-pulse');
                void display.offsetWidth; // Force reflow to restart animation
                display.classList.add('fg-damage-pulse');
            });

            // Update change indicators if not the first calculation
            this.updateChangeIndicator(swordNormalChangeEl, this.previousSwordNormal, swordNormalChange);
            this.updateChangeIndicator(swordCritChangeEl, this.previousSwordCrit, swordCritChange);
            this.updateChangeIndicator(swordDpsChangeEl, this.previousSwordDPS, swordDpsChange);
            this.updateChangeIndicator(magicNormalChangeEl, this.previousMagicNormal, magicNormalChange);
            this.updateChangeIndicator(magicCritChangeEl, this.previousMagicCrit, magicCritChange);
            this.updateChangeIndicator(magicDpsChangeEl, this.previousMagicDPS, magicDpsChange);

            // Store current damage values for next comparison
            this.previousSwordNormal = damageValues.sword.normal;
            this.previousSwordCrit = damageValues.sword.crit;
            this.previousSwordDPS = damageValues.sword.dps;
            this.previousMagicNormal = damageValues.magic.normal;
            this.previousMagicCrit = damageValues.magic.crit;
            this.previousMagicDPS = damageValues.magic.dps;

            // Store damage values in BuildPlanner and StatsSummary for other systems to access
            if (typeof BuildPlanner !== 'undefined') {
                BuildPlanner.damageValues = damageValues;
                BuildPlanner.swordDPS = damageValues.sword.dps;
                BuildPlanner.magicDPS = damageValues.magic.dps;
            }

            if (typeof StatsSummary !== 'undefined') {
                StatsSummary.damageValues = damageValues;
                StatsSummary.swordDPS = damageValues.sword.dps;
                StatsSummary.magicDPS = damageValues.magic.dps;
            }
        }
    },

    // Helper to update change indicators
    updateChangeIndicator: function(changeElement, previousValue, changeValue) {
        if (previousValue > 0) {
            const absChangeValue = Math.abs(changeValue).toLocaleString(undefined, {maximumFractionDigits: 0});
            if (changeValue > 0) {
                changeElement.textContent = `+${absChangeValue}`;
                changeElement.className = 'fg-damage-change fg-damage-increase';
            } else if (changeValue < 0) {
                changeElement.textContent = `-${absChangeValue}`;
                changeElement.className = 'fg-damage-change fg-damage-decrease';
            } else {
                changeElement.textContent = '';
                changeElement.className = 'fg-damage-change';
            }
        } else {
            changeElement.textContent = '';
        }
    },

    // Helper to format numbers with commas for thousands
    formatNumber: function(number) {
        return Math.round(number).toLocaleString(undefined, {maximumFractionDigits: 0});
    },

    // Configure enemy stats for more accurate damage calculation
    configureEnemy: function(config) {
        if (!config || typeof config !== 'object') {
            return;
        }

        // Update enemy configuration
        if (typeof config.level === 'number') this.enemyConfig.level = config.level;
        if (typeof config.defense === 'number') this.enemyConfig.defense = config.defense;
        if (typeof config.damageReduction === 'number') this.enemyConfig.damageReduction = config.damageReduction;
        if (typeof config.damageReductionPercent === 'number') this.enemyConfig.damageReductionPercent = config.damageReductionPercent;
        if (typeof config.finalDamageDecrease === 'number') this.enemyConfig.finalDamageDecrease = config.finalDamageDecrease;

        // Recalculate damage if we have stats
        if (typeof StatsSummary !== 'undefined') {
            if (StatsSummary.derivedStats && Object.keys(StatsSummary.derivedStats).length > 0) {
                this.calculateAndUpdateDamage(StatsSummary.derivedStats);
            } else if (StatsSummary.totalStats && Object.keys(StatsSummary.totalStats).length > 0) {
                this.calculateAndUpdateDamage(StatsSummary.totalStats);
            }
        } else if (typeof BuildPlanner !== 'undefined' && BuildPlanner.totalStats) {
            this.calculateAndUpdateDamage(BuildPlanner.totalStats);
        }
    }
};

// Initialize on DOM content loaded
document.addEventListener('DOMContentLoaded', function() {
    // Defer initialization slightly to ensure StatsSummary is loaded
    setTimeout(() => {
        DamageCalculator.init();
    }, 500);
});
