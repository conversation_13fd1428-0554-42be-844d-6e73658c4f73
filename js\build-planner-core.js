/**
 * Build Planner Core
 * Main functionality for the build planner including system navigation and stats management
 */

// Initialize the build planner namespace
const BuildPlanner = {
    // Current active system
    activeSystem: 'class',

    // Storage key for remembering the active system
    ACTIVE_SYSTEM_STORAGE_KEY: 'fg_active_system',

    // Systems configuration - define all available systems
    systems: [
        { id: 'class', name: 'Stats' },
        { id: 'pet', name: 'Pet' },
        { id: 'stellar', name: 'Stellar Link' },
        { id: 'honor', name: 'Honor Medal' },
        { id: 'equipment', name: 'Equipment' },
        { id: 'costumes', name: 'Costumes' },
        { id: 'gold-merit', name: 'Gold Merit' },
        { id: 'platinum-merit', name: 'Platinum Merit' },
        { id: 'force-wing', name: '<PERSON> Wing' },
        { id: 'essence-runes', name: '<PERSON><PERSON><PERSON>' },
        { id: 'karma-runes', name: 'Karma Runes' },
        { id: 'overlord-mastery', name: 'Overlord Mastery' },
        { id: 'achievement', name: 'Achievement' }
    ],

    // Track stats from all systems
    stats: {
        class: {},
        pet: {},
        stellar: {},
        honor: {},
        'essence-runes': {},
        'karma-runes': {},
        equipment: {},
        costumes: {},
        'overlord-mastery': {},
        achievement: {}
    },

    // Track loaded systems to prevent duplicate loading
    loadedSystems: {},

    // Initialize the planner
    init: function() {
        // Load saved active system from localStorage if it exists
        this.loadActiveSystemFromStorage();

        // Set up event listeners
        this.setupEventListeners();

        // Initialize the stats summary if available
        if (typeof StatsSummary !== 'undefined') {
            StatsSummary.init();
        }

        // Update UI to show active system button as selected
        this.updateActiveSystemButton();

        // Show initial active system
        this.showActiveSystem();

        // Create CSS for the loading indicator
        this.createLoaderStyles();

        // Load all systems in parallel
        Promise.all(
            this.systems.map(system =>
                this.loadSystemScript(system.id)
                    .then(() => this.initSystemIfNeeded(system.id))
                    .catch(err => {})
            )
        ).then(() => {
            this.showActiveSystem();
            this.refreshAllSystemStats();
        });
    },

    // Create CSS for the loading indicator
    createLoaderStyles: function() {
        const style = document.createElement('style');
        style.textContent = `
            #fg-system-loader {
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0,0,0,0.5);
                display: flex;
                justify-content: center;
                align-items: center;
                z-index: 9999;
            }
            .fg-loader-spinner {
                width: 50px;
                height: 50px;
                border: 5px solid rgba(255,255,255,0.3);
                border-radius: 50%;
                border-top-color: #fff;
                animation: spin 1s ease infinite;
            }
            @keyframes spin {
                to { transform: rotate(360deg); }
            }
        `;
        document.head.appendChild(style);
    },

    // Show/hide loading indicator
    showLoadingIndicator: function(show) {
        let loader = document.getElementById('fg-system-loader');

        if (!loader && show) {
            loader = document.createElement('div');
            loader.id = 'fg-system-loader';
            loader.innerHTML = '<div class="fg-loader-spinner"></div>';
            document.querySelector('.fg-build-planner-container').appendChild(loader);
        }

        if (loader) {
            loader.style.display = show ? 'flex' : 'none';
        }
    },

    // Dynamically load system JS and CSS
    loadSystemScript: function(systemId) {
        return new Promise((resolve, reject) => {
            // Skip if already loaded
            if (this.loadedSystems[systemId]) {
                return resolve();
            }

            // Find the URL for this system
            const scriptUrl = forceguidesPlannerData.systemJsUrls[systemId];
            if (!scriptUrl) {
                return resolve(); // Resolve anyway to not break the chain
            }

            // Load CSS first if available
            const cssUrl = forceguidesPlannerData.systemCssUrls[systemId];
            if (cssUrl) {
                const link = document.createElement('link');
                link.rel = 'stylesheet';
                link.href = cssUrl;
                document.head.appendChild(link);
            } else if (systemId === 'karma-runes') {
                // Karma runes shares CSS with essence runes
                const essenceRunesCssUrl = forceguidesPlannerData.systemCssUrls['essence-runes'];
                if (essenceRunesCssUrl) {
                    const link = document.createElement('link');
                    link.rel = 'stylesheet';
                    link.href = essenceRunesCssUrl;
                    document.head.appendChild(link);
                }
            }

            // Create and load the script
            const script = document.createElement('script');
            script.src = scriptUrl;
            script.async = true;

            script.onload = () => {
                // Wait a small amount of time for the script to execute and create global objects
                setTimeout(() => {
                    // Add fallback script to ensure global object exists
                    // This handles cases where the script loaded but didn't properly expose the global object
                    const systemObjectName = this.getSystemObjectName(systemId);
                    const fallbackScript = document.createElement('script');
                    fallbackScript.textContent = `
                        // Ensure the ${systemObjectName} exists as a global object
                        if (typeof ${systemObjectName} !== 'undefined' && typeof window.${systemObjectName} === 'undefined') {
                            window.${systemObjectName} = ${systemObjectName};
                        }
                    `;
                    document.head.appendChild(fallbackScript);

                    // Set loaded status
                    this.loadedSystems[systemId] = true;
                    resolve();
                }, 100); // Small timeout to ensure script execution completes
            };

            script.onerror = (error) => {
                reject(error);
            };

            document.body.appendChild(script);
        });
    },

    // Set up UI event listeners
    setupEventListeners: function() {
        // System navigation buttons
        const systemButtons = document.querySelectorAll('.fg-system-button:not(.disabled)');
        systemButtons.forEach(button => {
            button.addEventListener('click', () => {
                const system = button.getAttribute('data-system');
                this.setActiveSystem(system);
            });
        });
    },

    // Set the active system
    setActiveSystem: function(systemId) {
        // Don't reload if it's already the active system
        if (this.activeSystem === systemId) {
            return;
        }

        // Show loading indicator
        this.showLoadingIndicator(true);

        // Load the system script if needed
        this.loadSystemScript(systemId)
            .then(() => {
                // Update active system
                this.activeSystem = systemId;

                // Save active system to localStorage for persistence
                this.saveActiveSystemToStorage();

                // Update UI
                this.updateActiveSystemButton();

                // Show active system panel
                this.showActiveSystem();

                // Initialize the specific system if it has an init function
                this.initSystemIfNeeded(systemId);

                // Hide loading indicator
                this.showLoadingIndicator(false);
            })
            .catch(error => {
                this.showLoadingIndicator(false);
            });
    },

    // Initialize a system if it hasn't been initialized yet
    initSystemIfNeeded: function(systemId) {
        // Check if the system has an initializer function
        const initFunctionName = `init${this.capitalizeFirstLetter(systemId)}System`;
        if (typeof this[initFunctionName] === 'function') {
            // Call the system-specific initializer
            this[initFunctionName]();
        } else {
            // Try generic initialization approach
            this.initGenericSystem(systemId);
        }
    },

    // Generic system initializer that works with any properly formatted system
    initGenericSystem: function(systemId) {
        const systemObjectName = this.getSystemObjectName(systemId);

        // First check if system is already initialized
        if (window[systemObjectName] && window[systemObjectName].isInitialized === true) {
            return true;
        }

        // First try: direct access to window object
        if (window[systemObjectName] && typeof window[systemObjectName].init === 'function') {
            window[systemObjectName].init();
            return true;
        }

        // Second try: try to find the object in another scope
        if (typeof eval(systemObjectName) !== 'undefined' && typeof window[systemObjectName] === 'undefined') {
            try {
                window[systemObjectName] = eval(systemObjectName);

                if (typeof window[systemObjectName].init === 'function') {
                    window[systemObjectName].init();
                    return true;
                }
            } catch (e) {
                return false;
            }
        }

        return false;
    },

    // Helper to capitalize first letter for function names
    capitalizeFirstLetter: function(string) {
        return string.replace(/-([a-z])/g, function(g) {
            return g[1].toUpperCase();
        }).replace(/^([a-z])/, function(g) {
            return g.toUpperCase();
        });
    },

    // Get the correct system object name (handles special cases)
    getSystemObjectName: function(systemId) {
        // Special case for honor system
        if (systemId === 'honor') {
            return 'HonorMedalSystem';
        }

        // Default naming convention
        return this.capitalizeFirstLetter(systemId) + 'System';
    },

    // Show the active system panel
    showActiveSystem: function() {
        const panels = document.querySelectorAll('.fg-system-panel');
        panels.forEach(panel => {
            if (panel.id === `fg-${this.activeSystem}-system`) {
                panel.classList.add('active');
            } else {
                panel.classList.remove('active');
            }
        });
    },

    // Update stats from a system
    updateStats: function(systemId, newStats) {
        // Store stats for the system
        this.stats[systemId] = newStats;

        // Recalculate total stats
        this.calculateTotalStats();
    },

    // Used by StatsSummary to update stats from a system
    updateSystemStats: function(systemId, newStats) {
        // Store stats for the system
        this.stats[systemId] = newStats;

        // Recalculate total stats
        this.calculateTotalStats();
    },

    // Calculate total stats from all systems and update UI
    calculateTotalStats: function() {
        // Object to store combined stats
        const totalStats = {};

        // Combine stats from all systems
        Object.values(this.stats).forEach(systemStats => {
            Object.entries(systemStats).forEach(([statId, value]) => {
                if (totalStats[statId]) {
                    totalStats[statId] += value;
                } else {
                    totalStats[statId] = value;
                }
            });
        });

        // Add base stats from StatsConfig
        if (typeof StatsConfig !== 'undefined') {
            const baseStats = StatsConfig.getBaseStats();
            Object.entries(baseStats).forEach(([statId, baseValue]) => {
                if (!totalStats[statId]) {
                    totalStats[statId] = baseValue;
                } else {
                    totalStats[statId] += baseValue;
                }
            });
        }

        // Update the StatsSummary module with total stats
        if (typeof StatsSummary !== 'undefined') {
            StatsSummary.updateTotalStats(totalStats);
        }
    },

    // Export the current build as JSON
    exportBuild: function() {
        const buildData = {
            stats: this.stats,
            timestamp: new Date().toISOString(),
            version: '1.0'
        };

        return JSON.stringify(buildData);
    },

    // Import a build from JSON
    importBuild: function(jsonData) {
        try {
            const buildData = JSON.parse(jsonData);

            // Validate the build data
            if (!buildData.stats) {
                throw new Error('Invalid build data: missing stats');
            }

            // Import stats for each system
            this.systems.forEach(system => {
                if (buildData.stats[system.id]) {
                    this.stats[system.id] = buildData.stats[system.id];
                }
            });

            // Recalculate stats
            this.calculateTotalStats();

            // Refresh UI for current system
            this.showActiveSystem();

            return true;
        } catch (error) {
            return false;
        }
    },

    // Modified to work with our dynamic loading system
    initPetSystem: function() {
        // Check if the system is already initialized
        if (window.PetSystem && window.PetSystem.isInitialized === true) {
            return true;
        }

        const initFunction = () => {
            if (window.PetSystem && typeof window.PetSystem.init === 'function') {
                window.PetSystem.init();
                return true;
            }
            return false;
        };

        // Try immediately first
        if (initFunction()) return;

        // Try to rescue the situation - look for PetSystem in any scope
        if (typeof PetSystem !== 'undefined' && typeof window.PetSystem === 'undefined') {
            window.PetSystem = PetSystem;

            if (initFunction()) return;
        }

        // Last resort - reload the script directly
        const scriptUrl = forceguidesPlannerData.systemJsUrls.pet;
        if (scriptUrl) {
            const script = document.createElement('script');
            script.src = scriptUrl + '&forcereload=true'; // Force browser to reload
            script.async = false; // Use synchronous loading for this emergency case

            script.onload = () => {
                setTimeout(() => {
                    initFunction();
                }, 200);
            };

            document.body.appendChild(script);
        }
    },

    // Load the saved active system from localStorage
    loadActiveSystemFromStorage: function() {
        try {
            const savedSystem = localStorage.getItem(this.ACTIVE_SYSTEM_STORAGE_KEY);
            if (savedSystem) {
                // Verify this is a valid system
                const isValidSystem = this.systems.some(system => system.id === savedSystem);
                if (isValidSystem) {
                    this.activeSystem = savedSystem;
                } else {
                    // Remove invalid system from storage
                    localStorage.removeItem(this.ACTIVE_SYSTEM_STORAGE_KEY);
                }
            }
        } catch (error) {
            // Clear potentially corrupted storage
            try {
                localStorage.removeItem(this.ACTIVE_SYSTEM_STORAGE_KEY);
            } catch (e) {
                // Ignore errors on cleanup
            }
        }
    },

    // Save the current active system to localStorage
    saveActiveSystemToStorage: function() {
        try {
            localStorage.setItem(this.ACTIVE_SYSTEM_STORAGE_KEY, this.activeSystem);
        } catch (error) {
            // Handle storage errors silently
        }
    },

    // Update the active system button in the UI
    updateActiveSystemButton: function() {
        const buttons = document.querySelectorAll('.fg-system-button');
        buttons.forEach(button => {
            const systemId = button.getAttribute('data-system');
            if (systemId === this.activeSystem) {
                button.classList.add('active');
            } else {
                button.classList.remove('active');
            }
        });
    },

    // Initialize Stellar system
    initStellarSystem: function() {
        if (!window.StellarSystem) {
            window.StellarSystem = {
                isInitialized: false,
                init: function() { this.isInitialized = true; },
                updateStats: function() {}
            };
        }

        if (!window.StellarSystem.isInitialized && window.StellarSystem.init) {
            window.StellarSystem.init();
        }
    },

    // Initialize Equipment system
    initEquipmentSystem: function() {
        if (!window.EquipmentSystem) {
            return;
        }

        if (!window.EquipmentSystem.isInitialized && window.EquipmentSystem.init) {
            window.EquipmentSystem.init();
        }
    },

    // Attempt to refresh stats from all loaded systems
    refreshAllSystemStats: function() {
        const self = this;

        // Wait a short time to ensure all systems are fully loaded
        setTimeout(() => {
            // Iterate through each system
            self.systems.forEach(system => {
                const systemId = system.id;
                const systemObjectName = self.getSystemObjectName(systemId);

                // Only refresh stats for systems that are already loaded
                if (self.loadedSystems[systemId] && window[systemObjectName]) {
                    // If the system has an updateStats method, call it
                    if (typeof window[systemObjectName].updateStats === 'function') {
                        window[systemObjectName].updateStats();
                    }
                }
            });

            // Force recalculation of total stats
            self.calculateTotalStats();
        }, 500);
    },
};

// Initialize when document is ready
document.addEventListener('DOMContentLoaded', () => {
    // Only initialize if we're on the build planner page
    if (document.querySelector('.fg-build-planner-container')) {
        BuildPlanner.init();
    }
});