/**
 * Selection Window Manager
 *
 * A reusable component for creating and managing selection windows/popups
 * across different systems in the Force Guides Build Planner.
 *
 * This component handles:
 * - Creating selection windows with customizable content
 * - Showing/hiding windows
 * - Positioning (fixed center or relative to element)
 * - Event handling for selections and closing
 */

class SelectionWindowManager {
    /**
     * Create a new SelectionWindowManager
     * @param {Object} options - Configuration options
     * @param {string} options.id - Unique ID for this selection window
     * @param {string} options.title - Title to display in the header
     * @param {string} options.className - Additional CSS class for styling
     * @param {boolean} options.fixedPosition - If true, window is fixed in center of screen, otherwise positioned relative to target
     * @param {Function} options.onSelect - Callback when an option is selected
     * @param {Function} options.onClose - Callback when window is closed
     */
    constructor(options = {}) {
        this.options = Object.assign({
            id: 'fg-selection-window-' + Date.now(),
            title: 'Select an Option',
            className: '',
            fixedPosition: true,
            onSelect: () => {},
            onClose: () => {}
        }, options);

        this.isCreated = false;
        this.isVisible = false;
        this.elements = {};

        // Create the window element
        this._createWindow();
    }

    /**
     * Create the selection window DOM elements
     * @private
     */
    _createWindow() {
        // Check if window already exists
        if (this.isCreated) return;

        // Create window element
        const windowEl = document.createElement('div');
        windowEl.id = this.options.id;
        windowEl.className = `fg-selection-window ${this.options.className}`;

        // Set positioning class
        if (this.options.fixedPosition) {
            windowEl.classList.add('fg-selection-window-fixed');
        } else {
            windowEl.classList.add('fg-selection-window-relative');
        }

        // Create window content
        windowEl.innerHTML = `
            <div class="fg-selection-window-content">
                <div class="fg-selection-window-header">
                    <h3 class="fg-selection-window-title">${this.options.title}</h3>
                    <button class="fg-selection-window-close">&times;</button>
                </div>
                <div class="fg-selection-window-body">
                    <div class="fg-selection-window-options"></div>
                </div>
            </div>
        `;

        // Add to DOM
        document.body.appendChild(windowEl);

        // Cache elements
        this.elements.window = windowEl;
        this.elements.content = windowEl.querySelector('.fg-selection-window-content');
        this.elements.title = windowEl.querySelector('.fg-selection-window-title');
        this.elements.closeBtn = windowEl.querySelector('.fg-selection-window-close');
        this.elements.body = windowEl.querySelector('.fg-selection-window-body');
        this.elements.options = windowEl.querySelector('.fg-selection-window-options');

        // Set up event listeners
        this._setupEventListeners();

        this.isCreated = true;
    }

    /**
     * Set up event listeners for the window
     * @private
     */
    _setupEventListeners() {
        // Close button click
        this.elements.closeBtn.addEventListener('click', () => {
            this.hide();
            if (typeof this.options.onClose === 'function') {
                this.options.onClose();
            }
        });

        // Click outside to close
        this.elements.window.addEventListener('click', (e) => {
            if (e.target === this.elements.window) {
                this.hide();
                if (typeof this.options.onClose === 'function') {
                    this.options.onClose();
                }
            }
        });

        // Option selection via event delegation
        this.elements.options.addEventListener('click', (e) => {
            const option = e.target.closest('.fg-selection-option');
            if (option) {
                const data = this._getOptionData(option);
                console.log('Selection data:', data, 'Option dataset:', option.dataset);

                // Skip headers and separators
                if (option.classList.contains('fg-selection-category-header') ||
                    option.classList.contains('fg-selection-separator')) {
                    return;
                }

                // Debug log to help diagnose data attribute issues
                console.log('Selection window sending data to callback:', data);
                console.log('Raw dataset from element:', option.dataset);
                console.log('Data attributes on element:', Array.from(option.attributes)
                    .filter(attr => attr.name.startsWith('data-'))
                    .map(attr => `${attr.name}="${attr.value}"`)
                );

                if (typeof this.options.onSelect === 'function') {
                    this.options.onSelect(data);
                }
                this.hide();
            }
        });
    }

    /**
     * Extract data attributes from an option element
     * @private
     * @param {HTMLElement} optionEl - The option element
     * @returns {Object} - Object containing all data attributes
     */
    _getOptionData(optionEl) {
        const data = {};

        // Get all data attributes
        for (const key in optionEl.dataset) {
            // Try to parse JSON if possible, otherwise use string value
            try {
                data[key] = JSON.parse(optionEl.dataset[key]);
            } catch (e) {
                data[key] = optionEl.dataset[key];
            }

            // Handle camelCase conversion issues
            // If the key is all lowercase but was originally camelCase,
            // add the camelCase version as well for backward compatibility
            if (key.toLowerCase() === key && key.length > 1) {
                // Try to reconstruct the original camelCase key
                // For example, convert 'statid' to 'statId'
                // Common patterns in our codebase
                const commonCamelCasePatterns = {
                    'statid': 'statId',
                    'colorid': 'colorId',
                    'typeid': 'typeId',
                    'itemid': 'itemId',
                    'nodeid': 'nodeId',
                    'slotid': 'slotId',
                    'rankid': 'rankId',
                    'levelid': 'levelId',
                    'actionid': 'actionId'
                };

                // Check if this is a known pattern
                let possibleCamelCase = commonCamelCasePatterns[key] || key;

                // If we created a different key, add it as an alias
                if (possibleCamelCase !== key) {
                    data[possibleCamelCase] = data[key];
                }
            }
        }

        return data;
    }

    /**
     * Show the selection window
     * @param {Object} params - Show parameters
     * @param {Array} params.options - Array of options to display
     * @param {HTMLElement} [params.targetElement] - Element to position relative to (for non-fixed positioning)
     * @param {Object} [params.position] - Position overrides {top, left} as percentages
     * @param {string} [params.title] - Override the window title
     */
    show(params = {}) {
        if (!this.isCreated) {
            this._createWindow();
        }

        // Update title if provided
        if (params.title) {
            this.elements.title.textContent = params.title;
        }

        // Generate options HTML
        if (Array.isArray(params.options)) {
            let optionsHTML = '';

            params.options.forEach(option => {
                // Create data attributes string
                const dataAttrs = Object.entries(option.data || {})
                    .map(([key, value]) => {
                        // Make sure the value is properly escaped for HTML attributes
                        const escapedValue = String(value).replace(/"/g, '&quot;');
                        return `data-${key}="${escapedValue}"`;
                    })
                    .join(' ');

                console.log('Option data:', option.data, 'Data attributes:', dataAttrs);

                // Create option HTML
                // Skip empty options
                if (option.html || option.text) {
                    optionsHTML += `
                        <div class="fg-selection-option ${option.className || ''}" ${dataAttrs}>
                            ${option.html || option.text || ''}
                        </div>
                    `;
                }
            });

            this.elements.options.innerHTML = optionsHTML;
        }

        // Position the window
        if (!this.options.fixedPosition && params.targetElement) {
            this._positionRelativeToElement(params.targetElement);
        } else if (params.position) {
            this._setPosition(params.position);
        }

        // Show the window
        this.elements.window.classList.add('active');
        this.isVisible = true;
    }

    /**
     * Position the window relative to a target element
     * @private
     * @param {HTMLElement} targetElement - Element to position relative to
     */
    _positionRelativeToElement(targetElement) {
        const targetRect = targetElement.getBoundingClientRect();
        const windowWidth = window.innerWidth;
        const windowHeight = window.innerHeight;

        // Calculate position (centered above the element by default)

        // Position horizontally centered relative to target
        let left = targetRect.left + (targetRect.width / 2);
        left = (left / windowWidth) * 100; // Convert to percentage

        // Position above the target
        let top = targetRect.top - 10; // 10px above the target
        top = (top / windowHeight) * 100; // Convert to percentage

        // Apply position
        this._setPosition({ top, left });
    }

    /**
     * Set the position of the window
     * @private
     * @param {Object} position - Position {top, left} as percentages
     */
    _setPosition(position) {
        if (position.top !== undefined) {
            this.elements.content.style.top = `${position.top}%`;
        }

        if (position.left !== undefined) {
            this.elements.content.style.left = `${position.left}%`;
        }

        // Always center the content box
        this.elements.content.style.transform = 'translate(-50%, -50%)';
    }

    /**
     * Hide the selection window
     */
    hide() {
        if (this.isCreated) {
            this.elements.window.classList.remove('active');
            this.isVisible = false;
        }
    }

    /**
     * Update the window options
     * @param {Array} options - Array of option objects
     */
    updateOptions(options) {
        if (!this.isCreated) return;

        let optionsHTML = '';

        options.forEach(option => {
            // Create data attributes string
            const dataAttrs = Object.entries(option.data || {})
                .map(([key, value]) => `data-${key}="${value}"`)
                .join(' ');

            // Create option HTML
            optionsHTML += `
                <div class="fg-selection-option ${option.className || ''}" ${dataAttrs}>
                    ${option.html || option.text || ''}
                </div>
            `;
        });

        this.elements.options.innerHTML = optionsHTML;
    }

    /**
     * Update the window title
     * @param {string} title - New title
     */
    updateTitle(title) {
        if (this.isCreated) {
            this.elements.title.textContent = title;
        }
    }

    /**
     * Destroy the window and remove it from the DOM
     */
    destroy() {
        if (this.isCreated) {
            this.elements.window.remove();
            this.isCreated = false;
            this.isVisible = false;
        }
    }
}

// Export the class
window.SelectionWindowManager = SelectionWindowManager;
