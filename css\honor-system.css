/* Honor Medal System Styles */

/* Main container */
.fg-honor-system-container {
    padding: 20px;
    max-width: 100%;
    background-color: #1c1e22;
    border-radius: 5px;
    border: 1px solid #3c3f44;
    box-sizing: border-box;
    color: #f0f0f0;
}



/* Rank panels */
.fg-honor-rank-panel {
    background: rgba(28, 30, 34, 0.5);
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 20px;
    border: 1px solid #3c3f44;
}

.fg-honor-rank-header {
    font-size: 1.1em;
    font-weight: bold;
    color: #ffc107;
    display: block;
    margin-bottom: 10px;
}

/* Rank header with level up button */
.fg-honor-rank-header-container {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
}

.fg-honor-rank-level-up {
    margin-left: 8px;
    width: 24px;
    height: 24px;
    background-color: rgba(78, 154, 6, 0.8);
    color: white;
    border: none;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 14px;
    cursor: pointer;
    padding: 0;
    line-height: 1;
}

.fg-honor-rank-level-up:hover {
    background-color: rgba(95, 177, 7, 0.9);
}

.fg-honor-rank-level-up:disabled {
    background-color: rgba(78, 154, 6, 0.3);
    cursor: not-allowed;
}

.fg-honor-rank-level-down {
    margin-left: 4px;
    width: 24px;
    height: 24px;
    background-color: rgba(154, 78, 6, 0.8);
    color: white;
    border: none;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 14px;
    cursor: pointer;
    padding: 0;
    line-height: 1;
}

.fg-honor-rank-level-down:hover {
    background-color: rgba(177, 95, 7, 0.9);
}

.fg-honor-rank-level-down:disabled {
    background-color: rgba(154, 78, 6, 0.3);
    cursor: not-allowed;
}

/* Slot containers */
.fg-honor-slots-container {
    display: flex;
    flex-wrap: wrap;
    gap: 12px;
}

/* Medal slots */
.fg-honor-slot {
    min-height: 45px;
    max-height: 52px;
    aspect-ratio: 1/1;
    background-color: rgba(0, 0, 0, 0.2);
    border: 1px solid #444;
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    overflow: visible;
    transition: all 0.2s ease;
    cursor: pointer;
    margin-bottom: 22px;
}

.fg-honor-slot.empty {
    border-color: #333;
    background: linear-gradient(135deg, #1a1a1a 0%, #222 100%);
    margin-bottom: 0;
}

.fg-honor-slot.empty:hover {
    border-color: #4f5a68;
    box-shadow: inset 0 0 4px rgba(79, 90, 104, 0.7);
}

.fg-honor-slot.selected {
    border-color: #4c8e50;
    background: rgba(27, 94, 32, 0.3);
}

.fg-honor-slot.selected:hover {
    border-color: #4f5a68;
    box-shadow: inset 0 0 4px rgba(79, 90, 104, 0.7);
}

/* Empty slot styling */
.fg-honor-slot-empty {
    width: 24px;
    height: 24px;
    background-color: #444;
    border-radius: 50%;
    position: relative;
}

.fg-honor-slot-empty:before,
.fg-honor-slot-empty:after {
    content: "";
    position: absolute;
    background-color: #555;
}

.fg-honor-slot-empty:before {
    width: 12px;
    height: 2px;
    top: 11px;
    left: 6px;
}

.fg-honor-slot-empty:after {
    width: 2px;
    height: 12px;
    top: 6px;
    left: 11px;
}

/* Stat content in slots */
.fg-honor-slot-stat {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    text-align: center;
    padding: 2px;
    position: relative;
}

.fg-honor-slot-stat-icon {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.fg-honor-slot-stat-icon img {
    width: 100%;
    height: 100%;
    object-fit: contain;
}

/* Remove button */
.fg-honor-slot-remove {
    position: absolute;
    top: 2px;
    left: 2px;
    width: 16px;
    height: 16px;
    background: rgba(187, 66, 66, 0.7);
    color: white;
    border: none;
    border-radius: 50%;
    font-size: 12px;
    line-height: 1;
    padding: 0;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: background-color 0.2s;
    z-index: 5;
}

.fg-honor-slot-remove:hover {
    background: rgba(211, 47, 47, 0.9);
}

/* Stat value display below slot */
.fg-honor-slot-details {
    position: absolute;
    bottom: -20px;
    left: 0;
    width: 100%;
    text-align: center;
    font-size: 0.9em;
    line-height: 1.2;
    white-space: nowrap;
    pointer-events: none;
}

.fg-honor-slot-level {
    font-size: 0.7em;
    color: #aaa;
    display: inline-block;
    margin-right: 3px;
}

.fg-honor-slot-stat-value {
    font-size: 0.9em;
    color: #ffc107;
    font-weight: bold;
}

/* Upgrade button */
.fg-honor-slot-upgrade {
    display: none;
}

/* Selected stats summary */
.fg-honor-selected-stats {
    margin-top: 20px;
    background: rgba(28, 30, 34, 0.5);
    border: 1px solid #3c3f44;
    border-radius: 8px;
    padding: 15px;
}

.fg-honor-selected-stats h3 {
    margin-top: 0;
    color: #ffc107;
    margin-bottom: 10px;
    font-size: 1.1em;
}

.fg-honor-selected-list {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
}

.fg-honor-selected-stat {
    background-color: #333;
    border: 1px solid #444;
    border-radius: 4px;
    padding: 5px 10px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: calc(50% - 5px);
}

.fg-honor-selected-stat-info {
    display: flex;
    align-items: center;
}

.fg-honor-selected-stat-icon {
    width: 20px;
    height: 20px;
    margin-right: 5px;
}

.fg-honor-selected-stat-name {
    font-size: 0.9em;
}

.fg-honor-selected-stat-value {
    color: #ffc107;
    font-weight: bold;
}

/* Popup for stat selection */
.fg-honor-stat-popup {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.7);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    opacity: 0;
    visibility: hidden;
    transition: opacity 0.3s ease, visibility 0.3s ease;
}

.fg-honor-stat-popup.active {
    opacity: 1;
    visibility: visible;
}

.fg-honor-stat-popup-content {
    background-color: #2a2a2a;
    border: 1px solid #444;
    border-radius: 4px;
    width: 90%;
    max-width: 500px;
    max-height: 80vh;
    display: flex;
    flex-direction: column;
}

.fg-honor-stat-popup-header {
    padding: 15px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    border-bottom: 1px solid #444;
}

.fg-honor-stat-popup-header h3 {
    margin: 0;
    color: #ffc107;
    font-size: 1.1em;
}

.fg-honor-stat-popup-close {
    background-color: transparent;
    color: #aaa;
    border: none;
    font-size: 1.5em;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 30px;
    height: 30px;
    border-radius: 50%;
}

.fg-honor-stat-popup-close:hover {
    color: white;
    background-color: rgba(255, 255, 255, 0.1);
}

.fg-honor-stat-popup-body {
    padding: 15px;
    overflow-y: auto;
    max-height: calc(80vh - 60px);
}

.fg-honor-stat-options {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.fg-honor-stat-option {
    background-color: #333;
    border: 1px solid #444;
    border-radius: 4px;
    padding: 8px 12px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    cursor: pointer;
    transition: background-color 0.2s ease;
}

.fg-honor-stat-option:hover {
    background-color: #444;
    border-color: #555;
}

.fg-honor-stat-option-left {
    display: flex;
    align-items: center;
}

.fg-honor-stat-option-icon {
    width: 24px;
    height: 24px;
    margin-right: 8px;
}

.fg-honor-stat-option-name {
    font-size: 0.95em;
}

.fg-honor-stat-option-name .chance {
    font-size: 0.85em;
    color: #aaa;
    margin-left: 5px;
}

.fg-honor-stat-option-value {
    color: #ffc107;
    font-size: 0.9em;
}

/* No stats placeholder */
.no-stats {
    color: #aaa;
    font-style: italic;
}

/* Level up animation */
@keyframes levelUpPulse {
    0% {
        transform: scale(1);
        box-shadow: 0 0 0 0 rgba(95, 177, 7, 0.7);
    }
    50% {
        transform: scale(1.05);
        box-shadow: 0 0 0 10px rgba(95, 177, 7, 0);
    }
    100% {
        transform: scale(1);
        box-shadow: 0 0 0 0 rgba(95, 177, 7, 0);
    }
}

.level-up-animation {
    animation: levelUpPulse 0.5s ease;
}

/* Mobile adjustments */
@media (max-width: 768px) {
    .fg-honor-slot {
        min-height: 40px;
        max-height: 45px;
    }

    .fg-honor-slot-empty {
        width: 20px;
        height: 20px;
    }

    .fg-honor-slot-empty:before {
        width: 10px;
        height: 2px;
        top: 9px;
        left: 5px;
    }

    .fg-honor-slot-empty:after {
        width: 2px;
        height: 10px;
        top: 5px;
        left: 9px;
    }

    .fg-honor-slots-container {
        justify-content: center;
    }

    .fg-honor-selected-stat {
        width: 100%;
    }
}

/* Level number formatting */
.fg-honor-rank-header span.level {
    color: #8aff8e;
    margin-left: 5px;
}
