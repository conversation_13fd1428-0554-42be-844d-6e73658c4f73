/**
 * Costumes System
 * Handles costume progression for three types:
 * - Generic Costume
 * - Force Wing Costume
 * - Vehicle Costume
 */

// Add CSS styles for smaller slots
(function() {
    // Create style element
    const style = document.createElement('style');
    style.textContent = `
        .fg-costume-slot-small {
            min-height: 45px;
            max-height: 52px;
            aspect-ratio: 1/1;
            display: inline-flex;
            justify-content: center;
            align-items: center;
            margin: 5px;
            vertical-align: top;
            overflow: hidden;
        }

        .fg-costume-slot-small .fg-costume-slot-stat-icon {
            width: 100%;
            height: 100%;
            display: flex;
            justify-content: center;
            align-items: center;
            padding: 0;
        }

        .fg-costume-slot-small .fg-costume-slot-stat-icon img {
            width: 100%;
            height: 100%;
            object-fit: contain;
        }

        .fg-costume-slot-small .fg-costume-slot-empty {
            font-size: 24px;
            display: flex;
            justify-content: center;
            align-items: center;
            width: 100%;
            height: 100%;
            color: #aaa;
        }

        .fg-costume-slot-remove {
            position: absolute;
            top: 2px;
            left: 2px;
            width: 16px;
            height: 16px;
            background: rgba(187, 66, 66, 0.7);
            color: white;
            border: none;
            border-radius: 50%;
            font-size: 12px;
            line-height: 1;
            padding: 0;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: background-color 0.2s;
            z-index: 5;
        }

        .fg-costume-slot-stat-value-below {
            text-align: center;
            font-size: 12px;
            margin-top: 2px;
            height: 16px;
            color: #33ff33;
            display: block;
            width: 40px;
            position: relative;
        }

        .fg-costume-slots {
            display: flex;
            align-items: flex-start;
        }

        /* Wrapper for slot and its value */
        .fg-costume-slot-wrapper {
            display: flex;
            flex-direction: column;
            align-items: center;
            margin-right: 10px;
        }
    `;

    // Add to head
    document.head.appendChild(style);
})();

// Check if data is already loaded
(function() {
    // Check if CostumeData is already loaded
    if (typeof window.CostumeData !== 'undefined') {
        return;
    }

    // Load data file directly if not already loaded
    const script = document.createElement('script');

    // Determine the base URL based on the current script
    const scripts = document.getElementsByTagName('script');
    const currentScript = scripts[scripts.length - 1];
    const currentPath = currentScript.src;

    // Use WordPress plugin URL from global variable instead of attempting to calculate
    const basePath = window.forceguidesPlannerData ? forceguidesPlannerData.pluginUrl : '';

    // Set script source and append to document
    script.src = basePath + 'js/costume-system/costume-data.js';
    document.head.appendChild(script);
})();

// Define the system globally to ensure it's always available
window.CostumesSystem = {
    // Track system state
    isInitialized: false,

    // Cache DOM elements
    elements: {},

    // Costume types
    costumeTypes: ['generic', 'forceWing', 'vehicle'],

    // Track selected stats for each slot in each costume type
    selectedStats: {
        generic: Array(3).fill(null),
        forceWing: Array(3).fill(null),
        vehicle: Array(3).fill(null)
    },

    // Track selected epic craft option for each costume type
    epicCraftOptions: {
        generic: null,
        forceWing: null,
        vehicle: null
    },

    // Current selected slot for option popup
    currentSlot: {
        type: null,
        index: null
    },

    /**
     * Initialize the system - this is called by the BuildPlanner core
     * when this system becomes active
     */
    init: function() {
        // Prevent multiple initializations
        if (this.isInitialized) {
            return;
        }

        // Get the system panel
        const panel = document.getElementById('fg-costumes-system');
        if (!panel) {
            return;
        }

        // Cache DOM elements for better performance
        this.elements.panel = panel;

        // Check if data is available
        if (typeof CostumeData === 'undefined') {
            // Try again later if data not loaded yet
            setTimeout(() => this.init(), 100);
            return;
        }

        // Check if StatsConfig is available
        if (typeof StatsConfig === 'undefined') {
            // Try again later if StatsConfig not loaded yet
            setTimeout(() => this.init(), 100);
            return;
        }

        // Ensure StatIntegrationService styles are applied if available
        if (typeof StatIntegrationService !== 'undefined' && StatIntegrationService.addSummaryStyles) {
            StatIntegrationService.addSummaryStyles();
        }

        // Create popup overlay for stat selection (do this first)
        this.createStatSelectionPopup();

        // Initialize UI
        this.initUI();

        // Load data from the central store if available
        this.loadFromStore();

        // Setup event listeners
        this.setupEventListeners();

        // Mark as initialized
        this.isInitialized = true;

        // Update stats to reflect initial state
        this.updateCostumeStats();

        // Force BuildPlanner to refresh all system stats
        if (window.BuildPlanner && typeof BuildPlanner.refreshAllSystemStats === 'function') {
            setTimeout(() => BuildPlanner.refreshAllSystemStats(), 200);
        }
    },

    /**
     * Initialize the user interface
     */
    initUI: function() {
        // Replace the costume system placeholder with actual content
        this.createCostumeSystemUI();
    },

    // Create the costume system UI
    createCostumeSystemUI: function() {
        if (!this.elements.panel) {
            return;
        }

        // Create costumeRows HTML
        let costumeRowsHTML = '';

        // Create a row for each costume type
        this.costumeTypes.forEach(type => {
            const displayName = this.getCostumeDisplayName(type);
            costumeRowsHTML += `
                <div class="fg-costume-row" data-costume-type="${type}">
                    <div class="fg-costume-name">
                        <span class="fg-costume-name-prefix">[${displayName}]</span>
                    </div>
                    <div class="fg-costume-slots">
                        ${this.createSlotHTML(type, 3)}
                    </div>
                    <div class="fg-costume-epic-craft">
                        ${this.createEpicCraftDropdown(type)}
                    </div>
                </div>
            `;
        });

        // Create UI for costume system
        const costumeSystemHTML = `
            <div class="fg-costume-system-container">
                <!-- Costume rows -->
                <div class="fg-costume-rows-container">
                    ${costumeRowsHTML}
                </div>

                <!-- Selected stats summary -->
                <div class="fg-costume-selected-stats">
                    <h3>Selected Costume Stats</h3>
                    <div class="fg-costume-selected-list">
                        <!-- Selected stats will be shown here -->
                    </div>
                </div>
            </div>
        `;

        // Replace placeholder with our UI
        this.elements.panel.innerHTML = costumeSystemHTML;

        // Cache additional elements
        this.elements.selectedList = this.elements.panel.querySelector('.fg-costume-selected-list');

        // Set up slot click handlers immediately after creating the UI
        setTimeout(() => {
            this.setupSlotClickHandlers();
            this.setupEpicCraftDropdowns();
        }, 0);
    },

    // Get costume display name
    getCostumeDisplayName: function(type) {
        switch(type) {
            case 'generic': return 'Costume';
            case 'forceWing': return 'Wing Costume';
            case 'vehicle': return 'Vehicle Costume';
            default: return 'Unknown Costume';
        }
    },

    // Create HTML for costume slots
    createSlotHTML: function(costumeType, numSlots) {
        let html = '';

        for (let i = 0; i < numSlots; i++) {
            const hasSelection = this.selectedStats[costumeType][i] !== null;
            const slotClass = hasSelection ? 'selected' : 'empty';

            // Create a wrapper for the slot and its value
            html += `<div class="fg-costume-slot-wrapper">`;

            html += `
                <div class="fg-costume-slot ${slotClass} fg-costume-slot-small"
                     data-costume-type="${costumeType}"
                     data-slot-index="${i}">
                `;

            if (hasSelection) {
                const stat = this.selectedStats[costumeType][i];
                const statInfo = StatsConfig.getStatInfo(stat.id);
                const statName = statInfo ? statInfo.name : stat.id;
                html += `
                    <div class="fg-costume-slot-stat">
                        <div class="fg-costume-slot-stat-icon">
                            <img src="${StatsConfig.getStatIconUrl(stat.id)}" alt="${statName}">
                        </div>
                        <div class="fg-costume-slot-remove" data-costume-type="${costumeType}" data-slot-index="${i}">×</div>
                    </div>
                `;
            } else {
                html += `
                    <div class="fg-costume-slot-empty">
                        +
                    </div>
                `;
            }

            html += `</div>`;

            // Add stat value below slot (directly beneath the slot)
            if (hasSelection) {
                const stat = this.selectedStats[costumeType][i];
                const statInfo = StatsConfig.getStatInfo(stat.id);
                html += `
                    <div class="fg-costume-slot-stat-value-below">
                        +${stat.value}${statInfo && statInfo.isPercentage ? '%' : ''}
                    </div>
                `;
            } else {
                html += `<div class="fg-costume-slot-stat-value-below"></div>`;
            }

            // Close the wrapper
            html += `</div>`;
        }

        return html;
    },

    // Create epic craft dropdown
    createEpicCraftDropdown: function(costumeType) {
        return `
            <div class="fg-costume-epic-craft-button" data-costume-type="${costumeType}">
                <span class="fg-costume-epic-craft-label">Select Epic Craft</span>
            </div>
        `;
    },

    // Setup slot click handlers
    setupSlotClickHandlers: function() {
        // Get all slots
        const slots = this.elements.panel.querySelectorAll('.fg-costume-slot');

        // Add click handler to each slot
        slots.forEach(slot => {
            slot.addEventListener('click', (e) => {
                // Ignore if the click was on the remove button
                if (e.target.classList.contains('fg-costume-slot-remove')) {
                    return;
                }

                const costumeType = slot.getAttribute('data-costume-type');
                const slotIndex = parseInt(slot.getAttribute('data-slot-index'));

                // Show the stat selection popup
                this.showStatSelectionPopup(costumeType, slotIndex);
            });
        });

        // Add click handlers for remove buttons
        const removeButtons = this.elements.panel.querySelectorAll('.fg-costume-slot-remove');
        removeButtons.forEach(button => {
            button.addEventListener('click', (e) => {
                e.stopPropagation(); // Prevent slot click

                const costumeType = button.getAttribute('data-costume-type');
                const slotIndex = parseInt(button.getAttribute('data-slot-index'));

                // Remove the stat from this slot
                this.selectedStats[costumeType][slotIndex] = null;

                // Update UI
                this.updateSlotUI(costumeType, slotIndex);
                this.updateSelectedStatsDisplay();
                this.updateCostumeStats();

                // Save to store
                this.saveToStore();
            });
        });
    },

    // Set up epic craft dropdowns
    setupEpicCraftDropdowns: function() {
        // Get all epic craft buttons
        const epicCraftButtons = this.elements.panel.querySelectorAll('.fg-costume-epic-craft-button');

        // Set up each button
        epicCraftButtons.forEach(button => {
            const costumeType = button.getAttribute('data-costume-type');

            // Update button label if there's a selected epic craft
            this.updateEpicCraftButtonLabel(button, costumeType);

            // Add click handler
            button.addEventListener('click', () => {
                // Show the epic craft selection popup
                this.showEpicCraftSelectionPopup(costumeType);
            });
        });
    },

    // Update epic craft button label based on selection
    updateEpicCraftButtonLabel: function(button, costumeType) {
        const selectedEpicCraftId = this.epicCraftOptions[costumeType];
        const label = button.querySelector('.fg-costume-epic-craft-label');

        if (selectedEpicCraftId) {
            // Parse the epic craft ID to get the stat type and grade
            let parts, statKey, grade;

            if (costumeType === 'generic') {
                parts = selectedEpicCraftId.match(/generic_(\w+)_(\d+)/);
            } else if (costumeType === 'forceWing') {
                parts = selectedEpicCraftId.match(/fw_(\w+)_(\d+)/);
            } else if (costumeType === 'vehicle') {
                parts = selectedEpicCraftId.match(/v_(\w+)_(\d+)/);
            }

            if (parts && parts.length >= 3) {
                statKey = parts[1];
                grade = parseInt(parts[2]);

                // Get data from metaOptions
                const metaOption = CostumeData.metaOptions[costumeType][statKey];
                if (metaOption) {
                    const gradeIndex = metaOption.grades.indexOf(grade);
                    if (gradeIndex !== -1) {
                        const value = metaOption.values[gradeIndex];
                        label.textContent = `${metaOption.name} +${value}`;
                        label.classList.add('epic-craft-text');
                        return;
                    }
                }
            }

            // If we get here, the ID didn't match anything
            label.textContent = 'Select Epic Option';
            label.classList.remove('epic-craft-text');
        } else {
            label.textContent = 'Select Epic Option';
            label.classList.remove('epic-craft-text');
        }
    },

    // Show epic craft selection popup
    showEpicCraftSelectionPopup: function(costumeType) {
        // Store current costume type
        this.currentSlot = {
            type: costumeType,
            index: -1 // -1 indicates it's for epic craft, not a slot
        };

        // Populate options
        let optionsHTML = '<div class="fg-costume-stat-option" data-epic-id="">No Epic Option</div>';

        // Skip if the costume type doesn't exist in metaOptions
        if (CostumeData.metaOptions[costumeType]) {
            // Go through each stat type
            Object.keys(CostumeData.metaOptions[costumeType]).forEach(statKey => {
                const metaOption = CostumeData.metaOptions[costumeType][statKey];

                // Add section header for this stat type
                optionsHTML += `<div class="fg-costume-stat-group-header">${metaOption.name}</div>`;

                // Add all options for this type (one for each grade)
                for (let i = 0; i < metaOption.grades.length; i++) {
                    const grade = metaOption.grades[i];
                    const value = metaOption.values[i];

                    // Create option ID matching format needed by system
                    let optionId;
                    if (costumeType === 'generic') {
                        optionId = `generic_${statKey}_${grade}`;
                    } else if (costumeType === 'forceWing') {
                        optionId = `fw_${statKey}_${grade}`;
                    } else if (costumeType === 'vehicle') {
                        optionId = `v_${statKey}_${grade}`;
                    }

                    optionsHTML += `
                        <div class="fg-costume-stat-option" data-epic-id="${optionId}">
                            <div class="fg-costume-stat-option-left">
                                <div class="fg-costume-stat-option-icon">
                                    <img src="${StatsConfig.getStatIconUrl(metaOption.statId)}" alt="${metaOption.name}">
                                </div>
                                <div class="fg-costume-stat-option-name">${metaOption.name} +${value}</div>
                            </div>
                            <div class="fg-costume-stat-option-value grade-${grade}">
                                Grade ${grade}
                            </div>
                        </div>
                    `;
                }
            });
        }

        // Update popup content
        this.elements.popupOptions.innerHTML = optionsHTML;

        // Update popup title
        const popupTitle = this.elements.popup.querySelector('.fg-costume-stat-popup-header h3');
        if (popupTitle) {
            popupTitle.textContent = 'Select Epic Option';
        }

        // Add click handlers to options
        const options = this.elements.popupOptions.querySelectorAll('.fg-costume-stat-option');
        options.forEach(option => {
            option.addEventListener('click', () => {
                const epicId = option.getAttribute('data-epic-id');

                // Save the selected epic craft
                this.epicCraftOptions[costumeType] = epicId || null;

                // Update the button label
                const button = this.elements.panel.querySelector(`.fg-costume-epic-craft-button[data-costume-type="${costumeType}"]`);
                if (button) {
                    this.updateEpicCraftButtonLabel(button, costumeType);
                }

                // Update stats display
                this.updateSelectedStatsDisplay();
                this.updateCostumeStats();

                // Save to store
                this.saveToStore();

                // Hide popup
                this.hideStatSelectionPopup();
            });
        });

        // Show popup
        this.elements.popup.classList.add('active');
    },

    // Create stat selection popup
    createStatSelectionPopup: function() {
        const popup = document.createElement('div');
        popup.id = 'fg-costume-stat-popup';
        popup.className = 'fg-costume-stat-popup';

        popup.innerHTML = `
            <div class="fg-costume-stat-popup-content">
                <div class="fg-costume-stat-popup-header">
                    <h3>Select Costume Stat</h3>
                    <button class="fg-costume-stat-popup-close">&times;</button>
                </div>
                <div class="fg-costume-stat-popup-body">
                    <div class="fg-costume-stat-options">
                        <!-- Stat options will be populated dynamically -->
                    </div>
                </div>
            </div>
        `;

        // Add to DOM
        document.body.appendChild(popup);

        // Cache the popup elements
        this.elements.popup = popup;
        this.elements.popupClose = popup.querySelector('.fg-costume-stat-popup-close');
        this.elements.popupContent = popup.querySelector('.fg-costume-stat-popup-content');
        this.elements.popupOptions = popup.querySelector('.fg-costume-stat-options');

        // Add close handler
        this.elements.popupClose.addEventListener('click', () => {
            this.hideStatSelectionPopup();
        });

        // Close when clicking outside content
        popup.addEventListener('click', (e) => {
            if (e.target === popup) {
                this.hideStatSelectionPopup();
            }
        });

        // Add additional styles for the popup
        const style = document.createElement('style');
        style.textContent = `
            .fg-costume-stat-popup {
                display: none;
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0, 0, 0, 0.7);
                z-index: 1000;
                justify-content: center;
                align-items: center;
            }

            .fg-costume-stat-popup.active {
                display: flex;
            }

            .fg-costume-stat-popup-content {
                background: #1c1e22;
                border-radius: 5px;
                width: 90%;
                max-width: 500px;
                max-height: 80vh;
                overflow: hidden;
                box-shadow: 0 0 20px rgba(0, 0, 0, 0.5);
                border: 1px solid #3c3f44;
            }

            .fg-costume-stat-popup-header {
                display: flex;
                justify-content: space-between;
                align-items: center;
                padding: 10px 15px;
                border-bottom: 1px solid #3c3f44;
                background: #272a2e;
            }

            .fg-costume-stat-popup-header h3 {
                margin: 0;
                color: #e0e0e0;
                font-size: 1.1rem;
            }

            .fg-costume-stat-popup-close {
                background: none;
                border: none;
                color: #999;
                font-size: 24px;
                cursor: pointer;
                padding: 0;
                line-height: 1;
            }

            .fg-costume-stat-popup-close:hover {
                color: #fff;
            }

            .fg-costume-stat-popup-body {
                padding: 15px;
                max-height: calc(80vh - 50px);
                overflow-y: auto;
            }

            .fg-costume-stat-options {
                display: flex;
                flex-direction: column;
                gap: 8px;
            }

            .fg-costume-stat-option {
                display: flex;
                justify-content: space-between;
                align-items: center;
                padding: 8px 12px;
                background: rgba(30, 30, 40, 0.6);
                border-radius: 4px;
                cursor: pointer;
                transition: background 0.2s;
            }

            .fg-costume-stat-option:hover {
                background: rgba(50, 50, 70, 0.8);
            }

            .fg-costume-stat-option-left {
                display: flex;
                align-items: center;
            }

            .fg-costume-stat-option-icon {
                width: 24px;
                height: 24px;
                margin-right: 10px;
                border-radius: 3px;
                background-color: rgba(0, 0, 0, 0.2);
                padding: 2px;
                display: flex;
                align-items: center;
                justify-content: center;
            }

            .fg-costume-stat-option-icon img {
                max-width: 100%;
                max-height: 100%;
            }

            .fg-costume-stat-option-name {
                color: #d0d0d0;
                font-size: 0.9rem;
            }

            .fg-costume-stat-option-value {
                color: #66bb6a;
                font-weight: bold;
                font-size: 0.9rem;
            }

            .fg-costume-stat-option-value.grade-1 {
                color: #66bb6a; /* Green for Grade 1 */
            }

            .fg-costume-stat-option-value.grade-2 {
                color: #42a5f5; /* Blue for Grade 2 */
            }

            .fg-costume-stat-option-value.grade-3 {
                color: #ba68c8; /* Purple for Grade 3 */
            }

            .fg-costume-stat-option-value.grade-4 {
                color: #ffa726; /* Orange for Grade 4 */
            }

            .fg-costume-stat-group-header {
                padding: 5px 0;
                margin-top: 10px;
                color: #999;
                font-size: 0.85rem;
                border-bottom: 1px solid #3c3f44;
            }

            .fg-costume-epic-craft-button {
                background-color: rgba(30, 30, 40, 0.9);
                border: 1px solid #3c3f44;
                border-radius: 4px;
                padding: 8px 12px;
                cursor: pointer;
                transition: all 0.2s;
                min-width: 180px;
                max-width: 100%;
                text-align: center;
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
                box-sizing: border-box;
            }

            .fg-costume-epic-craft-button:hover {
                background-color: rgba(40, 40, 50, 0.9);
                border-color: #5e6d7f;
            }

            .fg-costume-epic-craft-label {
                color: #e0e0e0;
                font-size: 0.9rem;
                display: block;
                overflow: hidden;
                text-overflow: ellipsis;
            }

            .fg-costume-epic-craft-label.epic-craft-text {
                color: #a9e34b;
            }

            .fg-costume-epic-craft {
                min-width: 200px;
                flex-shrink: 0;
                padding-right: 10px;
                box-sizing: border-box;
                display: flex;
                align-items: center;
                overflow: hidden;
            }
        `;
        document.head.appendChild(style);
    },

    // Show stat selection popup
    showStatSelectionPopup: function(costumeType, slotIndex) {
        // Store current slot info
        this.currentSlot = {
            type: costumeType,
            index: slotIndex
        };

        // Get available stats for this costume type
        const availableStats = CostumeData.typeStats[costumeType] || [];

        // Populate options
        let optionsHTML = '';
        availableStats.forEach(stat => {
            const statInfo = StatsConfig.getStatInfo(stat.id);
            const statName = statInfo ? statInfo.name : stat.id;
            optionsHTML += `
                <div class="fg-costume-stat-option" data-stat-id="${stat.id}" data-stat-value="${stat.value}">
                    <div class="fg-costume-stat-option-left">
                        <div class="fg-costume-stat-option-icon">
                            <img src="${StatsConfig.getStatIconUrl(stat.id)}" alt="${statName}">
                        </div>
                        <div class="fg-costume-stat-option-name">${statName}</div>
                    </div>
                    <div class="fg-costume-stat-option-value">+${stat.value}${statInfo && statInfo.isPercentage ? '%' : ''}</div>
                </div>
            `;
        });

        // Update popup content
        this.elements.popupOptions.innerHTML = optionsHTML;

        // Add click handlers to options
        const options = this.elements.popupOptions.querySelectorAll('.fg-costume-stat-option');
        options.forEach(option => {
            option.addEventListener('click', () => {
                const statId = option.getAttribute('data-stat-id');
                const statValue = parseInt(option.getAttribute('data-stat-value'));

                // Save the selected stat
                this.selectStat(statId, statValue);

                // Hide popup
                this.hideStatSelectionPopup();
            });
        });

        // Show popup
        this.elements.popup.classList.add('active');
    },

    // Hide stat selection popup
    hideStatSelectionPopup: function() {
        this.elements.popup.classList.remove('active');
    },

    // Select a stat for the current slot
    selectStat: function(statId, statValue) {
        const { type, index } = this.currentSlot;

        // Create stat object
        const stat = {
            id: statId,
            value: statValue
        };

        // Save to selected stats
        this.selectedStats[type][index] = stat;

        // Update UI
        this.updateSlotUI(type, index);
        this.updateSelectedStatsDisplay();
        this.updateCostumeStats();

        // Save to store
        this.saveToStore();
    },

    // Update the UI for a specific slot
    updateSlotUI: function(costumeType, slotIndex) {
        // Find the slot element
        const slot = this.elements.panel.querySelector(`.fg-costume-slot[data-costume-type="${costumeType}"][data-slot-index="${slotIndex}"]`);
        if (!slot) {
            return;
        }

        // Find the wrapper or parent element
        const wrapper = slot.closest('.fg-costume-slot-wrapper');
        if (!wrapper) {
            return;
        }

        // Find the value element within the wrapper
        let valueElement = wrapper.querySelector('.fg-costume-slot-stat-value-below');
        if (!valueElement) {
            valueElement = document.createElement('div');
            valueElement.className = 'fg-costume-slot-stat-value-below';
            wrapper.appendChild(valueElement);
        }

        // Get the stat for this slot
        const stat = this.selectedStats[costumeType][slotIndex];

        // Update class
        if (stat) {
            slot.classList.add('selected');
            slot.classList.remove('empty');
        } else {
            slot.classList.remove('selected');
            slot.classList.add('empty');
        }

        // Update content
        if (stat) {
            const statInfo = StatsConfig.getStatInfo(stat.id);
            const statName = statInfo ? statInfo.name : stat.id;
            slot.innerHTML = `
                <div class="fg-costume-slot-stat">
                    <div class="fg-costume-slot-stat-icon">
                        <img src="${StatsConfig.getStatIconUrl(stat.id)}" alt="${statName}">
                    </div>
                    <div class="fg-costume-slot-remove" data-costume-type="${costumeType}" data-slot-index="${slotIndex}">×</div>
                </div>
            `;

            // Update value below
            valueElement.innerHTML = `+${stat.value}${statInfo && statInfo.isPercentage ? '%' : ''}`;

            // Add click handler to remove button
            const removeButton = slot.querySelector('.fg-costume-slot-remove');
            if (removeButton) {
                removeButton.addEventListener('click', (e) => {
                    e.stopPropagation(); // Prevent slot click

                    // Remove the stat from this slot
                    this.selectedStats[costumeType][slotIndex] = null;

                    // Update UI
                    this.updateSlotUI(costumeType, slotIndex);
                    this.updateSelectedStatsDisplay();
                    this.updateCostumeStats();

                    // Save to store
                    this.saveToStore();
                });
            }
        } else {
            slot.innerHTML = `
                <div class="fg-costume-slot-empty">
                    +
                </div>
            `;

            // Clear value below
            valueElement.innerHTML = '';
        }
    },

    // Display all selected costume stats
    updateSelectedStatsDisplay: function() {
        // Get all stats from all costume pieces
        const allStats = {};

        // Process all costume types and slots
        this.costumeTypes.forEach(type => {
            for (let i = 0; i < this.selectedStats[type].length; i++) {
                const selectedStat = this.selectedStats[type][i];
                if (selectedStat) {
                    const { id, value } = selectedStat;
                    if (!allStats[id]) {
                        allStats[id] = 0;
                    }
                    allStats[id] += value;
                }
            }
        });

        // Add stats from epic craft options
        this.costumeTypes.forEach(type => {
            const epicOptionId = this.epicCraftOptions[type];
            if (!epicOptionId) return;

            // Parse the epic craft ID to get the stat type and grade
            let parts, statKey, grade;

            if (type === 'generic') {
                parts = epicOptionId.match(/generic_(\w+)_(\d+)/);
            } else if (type === 'forceWing') {
                parts = epicOptionId.match(/fw_(\w+)_(\d+)/);
            } else if (type === 'vehicle') {
                parts = epicOptionId.match(/v_(\w+)_(\d+)/);
            }

            if (parts && parts.length >= 3) {
                statKey = parts[1];
                grade = parseInt(parts[2]);

                // Get data from metaOptions
                const metaOption = CostumeData.metaOptions[type][statKey];
                if (metaOption) {
                    const gradeIndex = metaOption.grades.indexOf(grade);
                    if (gradeIndex !== -1) {
                        const value = metaOption.values[gradeIndex];
                        const statId = metaOption.statId;

                        if (!allStats[statId]) {
                            allStats[statId] = 0;
                        }
                        allStats[statId] += value;
                    }
                }
            }
        });

        // Use StatIntegrationService for consistent stat display if available
        if (typeof StatIntegrationService !== 'undefined') {
            this.elements.selectedList.innerHTML = StatIntegrationService.createStatSummaryHTML(allStats);
        } else {
            // Fallback to basic display if service isn't available
            let statsHTML = '';

            for (const statId in allStats) {
                const statInfo = StatsConfig.getStatInfo(statId);
                const statName = statInfo ? statInfo.name : statId;
                const isPercentage = statInfo ? statInfo.isPercentage : false;
                statsHTML += `
                    <div class="fg-costume-stat-summary-item">
                        <div class="fg-costume-stat-summary-name">${statName}</div>
                        <div class="fg-costume-stat-summary-value">+${allStats[statId]}${isPercentage ? '%' : ''}</div>
                    </div>
                `;
            }

            // Update the display
            this.elements.selectedList.innerHTML = statsHTML || '<div class="fg-costume-no-stats">No stats selected</div>';
        }
    },

    // Update costume stats - this would connect to the BuildPlanner's stat system
    updateCostumeStats: function() {
        // Calculate total stats from all selected costumes
        const totalStats = {};

        // Process all costume types and slots
        this.costumeTypes.forEach(type => {
            for (let i = 0; i < this.selectedStats[type].length; i++) {
                const selectedStat = this.selectedStats[type][i];
                if (selectedStat) {
                    const { id, value } = selectedStat;
                    if (!totalStats[id]) {
                        totalStats[id] = 0;
                    }
                    totalStats[id] += value;
                }
            }
        });

        // Process epic craft options
        this.costumeTypes.forEach(type => {
            const epicOptionId = this.epicCraftOptions[type];
            if (!epicOptionId) return;

            // Parse the epic craft ID to get the stat type and grade
            let parts, statKey, grade;

            if (type === 'generic') {
                parts = epicOptionId.match(/generic_(\w+)_(\d+)/);
            } else if (type === 'forceWing') {
                parts = epicOptionId.match(/fw_(\w+)_(\d+)/);
            } else if (type === 'vehicle') {
                parts = epicOptionId.match(/v_(\w+)_(\d+)/);
            }

            if (parts && parts.length >= 3) {
                statKey = parts[1];
                grade = parseInt(parts[2]);

                // Get data from metaOptions
                const metaOption = CostumeData.metaOptions[type][statKey];
                if (metaOption) {
                    const gradeIndex = metaOption.grades.indexOf(grade);
                    if (gradeIndex !== -1) {
                        const value = metaOption.values[gradeIndex];
                        const statId = metaOption.statId;

                        if (!totalStats[statId]) {
                            totalStats[statId] = 0;
                        }
                        totalStats[statId] += value;
                    }
                }
            }
        });

        // Send stats to BuildPlanner (using same method as all other systems)
        if (typeof BuildPlanner !== 'undefined' && BuildPlanner.updateStats) {
            BuildPlanner.updateStats('costumes', totalStats);
        }
    },

    /**
     * Setup all event listeners
     */
    setupEventListeners: function() {
        // Already handled in the specific UI setup methods
    },

    /**
     * Get the essential data for saving
     * Returns just the minimum data needed to restore the system state
     */
    getEssentialData: function() {
        return {
            selectedStats: JSON.parse(JSON.stringify(this.selectedStats)),
            epicCraftOptions: JSON.parse(JSON.stringify(this.epicCraftOptions))
        };
    },

    /**
     * Ensure the proper data structure exists
     */
    ensureDataStructure: function() {
        // Initialize selectedStats if it doesn't exist
        if (!this.selectedStats) {
            this.selectedStats = {
                generic: Array(3).fill(null),
                forceWing: Array(3).fill(null),
                vehicle: Array(3).fill(null)
            };
        }

        // Ensure all costume types exist in selectedStats
        const costumeTypes = ['generic', 'forceWing', 'vehicle'];
        costumeTypes.forEach(type => {
            if (!this.selectedStats[type]) {
                this.selectedStats[type] = Array(3).fill(null);
            }

            // Ensure array has correct length
            if (this.selectedStats[type].length < 3) {
                const missing = Array(3 - this.selectedStats[type].length).fill(null);
                this.selectedStats[type] = this.selectedStats[type].concat(missing);
            } else if (this.selectedStats[type].length > 3) {
                this.selectedStats[type] = this.selectedStats[type].slice(0, 3);
            }
        });

        // Initialize epicCraftOptions if it doesn't exist
        if (!this.epicCraftOptions) {
            this.epicCraftOptions = {
                generic: null,
                forceWing: null,
                vehicle: null
            };
        }

        // Ensure all costume types exist in epicCraftOptions
        costumeTypes.forEach(type => {
            if (this.epicCraftOptions[type] === undefined) {
                this.epicCraftOptions[type] = null;
            }
        });
    },

    /**
     * Force a save of the current data to the store
     * Used for auto-saving after changes
     */
    saveToStore: function() {
        // Ensure data structure is valid
        this.ensureDataStructure();

        // Use the central BuildSaverStore to save data
        if (typeof BuildSaverStore !== 'undefined' && BuildSaverStore.saveData) {
            const essentialData = this.getEssentialData();
            BuildSaverStore.saveData('costumes', essentialData);
            return true;
        }

        return false;
    },

    /**
     * Ensure and save the current data
     * This method is a more explicit version of saveToStore that's used
     * when data needs to be saved immediately
     */
    ensureAndSave: function() {
        // Ensure data structure is valid
        this.ensureDataStructure();

        // Save data to the central store
        if (typeof BuildSaverStore !== 'undefined' && BuildSaverStore.saveData) {
            const essentialData = this.getEssentialData();
            BuildSaverStore.saveData('costumes', essentialData);
            return true;
        }

        return false;
    },

    /**
     * Load data from the central BuildSaverStore
     */
    loadFromStore: function() {
        // Check if BuildSaverStore exists and has data
        if (window.BuildSaverStore && BuildSaverStore.dataLoaded) {
            // Get data for this system
            const systemData = BuildSaverStore.getSystemData('costumes');
            if (systemData) {
                // Use the common loadFromData method
                return this.loadFromData(systemData);
            }
        }

        return false;
    },

    /**
     * Load data directly from provided data object
     * Used by both loadFromStore and the build sharing feature
     */
    loadFromData: function(data) {
        if (data) {
            // Load selected stats if available
            if (data.selectedStats) {
                this.selectedStats = JSON.parse(JSON.stringify(data.selectedStats));
            }

            // Load epic craft options if available
            if (data.epicCraftOptions) {
                this.epicCraftOptions = JSON.parse(JSON.stringify(data.epicCraftOptions));
            }

            // Ensure data structure validity
            this.ensureDataStructure();

            // Update UI to reflect the loaded data
            this.costumeTypes.forEach(type => {
                // Update slot UIs
                for (let i = 0; i < this.selectedStats[type].length; i++) {
                    this.updateSlotUI(type, i);
                }

                // Update dropdown selections
                const dropdown = document.querySelector(`.fg-costume-epic-craft-button[data-costume-type="${type}"]`);
                if (dropdown) {
                    this.updateEpicCraftButtonLabel(dropdown, type);
                }
            });

            // Update displays
            this.updateSelectedStatsDisplay();
            this.updateCostumeStats();

            return true;
        }

        return false;
    }
};

// Safety check to ensure the system is available when needed
document.addEventListener('DOMContentLoaded', function() {
    // Don't auto-initialize - let the BuildPlanner handle initialization
    // when the system tab is clicked instead
});

// Register the system under the name expected by BuildSaver
window.CostumeSystem = window.CostumesSystem;