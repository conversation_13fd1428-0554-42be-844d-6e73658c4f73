/**
 * Stellar Link System CSS
 * Styling for the stellar constellation skill tree
 */

/* Main container */
.fg-stellar-system-container {
    display: flex;
    flex-direction: column;
    gap: 20px;
    width: 100%;
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}



/* Skill tree container */
.fg-stellar-skill-tree {
    position: relative;
    width: 100%;
    /* Adjust aspect ratio to match the image if needed */
    aspect-ratio: 1/1;
    max-width: 800px;
    margin: 0 auto;
    background-color: #000;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.5);
    overflow: hidden;
}

/* Background image */
.fg-stellar-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: url('../assets/images/stellar-system/skill_tree_base.png');
    background-size: 100% 100%;
    background-position: center;
    background-repeat: no-repeat;
    z-index: 1;
}

/* Nodes container */
.fg-stellar-nodes-container {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 2;
}

/* Individual node - 15% larger from current size */
.fg-stellar-node {
    position: absolute;
    width: 61px; /* Increased from 53px by 15% */
    height: 61px; /* Increased from 53px by 15% */
    background-image: url('../assets/images/stellar-system/node_base.png');
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center;
    transform: translate(-50%, -50%);
    cursor: pointer;
    z-index: 3;
    filter: none !important;
    box-shadow: none !important;
    border: none !important;
    transition: none !important;
}

/* Add a pseudo-element for the colored background glow - 15% larger */
.fg-stellar-node.active[data-color]::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 52px; /* Increased from 45px by 15% */
    height: 52px; /* Increased from 45px by 15% */
    border-radius: 50%;
    z-index: -1;
    opacity: 0.9;
}

.fg-stellar-node.active[data-color="desire"]::before {
    background: radial-gradient(circle, rgba(255, 215, 0, 0.9) 30%, rgba(255, 215, 0, 0.7) 70%, transparent 100%);
    box-shadow: 0 0 10px 2px rgba(255, 215, 0, 0.5);
}

.fg-stellar-node.active[data-color="grief"]::before {
    background: radial-gradient(circle, rgba(255, 153, 51, 0.9) 30%, rgba(255, 153, 51, 0.7) 70%, transparent 100%);
    box-shadow: 0 0 10px 2px rgba(255, 153, 51, 0.5);
}

.fg-stellar-node.active[data-color="fury"]::before {
    background: radial-gradient(circle, rgba(255, 69, 0, 0.9) 30%, rgba(255, 69, 0, 0.7) 70%, transparent 100%);
    box-shadow: 0 0 10px 2px rgba(255, 69, 0, 0.5);
}

.fg-stellar-node.active[data-color="oblivion"]::before {
    background: radial-gradient(circle, rgba(68, 119, 255, 0.9) 30%, rgba(68, 119, 255, 0.7) 70%, transparent 100%);
    box-shadow: 0 0 10px 2px rgba(68, 119, 255, 0.5);
}

.fg-stellar-node.active[data-color="emptiness"]::before {
    background: radial-gradient(circle, rgba(170, 68, 238, 0.9) 30%, rgba(170, 68, 238, 0.7) 70%, transparent 100%);
    box-shadow: 0 0 10px 2px rgba(170, 68, 238, 0.5);
}

/* Remove direct node styling to avoid conflict with the background glow */
.fg-stellar-node.active[data-color="desire"],
.fg-stellar-node.active[data-color="grief"],
.fg-stellar-node.active[data-color="fury"],
.fg-stellar-node.active[data-color="oblivion"],
.fg-stellar-node.active[data-color="emptiness"] {
    filter: none !important;
    box-shadow: none !important;
    border: none !important;
}

/* Effects for icons based on node color - more compact glow */
.fg-stellar-node-icon[data-color="desire"] {
    box-shadow: 0 0 6px rgba(255, 215, 0, 0.8);
    border-color: rgba(255, 215, 0, 0.8);
}

.fg-stellar-node-icon[data-color="grief"] {
    box-shadow: 0 0 6px rgba(255, 153, 51, 0.8);
    border-color: rgba(255, 153, 51, 0.8);
}

.fg-stellar-node-icon[data-color="fury"] {
    box-shadow: 0 0 6px rgba(255, 69, 0, 0.8);
    border-color: rgba(255, 69, 0, 0.8);
}

.fg-stellar-node-icon[data-color="oblivion"] {
    box-shadow: 0 0 6px rgba(68, 119, 255, 0.8);
    border-color: rgba(68, 119, 255, 0.8);
}

.fg-stellar-node-icon[data-color="emptiness"] {
    box-shadow: 0 0 6px rgba(170, 68, 238, 0.8);
    border-color: rgba(170, 68, 238, 0.8);
}

/* Style for the node image */
.fg-stellar-node-icon img {
    width: 80%;
    height: 80%;
    object-fit: cover;
    object-position: center;
    filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.5));
    border-radius: 50%;
    z-index: 6;
}

/* Level indicator styling */
.fg-stellar-level-indicator {
    position: absolute;
    bottom: -6px;
    right: -6px;
    background-color: rgba(0, 0, 0, 0.7);
    color: #fff;
    font-size: 10px;
    font-weight: bold;
    width: 16px;
    height: 16px;
    border-radius: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 7;
    box-shadow: 0 0 3px rgba(0, 0, 0, 0.8);
    text-shadow: 0 1px 1px rgba(0, 0, 0, 0.8);
}

/* Color-specific level indicators */
.fg-stellar-node-icon[data-color="desire"] .fg-stellar-level-indicator {
    background-color: rgba(255, 215, 0, 0.8);
    color: #000;
}

.fg-stellar-node-icon[data-color="grief"] .fg-stellar-level-indicator {
    background-color: rgba(255, 153, 51, 0.8);
    color: #000;
}

.fg-stellar-node-icon[data-color="fury"] .fg-stellar-level-indicator {
    background-color: rgba(255, 69, 0, 0.8);
    color: #fff;
}

.fg-stellar-node-icon[data-color="oblivion"] .fg-stellar-level-indicator {
    background-color: rgba(68, 119, 255, 0.8);
    color: #fff;
}

.fg-stellar-node-icon[data-color="emptiness"] .fg-stellar-level-indicator {
    background-color: rgba(170, 68, 238, 0.8);
    color: #fff;
}

/* Node icon styling - 15% larger from current size */
.fg-stellar-node-icon {
    position: absolute;
    transform: translate(-50%, -50%);
    width: 43px; /* Increased from 37px by 15% */
    height: 43px; /* Increased from 37px by 15% */
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 5;
    border-radius: 50%;
    overflow: visible; /* Changed to visible to allow level indicator to overflow */
    pointer-events: none;
    background-color: rgba(20, 20, 30, 0.7);
    box-shadow: 0 0 5px rgba(0, 0, 0, 0.8), inset 0 0 3px rgba(255, 255, 255, 0.5);
    transition: box-shadow 0.3s ease;
    border: 2px solid rgba(255, 255, 255, 0.3);
}

/* Node hover state */
.fg-stellar-node:hover {
    filter: none !important;
    transform: translate(-50%, -50%);
    box-shadow: none !important;
    border: none !important;
}

/* Selected node */
.fg-stellar-node.selected {
    filter: none !important;
    box-shadow: none !important;
    transform: translate(-50%, -50%);
    border: none !important;
}

/* Active node (has stat) */
.fg-stellar-node.active {
    filter: none !important;
    box-shadow: none !important;
    border: none !important;
}

/* Active and selected node */
.fg-stellar-node.active.selected {
    filter: none !important;
    box-shadow: none !important;
    border: none !important;
}

/* Override all color-specific styles */
.fg-stellar-node.active[data-color="desire"],
.fg-stellar-node.active[data-color="grief"],
.fg-stellar-node.active[data-color="fury"],
.fg-stellar-node.active[data-color="oblivion"],
.fg-stellar-node.active[data-color="emptiness"] {
    filter: none !important;
    box-shadow: none !important;
    border: none !important;
}

/* Active and selected node with color */
.fg-stellar-node.active.selected[data-color] {
    filter: none !important;
    transform: translate(-50%, -50%);
    box-shadow: none !important;
    border: none !important;
}

/* Override constellation line colors */
.fg-stellar-node[data-line="daedalus"],
.fg-stellar-node[data-line="icarus"],
.fg-stellar-node[data-line="vulcanos"],
.fg-stellar-node[data-line="minerva"],
.fg-stellar-node[data-line="pluto"] {
    filter: none !important;
    box-shadow: none !important;
    border: none !important;
}

/* Node details section */
.fg-stellar-details-section {
    background-color: #1a1a24;
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

/* Stat selection */
.fg-stellar-stat-selection {
    position: absolute;
    z-index: 10;
    background-color: rgba(25, 25, 35, 0.95);
    background-image: linear-gradient(to bottom, rgba(40, 40, 60, 0.9), rgba(25, 25, 35, 0.95));
    backdrop-filter: blur(5px);
    border-radius: 8px;
    padding: 12px;
    width: 240px;
    max-height: 350px;
    overflow-y: auto;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.5), inset 0 1px 0 rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(100, 181, 246, 0.2);
}

.fg-stellar-stat-selection::before {
    content: '';
    position: absolute;
    width: 10px;
    height: 10px;
    background-color: rgba(34, 34, 48, 0.9);
    transform: rotate(45deg);
    top: -5px;
    left: 50%;
    margin-left: -5px;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    border-left: 1px solid rgba(255, 255, 255, 0.1);
}

.fg-stellar-stat-selection h3 {
    font-size: 0.95rem;
    margin: 0 0 10px 0;
    color: #ffffff;
    text-align: center;
    border-bottom: 1px solid rgba(100, 181, 246, 0.3);
    padding-bottom: 8px;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
    font-weight: 600;
}

/* Stat option */
.fg-stellar-stat-option {
    display: flex;
    justify-content: space-between;
    padding: 6px 10px;
    margin: 4px 0;
    background-color: rgba(42, 42, 56, 0.7);
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.fg-stellar-stat-option:hover {
    background-color: rgba(51, 51, 69, 0.9);
    transform: translateX(3px);
}

.fg-stellar-stat-option.selected {
    background-color: rgba(58, 80, 107, 0.9);
    border-left: 3px solid #64b5f6;
}

.fg-stellar-stat-name {
    color: #d0d0d0;
    font-size: 0.85rem;
    display: flex;
    align-items: center;
}

.fg-stellar-stat-icon {
    width: 20px;
    height: 20px;
    margin-right: 8px;
    object-fit: contain;
    border-radius: 4px;
    background-color: rgba(0, 0, 0, 0.3);
    padding: 2px;
    box-shadow: 0 0 3px rgba(255, 255, 255, 0.3);
    border: 1px solid rgba(100, 181, 246, 0.3);
}

.fg-stellar-stat-value {
    color: #64b5f6;
    font-weight: 500;
    font-size: 0.85rem;
}

/* Stat header */
.fg-stellar-stat-header {
    display: flex;
    align-items: center;
    padding: 8px 10px;
    margin: 6px 0 0 0;
    background-color: rgba(45, 45, 65, 0.9);
    border-radius: 6px 6px 0 0;
    font-weight: bold;
    color: #ffffff;
    border-bottom: 1px solid rgba(100, 181, 246, 0.5);
    box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

/* Stat tier option */
.fg-stellar-stat-tier {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 6px 10px;
    margin: 0;
    background-color: rgba(42, 42, 56, 0.7);
    cursor: pointer;
    transition: all 0.15s ease;
    border-left: 2px solid transparent;
}

.fg-stellar-stat-tier:last-of-type {
    border-radius: 0 0 6px 6px;
    margin-bottom: 4px;
}

.fg-stellar-stat-tier:hover {
    background-color: rgba(51, 51, 69, 0.9);
    border-left: 2px solid rgba(100, 181, 246, 0.5);
}

.fg-stellar-stat-tier.selected {
    background-color: rgba(58, 80, 107, 0.9);
    border-left: 3px solid #64b5f6;
    box-shadow: inset 0 0 5px rgba(100, 181, 246, 0.3);
}

.fg-stellar-stat-tier-left {
    display: flex;
    align-items: center;
    gap: 6px;
}

.fg-stellar-tier-level {
    display: inline-block;
    width: 20px;
    height: 20px;
    line-height: 20px;
    text-align: center;
    background-color: rgba(100, 181, 246, 0.2);
    border-radius: 50%;
    color: #64b5f6;
    font-size: 0.75rem;
    font-weight: bold;
}

.fg-stellar-stat-tier-name {
    color: #d0d0d0;
    font-size: 0.85rem;
}

.fg-stellar-stat-tier-value {
    color: #64b5f6;
    font-weight: 500;
    font-size: 0.85rem;
}

/* Separator between stat groups */
.fg-stellar-stat-separator {
    height: 8px;
    margin: 0 5px 8px 5px;
    border-bottom: 1px solid rgba(100, 181, 246, 0.15);
}

/* Clear stat option - updated style */
.fg-stellar-clear-stat {
    display: flex;
    justify-content: center;
    padding: 8px 10px;
    margin: 10px 0 5px 0;
    background-color: rgba(58, 42, 42, 0.7);
    border-radius: 6px;
    cursor: pointer;
    border-left: 3px solid #e57373;
    transition: all 0.2s ease;
    box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

.fg-stellar-clear-stat:hover {
    background-color: rgba(74, 53, 53, 0.9);
    box-shadow: 0 0 5px rgba(229, 115, 115, 0.3);
}

.fg-stellar-clear-stat .fg-stellar-stat-name {
    color: #e57373;
    font-weight: bold;
    display: flex;
    align-items: center;
}

.fg-stellar-clear-stat .fg-stellar-stat-name::before {
    content: '×';
    font-size: 1.2rem;
    margin-right: 5px;
    font-weight: bold;
}

/* Color selection - simplified */
.fg-stellar-color-selection {
    position: absolute;
    z-index: 10;
    background-color: rgba(34, 34, 48, 0.9);
    backdrop-filter: blur(5px);
    border-radius: 6px;
    padding: 12px;
    width: 200px; /* Reduced width for simpler UI */
    max-height: 300px;
    overflow-y: auto;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.5);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.fg-stellar-color-selection::before {
    content: '';
    position: absolute;
    width: 10px;
    height: 10px;
    background-color: rgba(34, 34, 48, 0.9);
    transform: rotate(45deg);
    top: -5px;
    left: 50%;
    margin-left: -5px;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    border-left: 1px solid rgba(255, 255, 255, 0.1);
}

.fg-stellar-color-selection h3 {
    font-size: 0.9rem;
    margin: 0 0 8px 0;
    color: #e0e0e0;
    text-align: center;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    padding-bottom: 5px;
}

/* Remove unneeded color selection description */
.fg-stellar-color-description {
    display: none;
}

/* Color option */
.fg-stellar-color-option {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 6px 10px;
    margin: 4px 0;
    background-color: rgba(42, 42, 56, 0.7);
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.fg-stellar-color-option:hover {
    background-color: rgba(51, 51, 69, 0.9);
    transform: translateX(3px);
}

.fg-stellar-color-option.selected {
    background-color: rgba(58, 80, 107, 0.9);
}

.fg-stellar-color-name {
    color: #d0d0d0;
    font-size: 0.85rem;
}

.fg-stellar-color-preview {
    width: 18px;
    height: 18px;
    border-radius: 50%;
    box-shadow: 0 0 5px rgba(0, 0, 0, 0.3);
}

/* Remove unneeded line bonus section */
.fg-stellar-line-bonus-section {
    display: none;
}

/* Line bonus section styling */
.fg-stellar-color-bonus-section {
    width: 100%;
    max-width: 800px;
    margin: 0 auto;
    background-color: rgba(30, 30, 40, 0.6);
    border-radius: 8px;
    padding: 15px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

/* Section header styles */
.fg-stellar-color-bonus-section h3 {
    margin-top: 0;
    margin-bottom: 15px;
    color: #fff;
    font-size: 20px;
    text-align: center;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
    border-bottom: 1px solid rgba(170, 68, 238, 0.5);
    padding-bottom: 10px;
}

/* Content container styles */
.fg-stellar-color-bonus-content {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

/* Line bonus styles */
.fg-stellar-line-bonus {
    background-color: rgba(20, 20, 30, 0.5);
    padding: 8px 12px;
    border-radius: 6px;
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    gap: 5px;
    border-left: 3px solid rgba(170, 68, 238, 0.6);
}

/* New styles for the summary sections */
.fg-stellar-constellation-bonuses,
.fg-stellar-stats-summary {
    background-color: rgba(40, 40, 50, 0.5);
    border-radius: 6px;
    padding: 10px;
    margin-bottom: 10px;
}

.fg-stellar-constellation-bonuses h4,
.fg-stellar-stats-summary h4 {
    margin-top: 0;
    margin-bottom: 10px;
    font-size: 16px;
    color: #ddd;
    border-bottom: 1px solid rgba(170, 68, 238, 0.3);
    padding-bottom: 5px;
}

/* Simplified stat display */
.fg-stellar-simple-stats {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
}

.fg-stellar-stat-item {
    background-color: rgba(20, 20, 30, 0.7);
    border-radius: 4px;
    padding: 4px 8px;
    display: flex;
    align-items: center;
    gap: 5px;
}

.fg-stellar-stat-name {
    color: #f0f0f0;
    font-size: 12px;
}

.fg-stellar-stat-value {
    color: #afa;
    font-weight: bold;
    font-size: 12px;
}

/* Line name and effect styles for compact display */
.fg-stellar-line-name {
    font-weight: bold;
    color: #f0f0f0;
    margin-right: 5px;
}

.fg-stellar-line-color {
    font-style: italic;
    margin-right: 5px;
}

.fg-stellar-line-effect {
    background-color: rgba(20, 20, 30, 0.7);
    padding: 2px 6px;
    border-radius: 4px;
    font-size: 12px;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .fg-stellar-skill-tree {
        max-width: 100%;
    }

    .fg-stellar-node {
        width: 36px;
        height: 36px;
    }

    .fg-stellar-simple-stats {
        flex-direction: column;
        gap: 5px;
    }

    .fg-stellar-line-bonus {
        flex-direction: column;
        align-items: flex-start;
    }
}

@media (max-width: 480px) {
    .fg-stellar-system-container {
        padding: 10px;
    }

    .fg-stellar-node {
        width: 30px;
        height: 30px;
    }
}