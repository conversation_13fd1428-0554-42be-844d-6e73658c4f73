/**
 * System Header Manager
 * 
 * Centralized management for all system headers, descriptions, and links.
 * Provides uniform header structure across all systems in the build planner.
 */

class SystemHeaderManager {
    constructor() {
        this.currentSystem = null;
        this.headerElement = null;
        this.isInitialized = false;
    }

    /**
     * Initialize the header manager
     */
    init() {
        if (this.isInitialized) return;
        
        this.createHeaderContainer();
        this.isInitialized = true;
    }

    /**
     * Create the header container element
     */
    createHeaderContainer() {
        // Find the main content area where systems are displayed
        const systemDisplay = document.querySelector('.fg-system-display');
        if (!systemDisplay) {
            console.warn('SystemHeaderManager: Could not find .fg-system-display container');
            return;
        }

        // Create header container
        this.headerElement = document.createElement('div');
        this.headerElement.className = 'fg-system-header-container';
        this.headerElement.style.display = 'none'; // Hidden by default

        // Insert at the beginning of system display
        systemDisplay.insertBefore(this.headerElement, systemDisplay.firstChild);
    }

    /**
     * Show header for a specific system
     */
    showHeader(systemId) {
        if (!this.headerElement) {
            this.createHeaderContainer();
        }

        const headerConfig = SystemHeadersConfig.getConfig(systemId);
        if (!headerConfig) {
            this.hideHeader();
            return;
        }

        this.currentSystem = systemId;
        this.renderHeader(headerConfig);
        this.headerElement.style.display = 'block';
    }

    /**
     * Hide the header
     */
    hideHeader() {
        if (this.headerElement) {
            this.headerElement.style.display = 'none';
        }
        this.currentSystem = null;
    }

    /**
     * Render header content
     */
    renderHeader(config) {
        if (!this.headerElement) return;

        // Build links HTML
        let linksHTML = '';
        if (config.links && config.links.length > 0) {
            const linkItems = config.links.map(link => 
                `<a href="${link.url}" target="_blank" class="fg-header-link">${link.text}</a>`
            ).join('');
            linksHTML = `<div class="fg-header-links">${linkItems}</div>`;
        }

        // Build description HTML
        const descriptionHTML = config.description 
            ? `<p class="fg-header-description">${config.description}</p>` 
            : '';

        // Render complete header
        this.headerElement.innerHTML = `
            <div class="fg-header-content">
                <h2 class="fg-header-title">${config.title}</h2>
                ${descriptionHTML}
                ${linksHTML}
            </div>
        `;
    }

    /**
     * Update header for current system (useful for dynamic content)
     */
    updateCurrentHeader() {
        if (this.currentSystem) {
            this.showHeader(this.currentSystem);
        }
    }
}

/**
 * System Headers Configuration
 * All system header data in one place
 */
window.SystemHeadersConfig = {
    /**
     * Header configurations for each system
     */
    headers: {
        'class': {
            title: 'Character Stats',
            description: 'Allocate attribute points to customize your character\'s base stats.',
            links: [
                { text: 'Stats Guide', url: '#' },
                { text: 'Character Builds', url: '#' }
            ]
        },

        'pet': {
            title: 'Pet System',
            description: 'Choose and customize your pets to gain additional stats and abilities. Higher tiers provide more powerful bonuses.',
            links: [
                { text: 'Pet Guide', url: '#' },
                { text: 'Pet Tier Guide', url: '#' }
            ]
        },

        'stellar': {
            title: 'Stellar Link System',
            description: 'Activate constellation nodes to unlock powerful stat bonuses. Connect nodes to create powerful combinations.',
            links: [
                { text: 'Stellar Guide', url: '#' },
                { text: 'Node Combinations', url: '#' }
            ]
        },

        'honor': {
            title: 'Honor Medal System',
            description: 'Equip honor medals to boost your character\'s combat effectiveness. Higher ranks provide better stat bonuses.',
            links: [
                { text: 'Honor Guide', url: '#' },
                { text: 'Medal Rankings', url: '#' }
            ]
        },

        'equipment': {
            title: 'Equipment System',
            description: 'Upgrade your weapons and armor through multiple enhancement paths for maximum power.',
            links: [
                { text: 'Equipment Guide', url: '#' },
                { text: 'Upgrade Paths', url: '#' }
            ]
        },

        'costumes': {
            title: 'Costume System',
            description: 'Equip costumes and craft epic versions for additional stat bonuses and visual customization.',
            links: [
                { text: 'Costume Guide', url: '#' },
                { text: 'Epic Crafting', url: '#' }
            ]
        },

        'gold-merit': {
            title: 'Gold Merit System',
            description: 'Earn and spend gold merit points to unlock permanent character improvements.',
            links: [
                { text: 'Merit Guide', url: '#' }
            ]
        },

        'platinum-merit': {
            title: 'Platinum Merit System',
            description: 'Use premium platinum merit points for exclusive high-tier character enhancements.',
            links: [
                { text: 'Platinum Guide', url: '#' }
            ]
        },

        'force-wing': {
            title: 'Force Wing System',
            description: 'Upgrade your force wings through multiple tiers to gain powerful stat bonuses and visual effects.',
            links: [
                { text: 'Force Wing Guide', url: '#' },
                { text: 'Upgrade Tiers', url: '#' }
            ]
        },

        'essence-runes': {
            title: 'Essence Runes',
            description: 'Socket essence runes into available slots and level them up for stat bonuses.',
            links: [
                { text: 'Rune Guide', url: '#' },
                { text: 'Rune Combinations', url: '#' }
            ]
        },

        'karma-runes': {
            title: 'Karma Runes',
            description: 'Equip karma runes for additional stat bonuses. Similar to essence runes but with different effects.',
            links: [
                { text: 'Karma Guide', url: '#' }
            ]
        },

        'overlord-mastery': {
            title: 'Overlord Mastery',
            description: 'Master overlord skills in attack and defense trees. Requires level 200+ to access.',
            links: [
                { text: 'Mastery Guide', url: '#' },
                { text: 'Skill Trees', url: '#' }
            ]
        },

        'achievement': {
            title: 'Achievement System',
            description: 'Complete various achievements to earn permanent stat bonuses and unlock new content.',
            links: [
                { text: 'Achievement Guide', url: '#' },
                { text: 'Milestone Rewards', url: '#' }
            ]
        }
    },

    /**
     * Get configuration for a specific system
     */
    getConfig: function(systemId) {
        return this.headers[systemId] || null;
    },

    /**
     * Update configuration for a system
     */
    updateConfig: function(systemId, config) {
        if (this.headers[systemId]) {
            this.headers[systemId] = { ...this.headers[systemId], ...config };
        } else {
            this.headers[systemId] = config;
        }
    },

    /**
     * Add a new system configuration
     */
    addSystem: function(systemId, config) {
        this.headers[systemId] = config;
    },

    /**
     * Get all system IDs that have headers configured
     */
    getConfiguredSystems: function() {
        return Object.keys(this.headers);
    }
};

// Create global instance
window.SystemHeaderManager = new SystemHeaderManager();

// Auto-initialize when DOM is ready
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
        window.SystemHeaderManager.init();
    });
} else {
    window.SystemHeaderManager.init();
}
