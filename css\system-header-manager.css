/**
 * System Header Manager Styles
 * Uniform styling for all system headers
 */

.fg-system-header-container {
    background: #272a2e;
    border: 1px solid #3c3f44;
    border-radius: 5px;
    margin-bottom: 20px;
    padding: 20px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}

.fg-header-content {
    text-align: center;
}

.fg-header-title {
    color: #e0e0e0;
    font-size: 1.5rem;
    font-weight: bold;
    margin: 0 0 10px 0;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
}

.fg-header-description {
    color: #b0b0b0;
    font-size: 0.95rem;
    line-height: 1.4;
    margin: 0 0 15px 0;
    max-width: 800px;
    margin-left: auto;
    margin-right: auto;
}

.fg-header-links {
    display: flex;
    justify-content: center;
    gap: 15px;
    flex-wrap: wrap;
}

.fg-header-link {
    color: #66bb6a;
    text-decoration: none;
    font-size: 0.9rem;
    padding: 5px 12px;
    border: 1px solid #66bb6a;
    border-radius: 3px;
    transition: all 0.2s ease;
    background: rgba(102, 187, 106, 0.1);
}

.fg-header-link:hover {
    background: rgba(102, 187, 106, 0.2);
    color: #81c784;
    border-color: #81c784;
    text-decoration: none;
}

.fg-header-link:active {
    transform: translateY(1px);
}

/* Mobile responsiveness */
@media (max-width: 768px) {
    .fg-system-header-container {
        padding: 15px;
        margin-bottom: 15px;
    }
    
    .fg-header-title {
        font-size: 1.3rem;
    }
    
    .fg-header-description {
        font-size: 0.9rem;
    }
    
    .fg-header-links {
        gap: 10px;
    }
    
    .fg-header-link {
        font-size: 0.85rem;
        padding: 4px 10px;
    }
}

/* Dark theme compatibility */
@media (prefers-color-scheme: dark) {
    .fg-system-header-container {
        background: #1c1e22;
        border-color: #3c3f44;
    }
}
